{"Something went wrong": "Something went wrong", "Language deleted successfully": "Language deleted successfully", "language term updated successfully": "language term updated successfully", "Operation successful": "Operation successful", "Language default updated successfully": "Language default updated successfully", "Language status changed successfully": "Language status changed successfully", "Branch changed successfully": "Branch changed successfully", "Something went wrong!": "Something went wrong!", "User Permission update successfully": "User Permission update successfully", "User update successfully": "User update successfully", "Leave request created successfully": "Leave request created successfully", "Good Morning": "Good Morning", "Have a good day with full of productivity and good vibes!": "Have a good day with full of productivity and good vibes!", "The employee field is required": "The employee field is required", "The selected employee does not exist": "The selected employee does not exist", "The start date is required": "The start date is required", "The start date is not a valid date": "The start date is not a valid date", "The start date must be before or equal to the end date": "The start date must be before or equal to the end date", "The end date is required": "The end date is required", "The end date is not a valid date": "The end date is not a valid date", "The end date must be after or equal to the start date": "The end date must be after or equal to the start date", "The starting location must be a string": "The starting location must be a string", "The starting location may not be greater than 255 characters": "The starting location may not be greater than 255 characters", "The destination must be a string": "The destination must be a string", "The destination may not be greater than 255 characters": "The destination may not be greater than 255 characters", "The purpose of travel must be a string": "The purpose of travel must be a string", "The travel amount must be a number": "The travel amount must be a number", "The travel amount must be at least 0": "The travel amount must be at least 0", "Travel Plan has been created successfully": "Travel Plan has been created successfully", "Travel Plan has been updated successfully": "Travel Plan has been updated successfully", "Travel Plan has been deleted successfully": "Travel Plan has been deleted successfully", "The signature field is required": "The signature field is required", "The signature date field is required": "The signature date field is required", "The signature date is not a valid date": "The signature date is not a valid date", "Travel Plan has been approved successfully": "Travel Plan has been approved successfully", "Travel Plan has been rejected successfully": "Travel Plan has been rejected successfully", "The starting location is required": "The starting location is required", "The destination is required": "The destination is required", "The purpose is required": "The purpose is required", "The amount is required": "The amount is required", "Client created successfully": "Client created successfully", "The date field is required": "The date field is required", "The date is not a valid date": "The date is not a valid date", "The schedule time field is required": "The schedule time field is required", "The schedule time format is invalid": "The schedule time format is invalid", "The start time field is required": "The start time field is required", "The start time format is invalid": "The start time format is invalid", "The start time must be before the end time": "The start time must be before the end time", "The end time field is required": "The end time field is required", "The end time format is invalid": "The end time format is invalid", "The end time must be after the start time": "The end time must be after the start time", "The company name field is required": "The company name field is required", "The company name may not be greater than 255 characters": "The company name may not be greater than 255 characters", "The customer name field is required": "The customer name field is required", "The customer name may not be greater than 255 characters": "The customer name may not be greater than 255 characters", "The email field is required": "The email field is required", "The email must be a valid email address": "The email must be a valid email address", "The phone field is required": "The phone field is required", "The phone number may not be greater than 15 characters": "The phone number may not be greater than 15 characters", "The purpose field is required": "The purpose field is required", "The file must be a type of: jpg, jpeg, png, pdf, doc, docx": "The file must be a type of: jpg, jpeg, png, pdf, doc, docx", "The file may not be greater than 2MB": "The file may not be greater than 2MB", "The rating must be a number between 1 and 5": "The rating must be a number between 1 and 5", "The rating must be at least 1": "The rating must be at least 1", "The rating may not be greater than 5": "The rating may not be greater than 5", "Travel Meeting has been created successfully": "Travel Meeting has been created successfully", "Travel Meeting has been updated successfully": "Travel Meeting has been updated successfully", "The phone number may not be greater than 50 characters": "The phone number may not be greater than 50 characters", "Travel Meeting has been deleted successfully": "Travel Meeting has been deleted successfully", "The date is required": "The date is required", "Please provide a valid date": "Please provide a valid date", "Travel type is required": "Travel type is required", "The selected travel type is invalid": "The selected travel type is invalid", "Amount must be a valid number": "Amount must be a valid number", "Amount cannot be negative": "Amount cannot be negative", "Please upload a valid file": "Please upload a valid file", "Supported file types are jpg, jpeg, png, pdf, doc, docx": "Supported file types are jpg, jpeg, png, pdf, doc, docx", "Remark must be a string": "Remark must be a string", "Rating must be between 1 and 5": "Rating must be between 1 and 5", "Mode of transportation is required": "Mode of transportation is required", "Selected mode of transportation is invalid": "Selected mode of transportation is invalid", "Travel Expense has been created successfully": "Travel Expense has been created successfully", "Travel Expense has been updated successfully": "Travel Expense has been updated successfully", "Travel Expense has been deleted successfully": "Travel Expense has been deleted successfully", "Travel Expense has been approved successfully": "Travel Expense has been approved successfully", "The date must be a valid date": "The date must be a valid date", "Start time is required": "Start time is required", "Start time must be a valid date": "Start time must be a valid date", "Calls made are required": "Calls made are required", "Calls made must be a number": "Calls made must be a number", "Positive leads are required": "Positive leads are required", "Positive leads must be a number": "Positive leads must be a number", "Total sales are required": "Total sales are required", "Total sales must be a string": "Total sales must be a string", "Please select an option for pending leads update": "Please select an option for pending leads update", "Please provide additional details for \"Other\" option": "Please provide additional details for \"Other\" option", "Please select an option for recovery work today": "Please select an option for recovery work today", "Daily report summary is required": "Daily report summary is required", "Daily report summary must be at least 10 characters": "Daily report summary must be at least 10 characters", "Complaints/questions/comments must be a string": "Complaints/questions/comments must be a string", "The file must be a valid file": "The file must be a valid file", "The file must be of type: jpeg, png, jpg, pdf, docx": "The file must be of type: jpeg, png, jpg, pdf, docx", "The file size may not exceed 2MB": "The file size may not exceed 2MB", "Please rate your day in terms of production": "Please rate your day in terms of production", "The rating must be an integer": "The rating must be an integer", "The rating must be between 1 and 10": "The rating must be between 1 and 10", "Travel Workflow has been created successfully": "Travel Workflow has been created successfully", "Travel Workflow has been updated successfully": "Travel Workflow has been updated successfully", "Travel Workflow has been deleted successfully": "Travel Workflow has been deleted successfully", "The :attribute must be a valid Base64-encoded image": "The :attribute must be a valid Base64-encoded image", "The travel plan field is required": "The travel plan field is required", "The travel plan ID must be a valid number": "The travel plan ID must be a valid number", "Validation Error": "Validation Error", "The file ID must be a valid number": "The file ID must be a valid number", "The selected file ID is invalid or does not exist": "The selected file ID is invalid or does not exist", "The expense category field is required": "The expense category field is required", "The expense category ID must be a valid number": "The expense category ID must be a valid number", "Each expense category field is required": "Each expense category field is required", "Each expense category must be a valid number": "Each expense category must be a valid number", "Each amount must be a valid number": "Each amount must be a valid number", "The amount field is required": "The amount field is required", "Profile updated successfully": "Profile updated successfully", "Good Day": "Good Day", "Best wishes for your day!": "Best wishes for your day!", "Support ticket created successfully": "Support ticket created successfully", "Appointment created successfully": "Appointment created successfully", "Daily leave created successfully": "Daily leave created successfully", "Updated successful": "Updated successful", "Operation Done Succesfully": "Operation Done Succesfully", "You can not delete this Plan": "You can not delete this Plan", "Leave Request Referred": "Leave Request Referred", "Notice updated successfully": "Notice updated successfully", "Notice deleted successfully": "Notice deleted successfully", "Edit Client Information": "Edit Client Information", "Client updated successfully": "Client updated successfully", "Client deleted successfully": "Client deleted successfully", "You are not allowed to perform the delete action in demo mode": "You are not allowed to perform the delete action in demo mode", "Support ticket replied successfully": "Support ticket replied successfully", "Support ticket updated successfully": "Support ticket updated successfully", "Support ticket deleted successfully": "Support ticket deleted successfully", "Contact added successfully": "Contact added successfully", "The accepted format is HH:mm or HH:mm:ss": "The accepted format is HH:mm or HH:mm:ss", "Tardy rule created successfully": "Tardy rule created successfully", "Tardy rule updated successfully": "Tardy rule updated successfully", "Tardy record created successfully": "Tardy record created successfully", "Tardy request created successfully": "Tardy request created successfully", "Tardy request updated successfully": "Tardy request updated successfully", "Tardy request deleted successfully": "Tardy request deleted successfully", "Tardy record deleted successfully": "Tardy record deleted successfully", "Tardy appeal rejected successfully": "Tardy appeal rejected successfully", "Tardy appeal approved successfully": "Tardy appeal approved successfully", "Tardy appeal created successfully": "Tardy appeal created successfully", "Tardy appeal updated successfully": "Tardy appeal updated successfully", "Tardy appeal deleted successfully": "Tardy appeal deleted successfully", "Tardy rule deleted successfully": "Tardy rule deleted successfully", "Pricing Plan has been updated successfully!": "Pricing Plan has been updated successfully!", "Good Afternoon": "Good Afternoon", "You almost done for today": "You almost done for today", "Good Evening": "Good Evening", "Thank you for your hard work today": "Thank you for your hard work today", "Tardy group created successfully": "Tardy group created successfully", "Tardy group updated successfully": "Tardy group updated successfully", "Tardy group deleted successfully": "Tardy group deleted successfully", "Tardy group is required": "Tardy group is required", "Tardy rule assign created successfully": "Tardy rule assign created successfully", "Role Changed Successfully": "Role Changed Successfully", "Appointment updated successfully": "Appointment updated successfully", "Qr Attendance Not Setup": "Qr Attendance Not Setup", "Please click on button!": "Please click on button!"}