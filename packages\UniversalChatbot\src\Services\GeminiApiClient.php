<?php

namespace UniversalChatbot\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class GeminiApiClient
{
    protected string $apiKey;
    protected string $baseUrl = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent';

    public function __construct()
    {
        $this->apiKey = config('universal_chatbot.api_keys.gemini');
    }

    /**
     * Make a request to the Gemini API
     *
     * @param string $prompt The prompt to send
     * @param array $options Configuration options
     * @return array API response
     */
    public function generateContent(string $prompt, array $options = []): array
    {
        $defaultOptions = [
            'temperature' => 0.3,
            'maxOutputTokens' => 1024,
            'topP' => 0.95,
            'topK' => 40,
        ];

        $config = array_merge($defaultOptions, $options);

        $response = Http::withHeaders([
            'Content-Type' => 'application/json',
        ])->post("{$this->baseUrl}?key={$this->apiKey}", [
            'contents' => [
                [
                    'role' => 'user',
                    'parts' => [
                        ['text' => $prompt],
                    ],
                ],
            ],
            'generationConfig' => $config,
        ]);

        if ($response->failed()) {
            Log::error('Gemini API request failed: ' . $response->body());
            return [
                'success' => false,
                'error' => 'API request failed',
                'response' => null,
            ];
        }

        return [
            'success' => true,
            'error' => null,
            'response' => $response->json(),
        ];
    }

    /**
     * Generate content for question analysis with low temperature
     *
     * @param string $prompt Analysis prompt
     * @return array API response
     */
    public function analyzeQuestion(string $prompt): array
    {
        return $this->generateContent($prompt, [
            'temperature' => 0.1, // Low temperature for more deterministic output
            'maxOutputTokens' => 1024,
        ]);
    }

    /**
     * Generate content for response formatting
     *
     * @param string $prompt Formatting prompt
     * @return array API response
     */
    public function formatResponse(string $prompt): array
    {
        return $this->generateContent($prompt, [
            'temperature' => 0.3,
            'maxOutputTokens' => 1024,
        ]);
    }

    /**
     * Generate context-based response
     *
     * @param string $prompt Context-based prompt
     * @return array API response
     */
    public function generateContextResponse(string $prompt): array
    {
        return $this->generateContent($prompt, [
            'temperature' => 0.3,
            'maxOutputTokens' => 1024,
        ]);
    }

    /**
     * Extract text content from API response
     *
     * @param array $response API response
     * @return string|null Extracted text or null if not found
     */
    public function extractTextFromResponse(array $response): ?string
    {
        if (!isset($response['candidates'][0]['content']['parts'][0]['text'])) {
            return null;
        }

        return $response['candidates'][0]['content']['parts'][0]['text'];
    }
}