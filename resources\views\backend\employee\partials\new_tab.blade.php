<!-- profile menu mobile start -->
<div class="profile-content">
    <div class="profile-menu-mobile">
        <button class="btn-menu-mobile" type="button" data-bs-toggle="offcanvas"
            data-bs-target="#offcanvasWithBothOptionsMenuMobile" aria-controls="offcanvasWithBothOptionsMenuMobile">
            <span class="icon"><i class="fa-solid fa-bars"></i></span>
        </button>

        <div class="offcanvas offcanvas-start" data-bs-scroll="true" tabindex="-1"
            id="offcanvasWithBothOptionsMenuMobile">
            <div class="offcanvas-header">
                <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close">
                    <span class="icon"><i class="fa-solid fa-xmark"></i></span>
                </button>
            </div>
            <div class="offcanvas-body">
                <!-- profile menu start -->
                <div class="profile-menu">
                    <!-- profile menu head start -->
                    <div class="profile-menu-head">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <img class="img-fluid rounded-circle"
                                    src="{{ @$data['show']->original['data']['avatar'] }}" width="60"
                                    alt="profile image" />
                            </div>
                            <div class="flex-grow-1">
                                <div class="body">
                                    <h2 class="title">{{ @$data['name'] }}</h2>
                                    <p class="paragraph">{{ @$data['name'] }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- profile menu head end -->

                    <!-- profile menu body start -->
                    <div class="profile-menu-body">
                        <nav>
                            <ul class="nav flex-column">
                                <li class="nav-item dropdown">
                                    <a class="nav-link {{ menu_active_by_url(route('user.profile', [$data['id'], 'personal'])) }}"
                                        href="{{ route('user.profile', [$data['id'], 'personal']) }}">
                                        <x-common.icons name="profile-edit" class="text-title" size="16"
                                            stroke-width="1.5" />
                                        {{ _trans('common.Profile') }}
                                    </a>
                                </li>
                                @if (hasPermission('contract_profile'))
                                <li class="nav-item">
                                    <a class="nav-link  {{ menu_active_by_url(route('user.profile', [$data['id'], 'contract'])) }}"
                                        href="{{ route('user.profile', [$data['id'], 'contract']) }}">
                                        {{ _trans('profile.Contract') }}
                                    </a>
                                </li>
                                @endif
                                @if (hasPermission('attendance_profile'))
                                <li class="nav-item dropdown">
                                    <a class="nav-link  {{ menu_active_by_url(route('user.profile', [$data['id'], 'attendance'])) }}"
                                        href="{{ route('user.profile', [$data['id'], 'attendance']) }}">
                                        {{ _trans('attendance.Attendance') }}
                                    </a>
                                </li>
                                @endif
                                @if (hasPermission('notice_profile'))
                                <li class="nav-item">
                                    <a class="nav-link  {{ menu_active_by_url(route('user.profile', [$data['id'], 'notice'])) }}"
                                        href="{{ route('user.profile', [$data['id'], 'notice']) }}">
                                        {{ _trans('common.Notices') }}
                                    </a>
                                </li>
                                @endif
                                @if (hasPermission('leave_request_profile'))
                                <li class="nav-item">
                                    <a class="nav-link  {{ menu_active_by_url(route('user.profile', [$data['id'], 'leave_request'])) }}"
                                        href="{{ route('user.profile', [$data['id'], 'leave_request']) }}">
                                        {{ _trans('common.Leaves') }}
                                    </a>
                                </li>
                                @endif
                                @if (hasPermission('visit_profile'))
                                <li class="nav-item">
                                    <a class="nav-link  {{ menu_active_by_url(route('user.profile', [$data['id'], 'visit'])) }}"
                                        href="{{ route('user.profile', [$data['id'], 'visit']) }}">
                                        {{ _trans('common.Visit') }}
                                    </a>
                                </li>
                                @endif
                                @if (hasPermission('phonebook_profile'))
                                <li class="nav-item">
                                    <a class="nav-link  {{ menu_active_by_url(route('user.profile', [$data['id'], 'phonebook'])) }}"
                                        href="{{ route('user.profile', [$data['id'], 'phonebook']) }}">
                                        {{ _trans('common.Phonebook') }}
                                    </a>
                                </li>
                                @endif
                                @if (hasPermission('appointment_profile'))
                                <li class="nav-item">
                                    <a class="nav-link  {{ menu_active_by_url(route('user.profile', [$data['id'], 'appointment'])) }}"
                                        href="{{ route('user.profile', [$data['id'], 'appointment']) }}">
                                        {{ _trans('appointment.Appointment') }}
                                    </a>
                                </li>
                                @endif
                                @if (hasPermission('support_ticket_profile'))
                                <li class="nav-item">
                                    <a class="nav-link  {{ menu_active_by_url(route('user.profile', [$data['id'], 'ticket'])) }}"
                                        href="{{ route('user.profile', [$data['id'], 'ticket']) }}">
                                        {{ _trans('common.Support') }}
                                    </a>
                                </li>
                                @endif
                                @if (hasPermission('advance_profile'))
                                <li class="nav-item">
                                    <a class="nav-link  {{ menu_active_by_url(route('user.profile', [$data['id'], 'advance'])) }}"
                                        href="{{ route('user.profile', [$data['id'], 'advance']) }}">
                                        {{ _trans('payroll.Advance') }}
                                    </a>
                                </li>
                                @endif
                                @if (hasPermission('commission_profile'))
                                <li class="nav-item">
                                    <a class="nav-link  {{ menu_active_by_url(route('user.profile', [$data['id'], 'commission'])) }}"
                                        href="{{ route('user.profile', [$data['id'], 'commission']) }}">
                                        {{ _trans('payroll.Commission') }}
                                    </a>
                                </li>
                                @endif
                                @if (hasPermission('salary_profile'))
                                <li class="nav-item">
                                    <a class="nav-link  {{ menu_active_by_url(route('user.profile', [$data['id'], 'salary'])) }}"
                                        href="{{ route('user.profile', [$data['id'], 'salary']) }}">
                                        {{ _trans('payroll.Salary') }}
                                    </a>
                                </li>
                                @endif
                                @if (hasPermission('project_profile'))
                                <li class="nav-item">
                                    <a class="nav-link  {{ menu_active_by_url(route('user.profile', [$data['id'], 'project'])) }}"
                                        href="{{ route('user.profile', [$data['id'], 'project']) }}">
                                        {{ _trans('project.Projects') }}
                                    </a>
                                </li>
                                @endif
                                @if (hasPermission('task_profile'))
                                <li class="nav-item">
                                    <a class="nav-link  {{ menu_active_by_url(route('user.profile', [$data['id'], 'task'])) }}"
                                        href="{{ route('user.profile', [$data['id'], 'task']) }}">
                                        {{ _trans('project.Tasks') }}
                                    </a>
                                </li>
                                @endif
                                @if (hasPermission('award_profile'))
                                <li class="nav-item">
                                    <a class="nav-link  {{ menu_active_by_url(route('user.profile', [$data['id'], 'award'])) }}"
                                        href="{{ route('user.profile', [$data['id'], 'award']) }}">
                                        {{ _trans('award.Awards') }}
                                    </a>
                                </li>
                                @endif
                                @if (hasPermission('travel_profile'))
                                <li class="nav-item">
                                    <a class="nav-link  {{ menu_active_by_url(route('user.profile', [$data['id'], 'travel'])) }}"
                                        href="{{ route('user.profile', [$data['id'], 'travel']) }}">
                                        {{ _trans('travel.Travels') }}
                                    </a>
                                </li>
                                @endif
                                <li class="nav-item">
                                    <a class="nav-link" target="_blank"
                                        href="{{ route('pages.content', ['company-policies']) }}">
                                        {{ _trans('travel.Company Policy') }}
                                    </a>
                                </li>
                            </ul>
                        </nav>
                    </div>
                    <!-- profile menu body end -->
                </div>
                <!-- profile menu end -->
            </div>
        </div>
    </div>
</div>

<!-- profile menu mobile end -->
<div class="new-profile-content">
    <div class="profile-menu">


        {{-- Profile Card --}}
        <div class="profile-card ot-card flex-fill">
            <div class="profile-menu mb-20">
                <div class="profile-menu-item d-flex align-items-center gap-10">
                    <div class="user-profile-photo">
                        <img class="img-fluid rounded-circle w-100 h-100"
                            src="{{ @$data['show']->original['data']['avatar'] }}" width="60" alt="profile image" />
                    </div>
                    <div class="contents">
                        <h2 class="title fw-semibold text-20 pb-6">Arlene McCoy</h2>
                        <p class="paragraph m-0">{{ @$data['name'] }}</p>
                    </div>
                </div>
                <div class="profile-menu-item d-flex align-items-center gap-10">
                    <div class="contents">
                        <p class="paragraph">Employee ID</p>
                        <h6 class="title fw-semibold">5104</h6>
                    </div>
                </div>
                <div class="profile-menu-item d-flex align-items-center gap-10">
                    <div class="contents">
                        <p class="paragraph">Role</p>
                        <h6 class="title fw-semibold">{{ @$data['name'] }}</h6>
                    </div>
                </div>
                <div class="profile-menu-item d-flex align-items-center gap-10">
                    <div class="contents">
                        <p class="paragraph">Phone Number</p>
                        <h6 class="title fw-semibold">+993652487</h6>
                    </div>
                </div>
            </div>

            {{-- User Related info --}}
            <div class="user-related-info-wrapper border radius-8">
                <ul class="tab-style-2 nav nav-pills mb-3 p-3" id="pills-tab" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="one-tab" data-bs-toggle="pill" data-bs-target="#one"
                            type="button" role="tab" aria-controls="one" aria-selected="true">
                            <div class="icon icon-size-20 text-subtitle">
                                <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24"
                                    fill="none">
                                    <path d="M2 8.5h11.5M6 16.5h2M10.5 16.5h4" stroke="currentColor" stroke-width="1.5"
                                        stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                                    <path
                                        d="M22 11.03v5.08c0 3.51-.89 4.39-4.44 4.39H6.44C2.89 20.5 2 19.62 2 16.11V7.89c0-3.51.89-4.39 4.44-4.39h7.06"
                                        stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
                                        stroke-linejoin="round">
                                    </path>
                                    <path d="M16.5 6 18 7.5l4-4" stroke="currentColor" stroke-width="1.5"
                                        stroke-linecap="round" stroke-linejoin="round">
                                    </path>
                                </svg>
                            </div>
                            <span> Attendance</span>
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="two-tab" data-bs-toggle="pill" data-bs-target="#two" type="button"
                            role="tab" aria-controls="two" aria-selected="false">
                            <div class="icon icon-size-20 text-subtitle">
                                <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24"
                                    fill="none">
                                    <path d="M22 12c0 5.52-4.48 10-10 10S2 17.52 2 12 6.48 2 12 2s10 4.48 10 10Z"
                                        stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
                                        stroke-linejoin="round">
                                    </path>
                                    <path d="m15.71 15.18-3.1-1.85c-.54-.32-.98-1.09-.98-1.72v-4.1"
                                        stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
                                        stroke-linejoin="round"></path>
                                </svg>
                            </div>
                            <span> Time Log</span>
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="three-tab" data-bs-toggle="pill" data-bs-target="#three"
                            type="button" role="tab" aria-controls="three" aria-selected="false">
                            <div class="icon icon-size-20 text-subtitle">
                                <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24"
                                    fill="none">
                                    <path
                                        d="M6.88 18.15v-2.07M12 18.15v-4.14M17.12 18.15v-6.22M17.12 5.85l-.46.54a18.882 18.882 0 0 1-9.78 6.04"
                                        stroke="currentColor" stroke-width="1.5" stroke-linecap="round"></path>
                                    <path d="M14.19 5.85h2.93v2.92" stroke="currentColor" stroke-width="1.5"
                                        stroke-linecap="round" stroke-linejoin="round">
                                    </path>
                                    <path d="M9 22h6c5 0 7-2 7-7V9c0-5-2-7-7-7H9C4 2 2 4 2 9v6c0 5 2 7 7 7Z"
                                        stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
                                        stroke-linejoin="round"></path>
                                </svg>
                            </div>
                            <span> This Month</span>
                        </button>
                    </li>
                </ul>
                <div class="tab-content" id="pills-tabContent">
                    <div class="tab-pane fade show active" id="one" role="tabpanel" aria-labelledby="one-tab">

                        {{-- Tab Contents --}}
                        <div
                            class="user-related-info p-3 d-flex align-items-center justify-content-between flex-wrap gap-20">

                            <div class="user-related-info-item d-flex align-items-center gap-10">
                                <div class="contents">
                                    <p class="paragraph mb-6 text-title">Total Leaves</p>
                                    <h6 class="title fw-semibold mb-0">0 Days</h6>
                                </div>
                            </div>
                            <span class="line-style"></span>
                            <div class="user-related-info-item d-flex align-items-center gap-10">
                                <div class="contents">
                                    <p class="paragraph mb-6 text-title">Day Off</p>
                                    <h6 class="title fw-semibold mb-0">10</h6>
                                </div>
                            </div>
                            <span class="line-style"></span>
                            <div class="user-related-info-item d-flex align-items-center gap-10">
                                <div class="contents">
                                    <p class="paragraph mb-6 text-title">Worked Time</p>
                                    <h6 class="title fw-semibold mb-0">10 Hours</h6>
                                </div>
                            </div>
                            <span class="line-style"></span>
                            <div class="user-related-info-item d-flex align-items-center gap-10">
                                <div class="contents">
                                    <p class="paragraph mb-6 text-title">Over Time</p>
                                    <h6 class="title fw-semibold mb-0">03 Hours</h6>
                                </div>
                            </div>
                            <span class="line-style"></span>
                            <div class="user-related-info-item d-flex align-items-center gap-10">
                                <div class="contents">
                                    <p class="paragraph mb-6 text-title">Late In</p>
                                    <h6 class="title fw-semibold mb-0">12 Days</h6>
                                </div>
                            </div>
                            <span class="line-style"></span>
                            <div class="user-related-info-item d-flex align-items-center gap-10">
                                <div class="contents">
                                    <p class="paragraph mb-6 text-title">Late Out</p>
                                    <h6 class="title fw-semibold mb-0">05 Days</h6>
                                </div>
                            </div>
                            <span class="line-style"></span>
                            <div class="user-related-info-item d-flex align-items-center gap-10">
                                <div class="contents">
                                    <p class="paragraph mb-6 text-title">Break Time</p>
                                    <h6 class="title fw-semibold mb-0">2 Hour</h6>
                                </div>
                            </div>
                            <span class="line-style"></span>
                            <div class="user-related-info-item d-flex align-items-center gap-10">
                                <div class="contents">
                                    <p class="paragraph mb-6 text-title">Late Time</p>
                                    <h6 class="title fw-semibold mb-0">1 Hour</h6>
                                </div>
                            </div>

                        </div>
                        {{-- / --}}

                    </div>
                    <div class="tab-pane fade" id="two" role="tabpanel" aria-labelledby="two-tab">...
                    </div>
                    <div class="tab-pane fade" id="three" role="tabpanel" aria-labelledby="three-tab">...
                    </div>
                </div>

            </div>

        </div>
        {{-- / Profile Card --}}


        <!-- profile menu body start -->
        <div class="ot-card mb-16">
            <nav>
                <ul class="nav nav-pills">
                    <li class="nav-item dropdown">
                        <a class="nav-link {{ menu_active_by_url(route('user.profile', [$data['id'], 'personal'])) }}"
                            href="{{ route('user.profile', [$data['id'], 'personal']) }}">
                            {{ _trans('common.Profile') }}
                        </a>
                    </li>
                    @if (hasPermission('contract_profile'))
                    <li class="nav-item">
                        <a class="nav-link  {{ menu_active_by_url(route('user.profile', [$data['id'], 'contract'])) }}"
                            href="{{ route('user.profile', [$data['id'], 'contract']) }}">
                            {{ _trans('profile.Contract') }}
                        </a>
                    </li>
                    @endif
                    @if (hasPermission('attendance_profile'))
                    <li class="nav-item dropdown">
                        <a class="nav-link  {{ menu_active_by_url(route('user.profile', [$data['id'], 'attendance'])) }}"
                            href="{{ route('user.profile', [$data['id'], 'attendance']) }}">
                            {{ _trans('attendance.Attendance') }}
                        </a>
                    </li>
                    @endif
                    @if (hasPermission('notice_profile'))
                    <li class="nav-item">
                        <a class="nav-link  {{ menu_active_by_url(route('user.profile', [$data['id'], 'notice'])) }}"
                            href="{{ route('user.profile', [$data['id'], 'notice']) }}">
                            {{ _trans('common.Notices') }}
                        </a>
                    </li>
                    @endif
                    @if (hasPermission('leave_request_profile'))
                    <li class="nav-item">
                        <a class="nav-link  {{ menu_active_by_url(route('user.profile', [$data['id'], 'leave_request'])) }}"
                            href="{{ route('user.profile', [$data['id'], 'leave_request']) }}">
                            {{ _trans('common.Leaves') }}
                        </a>
                    </li>
                    @endif
                    @if (hasPermission('visit_profile'))
                    <li class="nav-item">
                        <a class="nav-link  {{ menu_active_by_url(route('user.profile', [$data['id'], 'visit'])) }}"
                            href="{{ route('user.profile', [$data['id'], 'visit']) }}">
                            {{ _trans('common.Visit') }}
                        </a>
                    </li>
                    @endif
                    @if (hasPermission('phonebook_profile'))
                    <li class="nav-item">
                        <a class="nav-link  {{ menu_active_by_url(route('user.profile', [$data['id'], 'phonebook'])) }}"
                            href="{{ route('user.profile', [$data['id'], 'phonebook']) }}">
                            {{ _trans('common.Phonebook') }}
                        </a>
                    </li>
                    @endif
                    @if (hasPermission('appointment_profile'))
                    <li class="nav-item">
                        <a class="nav-link  {{ menu_active_by_url(route('user.profile', [$data['id'], 'appointment'])) }}"
                            href="{{ route('user.profile', [$data['id'], 'appointment']) }}">
                            {{ _trans('appointment.Appointment') }}
                        </a>
                    </li>
                    @endif
                    @if (hasPermission('support_ticket_profile'))
                    <li class="nav-item">
                        <a class="nav-link  {{ menu_active_by_url(route('user.profile', [$data['id'], 'ticket'])) }}"
                            href="{{ route('user.profile', [$data['id'], 'ticket']) }}">
                            {{ _trans('common.Support') }}
                        </a>
                    </li>
                    @endif
                    @if (hasPermission('advance_profile'))
                    <li class="nav-item">
                        <a class="nav-link  {{ menu_active_by_url(route('user.profile', [$data['id'], 'advance'])) }}"
                            href="{{ route('user.profile', [$data['id'], 'advance']) }}">
                            {{ _trans('payroll.Advance') }}
                        </a>
                    </li>
                    @endif
                    @if (hasPermission('commission_profile'))
                    <li class="nav-item">
                        <a class="nav-link  {{ menu_active_by_url(route('user.profile', [$data['id'], 'commission'])) }}"
                            href="{{ route('user.profile', [$data['id'], 'commission']) }}">
                            {{ _trans('payroll.Commission') }}
                        </a>
                    </li>
                    @endif
                    @if (hasPermission('salary_profile'))
                    <li class="nav-item">
                        <a class="nav-link  {{ menu_active_by_url(route('user.profile', [$data['id'], 'salary'])) }}"
                            href="{{ route('user.profile', [$data['id'], 'salary']) }}">
                            {{ _trans('payroll.Salary') }}
                        </a>
                    </li>
                    @endif
                    @if (hasPermission('project_profile'))
                    <li class="nav-item">
                        <a class="nav-link  {{ menu_active_by_url(route('user.profile', [$data['id'], 'project'])) }}"
                            href="{{ route('user.profile', [$data['id'], 'project']) }}">
                            {{ _trans('project.Projects') }}
                        </a>
                    </li>
                    @endif
                    @if (hasPermission('task_profile'))
                    <li class="nav-item">
                        <a class="nav-link  {{ menu_active_by_url(route('user.profile', [$data['id'], 'task'])) }}"
                            href="{{ route('user.profile', [$data['id'], 'task']) }}">
                            {{ _trans('project.Tasks') }}
                        </a>
                    </li>
                    @endif
                    @if (hasPermission('award_profile'))
                    <li class="nav-item">
                        <a class="nav-link  {{ menu_active_by_url(route('user.profile', [$data['id'], 'award'])) }}"
                            href="{{ route('user.profile', [$data['id'], 'award']) }}">
                            {{ _trans('award.Awards') }}
                        </a>
                    </li>
                    @endif
                    @if (hasPermission('travel_profile'))
                    <li class="nav-item">
                        <a class="nav-link  {{ menu_active_by_url(route('user.profile', [$data['id'], 'travel'])) }}"
                            href="{{ route('user.profile', [$data['id'], 'travel']) }}">
                            {{ _trans('travel.Travels') }}
                        </a>
                    </li>
                    @endif
                    <li class="nav-item">
                        <a class="nav-link" target="_blank" href="{{ route('pages.content', ['company-policies']) }}">
                            {{ _trans('travel.Company Policy') }}
                        </a>
                    </li>
                    {{-- <li class="nav-item">
                        <a class="nav-link  {{ menu_active_by_url(route('user.profile', [$data['id'], 'request'])) }}"
                            href="{{ route('user.profile', [$data['id'], 'request']) }}">
                            {{ _trans('request.Requests') }}
                        </a>
                    </li> --}}
                </ul>
            </nav>
        </div>
        <!-- profile menu body end -->
    </div>
</div>