{"Configuration": "Configuration", "Date and time setting": "Date and time setting", "TimeZone": "TimeZone", "Date format": "Date format", "Language setting": "Language setting", "Select Language": "Select Language", "Language": "Language", "Attendance setting": "Attendance setting", "Attendance Method": "Attendance Method", "Max Work Hours": "Max Work Hours", "Live tracking setting": "Live tracking setting", "App sync time": "App sync time", "Live data store after": "Live data store after", "Live data store time": "Live data store time", "Api Key": "Api Key", "Google Map Key": "Google Map Key", "Google Firebase Key": "Google Firebase Key", "Employee": "Employee", "Employee Passport Required": "Employee Passport Required", "Yes": "Yes", "No": "No", "Employee EID Required": "Employee EID Required", "Currency setting": "Currency setting", "Currency": "<PERSON><PERSON><PERSON><PERSON>", "Settings updated successfully": "Settings updated successfully", "Settings": "Settings", "General": "General", "Email setup": "Email setup", "Firebase setup": "Firebase setup", "Payment Gateway": "Payment Gateway", "Storage setup": "Storage setup", "About System": "About System", "App Theme Setup": "App Theme Setup", "Type your company name": "Type your company name", "Background Image": "Background Image", "Recommended  size: 1920 x 1080 px)": "Recommended  size: 1920 x 1080 px)", "Logo": "Logo", "(Recommended size: 210 x 50 px)": "(Recommended size: 210 x 50 px)", "Company Logo Frontend": "Company Logo Frontend", "Company icon": "Company icon", "(Recommended size: 50 x 50 px)": "(Recommended size: 50 x 50 px)", "Company Icon": "Company Icon", "Website Footer": "Website Footer", "Type your company short description": "Type your company short description", "Android url": "Android url", "Android icon": "Android icon", "(Recommended size: 150 x 50 px)": "(Recommended size: 150 x 50 px)", "Ios url": "Ios url", "IOS icon": "IOS icon", "Email setup [SMTP]": "Email setup [SMTP]", "MAIL HOST": "MAIL HOST", "MAIL PORT": "MAIL PORT", "MAIL USERNAME": "MAIL USERNAME", "MAIL FROM ADDRESS": "MAIL FROM ADDRESS", "MAIL PASSWORD": "MAIL PASSWORD", "MAIL ENCRYPTION": "MAIL ENCRYPTION", "MAIL FROM NAME": "MAIL FROM NAME", "FIREBASE API KEY": "FIREBASE API KEY", "FIREBASE AUTH DOMAIN": "FIREBASE AUTH DOMAIN", "FIREBASE AUTH DATABASE URL": "FIREBASE AUTH DATABASE URL", "FIREBASE AUTH PROJECT ID": "FIREBASE AUTH PROJECT ID", "FIREBASE AUTH STORAGE BUCKET": "FIREBASE AUTH STORAGE BUCKET", "FIREBASE AUTH SENDER ID": "FIREBASE AUTH SENDER ID", "FIREBASE AUTH APP ID": "FIREBASE AUTH APP ID", "FIREBASE AUTH MEASUREMENT ID": "FIREBASE AUTH MEASUREMENT ID", "FIREBASE AUTH COLLECTION NAME": "FIREBASE AUTH COLLECTION NAME", "FIREBASE CREDENTIALS JSON": "FIREBASE CREDENTIALS JSON", "Geocoding setup": "Geocoding setup", "GEOCODING API KEY": "GEOCODING API KEY", "GEOCODING BASE URL": "GEOCODING BASE URL", "Pusher setup": "Pusher setup", "PUSHER APP ID": "PUSHER APP ID", "PUSHER APP KEY": "PUSHER APP KEY", "PUSHER APP SECRET": "PUSHER APP SECRET", "PUSHER APP CLUSTER": "PUSHER APP CLUSTER", "Stripe": "Stripe", "Stripe Key": "Stripe Key", "Stripe Secret": "Stripe Secret", "Enable Credit Card Payment": "Enable Credit Card Payment", "Enable Offline Payment": "Enable Offline Payment", "Enable Demo Checkout": "Enable Demo Checkout", "Enable Paystack Payment": "Enable Paystack Payment", "Paystack": "Paystack", "Paystack secret key": "Paystack secret key", "Paystack public key": "Paystack public key", "Paystack URL": "Paystack URL", "Paystack callback URL": "Paystack callback <PERSON><PERSON>", "Offline Payment Type": "Offline Payment Type", "Cash": "Cash", "Cheque": "Cheque", "Bank Transfer": "Bank Transfer", "Default storage": "Default storage", "Local": "Local", "S3": "S3", "AWS ACCESS KEY ID": "AWS ACCESS KEY ID", "AWS SECRET ACCESS KEY": "AWS SECRET ACCESS KEY", "AWS DEFAULT REGION": "AWS DEFAULT REGION", "AWS BUCKET": "AWS BUCKET", "AWS URL": "AWS URL", "AWS ENDPOINT": "AWS ENDPOINT", "AWS USE PATH STYLE ENDPOINT": "AWS USE PATH STYLE ENDPOINT", "Choose one theme": "Choose one theme", "Setup": "Setup", "Change Status": "Change Status", "Make Default": "Make Default", "Distance (Meters)": "Distance (Meters)", "Enter Distance": "Enter Distance", "Distance measure in meters": "Distance measure in meters", "Location": "Location", "Enter Location": "Enter Location", "Latitude": "Latitude", "Longitude": "Longitude", "Distance": "Distance", "User": "User", "Location Binding": "Location Binding", "Location Binding Create": "Location Binding Create", "Google Font app key": "Google Font app key", "Google Meet Credentials": "Google Meet Credentials", "Google Admin Email": "Google Admin Email"}