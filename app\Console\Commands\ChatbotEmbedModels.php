<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Modules\HRPolicies\Entities\HRPolicy;
use Modules\SystemGuidelines\Entities\SystemGuideline;
use UniversalChatbot\Services\UniversalEmbeddingService;

class ChatbotEmbedModels extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'chatbot:embed-models {--model= : Specific model to process (hrpolicies or systemguidelines)} {--id= : Process a specific record ID}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process embeddings for HRPolicies and SystemGuidelines models';

    /**
     * Execute the console command.
     */
    public function handle(UniversalEmbeddingService $embeddingService)
    {
        $model = $this->option('model');
        $id = $this->option('id');

        if (! $model) {
            // Process both models if none specified
            $this->processHRPolicies($embeddingService, $id);
            $this->processSystemGuidelines($embeddingService, $id);
        } elseif ($model === 'hrpolicies') {
            $this->processHRPolicies($embeddingService, $id);
        } elseif ($model === 'systemguidelines') {
            $this->processSystemGuidelines($embeddingService, $id);
        } else {
            $this->error("Unknown model: {$model}");

            return 1;
        }

        return 0;
    }

    /**
     * Process HR Policies
     *
     * @param  UniversalEmbeddingService  $embeddingService
     * @param  int|null  $id
     */
    protected function processHRPolicies($embeddingService, $id = null)
    {
        $this->info('Processing HR Policies...');

        $query = HRPolicy::query();

        if ($id) {
            $query->where('id', $id);
        }

        $count = 0;
        $query->chunk(10, function ($policies) use ($embeddingService, &$count) {
            foreach ($policies as $policy) {
                $this->processModel($policy, $embeddingService);
                $count++;
            }
        });

        $this->info("Processed {$count} HR Policies");
    }

    /**
     * Process System Guidelines
     *
     * @param  UniversalEmbeddingService  $embeddingService
     * @param  int|null  $id
     */
    protected function processSystemGuidelines($embeddingService, $id = null)
    {
        $this->info('Processing System Guidelines...');

        $query = SystemGuideline::query();

        if ($id) {
            $query->where('id', $id);
        }

        $count = 0;
        $query->chunk(10, function ($guidelines) use ($embeddingService, &$count) {
            foreach ($guidelines as $guideline) {
                $this->processModel($guideline, $embeddingService);
                $count++;
            }
        });

        $this->info("Processed {$count} System Guidelines");
    }

    /**
     * Process a single model
     *
     * @param  mixed  $model
     * @param  UniversalEmbeddingService  $embeddingService
     */
    protected function processModel($model, $embeddingService)
    {
        $result = $model->generateEmbeddings();

        if ($result['status'] === 'success') {
            $this->line("Processed {$model->getKey()}: {$result['chunks_processed']} chunks");
        } else {
            $this->warn("Failed to process {$model->getKey()}: {$result['message']}");
        }
    }
}
