<?php

namespace App\Http\Controllers\Backend\Client;

use App\Helpers\CoreApp\Traits\ApiReturnFormatTrait;
use App\Http\Controllers\Controller;
use App\Repositories\Hrm\Client\ClientRepository;
use Brian2694\Toastr\Facades\Toastr;
use Illuminate\Http\Request;
use Validator;

class ClientController extends Controller
{
    use ApiReturnFormatTrait;

    protected $clientRepo;

    public function __construct(ClientRepository $clientRepo)
    {
        $this->clientRepo = $clientRepo;
    }

    public function index(Request $request)
    {
        $data['fields'] = $this->clientRepo->fields();
        if ($request->ajax()) {
            return $this->clientRepo->table($request);
        }
        $data['checkbox'] = true;
        $data['class'] = 'clients_datatable';
        $data['status_url'] = \route('hrm.client.statusUpdate');
        $data['delete_url'] = \route('hrm.client.deleteData');

        $data['title'] = _trans('client.Client List');

        return \view('backend.client.index', \compact('data'));
    }

    public function create()
    {
        $data['title'] = _trans('client.Add New Client');

        return \view('backend.client.create', \compact('data'));
    }

    public function store(Request $request)
    {
        $this->validate($request, [
            'name' => 'required',
            'email' => 'required|email|unique:clients',
            'phone' => 'required|unique:clients',
            'website' => 'required',
        ]);
        try {
            $result = $this->clientRepo->storeClient($request);
            if ($result) {
                Toastr::success(_trans('response.Client created successfully'), 'Created');

                return \redirect()->route('client.index');
            } else {
                Toastr::error(_trans('response.Something went wrong!'), 'Error');

                return \redirect()->route('client.create');
            }
        } catch (\Throwable $th) {
            Toastr::error(_trans('response.Something went wrong!'), 'Error');

            return \redirect()->route('client.create');
        }
    }

    public function update(Request $request)
    {
        $rules = [
            'id' => 'required',
            'name' => 'required',
            'email' => 'required|email|unique:clients,email,'.$request->id,
            'phone' => 'required|unique:clients,phone,'.$request->id,
            'website' => 'required',
        ];

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return \redirect()->back()->withInput()->withErrors($validator);
        }

        $result = $this->clientRepo->updateClient($request);
        if ($result) {
            Toastr::success(_trans('response.Client updated successfully'), 'Updated');

            return \redirect()->route('client.index');
        }

        Toastr::error(_trans('response.Something went wrong!'), 'Error');

        return \redirect()->back();
    }

    public function datatable(Request $request)
    {
        return $this->clientRepo->dataTable($request);
    }

    public function edit($id)
    {
        $data['title'] = _trans('response.Edit Client Information');
        $data['show'] = $this->clientRepo->getById($id);

        return \view('backend.client.create', \compact('data'));
    }

    public function delete($id)
    {
        if (\config('app.style') === 'demo' || \env('APP_STYLE') === 'demo') {
            Toastr::warning('You are not allowed to perform the delete action in demo mode.', 'Warning');

            return \redirect()->back();
        }

        try {
            $result = $this->clientRepo->deleteClient($id);
            if ($result) {
                Toastr::success(_trans('response.Client deleted successfully'), 'Deleted');

                return \redirect()->route('client.index');
            } else {
                Toastr::error(_trans('response.Something went wrong!'), 'Error');

                return \redirect()->back();
            }
        } catch (\Throwable $th) {
            Toastr::error(_trans('response.Something went wrong!'), 'Error');

            return \redirect()->back();
        }
    }

    // status change
    public function statusUpdate(Request $request)
    {
        if (\demoCheck()) {
            return $this->responseWithError(_trans('message.You cannot do it for demo'), [], 400);
        }

        return $this->clientRepo->statusUpdate($request);
    }

    // destroy all selected data

    public function deleteData(Request $request)
    {
        if (\demoCheck()) {
            return $this->responseWithError(_trans('message.You cannot delete for demo'), [], 400);
        }

        return $this->clientRepo->destroyAll($request);
    }
}
