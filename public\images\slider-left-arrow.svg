<svg width="88" height="80" viewBox="0 0 88 80" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_6_51)">
<path d="M27.625 3L7 23.625L27.625 44.25" stroke="url(#paint0_linear_6_51)" stroke-width="5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M7 23.625H40C62.7824 23.625 81.25 42.0926 81.25 64.875V69" stroke="url(#paint1_linear_6_51)" stroke-width="5" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<defs>
<filter id="filter0_d_6_51" x="0.5" y="0.5" width="87.25" height="79" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_6_51"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_6_51" result="shape"/>
</filter>
<linearGradient id="paint0_linear_6_51" x1="17.3125" y1="3" x2="17.3125" y2="44.25" gradientUnits="userSpaceOnUse">
<stop stop-color="#0F6AFF"/>
<stop offset="1" stop-color="#21C6FB"/>
</linearGradient>
<linearGradient id="paint1_linear_6_51" x1="44.125" y1="23.625" x2="44.125" y2="69" gradientUnits="userSpaceOnUse">
<stop stop-color="#0F6AFF"/>
<stop offset="1" stop-color="#21C6FB"/>
</linearGradient>
</defs>
</svg>
