@extends('backend.layouts.app')
@section('title', @$data['title'])
@section('content')
<div class="content-wrapper">
    <div class="content-header">
        <div class="container-fluid">
            <div class="row">
                <div class="col-sm-6">
                    <h1 class="m-0 text-dark">{{ @$data['title'] }}</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">{{
                                _trans('common.Dashboard') }}</a></li>
                        <li class="breadcrumb-item active">{{ @$data['title'] }}</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>
    <!-- /.content-header -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-lg-12 ">
                    <div class="">
                        {{ Form::open(['url' => $data['url'], 'class' => 'card', 'id' => 'add_supplier', 'files' =>
                        'true']) }}
                        <div class="ot-card">
                            <div class="row">
                                <div class="col-md-12 text-center">
                                    <div class="form-group">
                                        <label class="form-label" for="image">{{ _trans('employee.Profile Picture')
                                            }}</label>
                                        <div class="input-group text-center position-center">
                                            <div class="input-group  justify-content-center">
                                                <div>
                                                    <div class="custom-image-upload-wrapper d-flex justify-center">
                                                        <div class="image-area d-flex"><img id="bruh"
                                                                src="{{ uploaded_asset(@$data['show']->avatar_id) }}"
                                                                class="img-fluid mx-auto my-auto">
                                                        </div>
                                                        <div class="input-area"><label class="form-label"
                                                                id="upload-label" for="appSettings_company_logo">
                                                                {{ _trans('employee.Change Avatar') }}
                                                            </label> <input id="appSettings_company_logo" name="avatar"
                                                                type="file" class="form-control d-none"
                                                                placeholder="{{ _trans('employee.Change Avatar') }}">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        @if (@$data['edit']->avatar_id)
                                        <img class="custom-edit img"
                                            src="{{ uploaded_asset($data['edit']->avatar_id) }}" alt="onest-hrm"
                                            width="80" srcset="">
                                        @endif
                                        @error('image')
                                        <span class="invalid-feedback" role="alert">
                                            <strong>{{ $message }}</strong>
                                        </span>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label" for="name">{{ _trans('employee.Name') }}</label>
                                        <input type="text" name="name" class="form-control" id="name"
                                            placeholder="{{ _trans('employee.Name') }}"
                                            value="{{ @$data['edit']->name ? $data['edit']->name : old('name') }}">
                                        @if ($errors->has('name'))
                                        <div class="error">{{ $errors->first('name') }}</div>
                                        @endif
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label" for="phone">{{ _trans('employee.Phone') }}</label>
                                        <input type="number" class="form-control" id="phone" name="phone"
                                            placeholder="{{ _trans('employee.Phone') }}"
                                            value="{{ @$data['edit']->phone ? $data['edit']->phone : old('phone') }}">
                                        @if ($errors->has('phone'))
                                        <div class="error">{{ $errors->first('phone') }}</div>
                                        @endif
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label" for="email">{{ _trans('employee.Email') }}</label>
                                        <input type="email" class="form-control"
                                            value="{{ @$data['edit']->email ? $data['edit']->email : old('email') }}"
                                            name="email" id="email" placeholder="{{ _trans('employee.Email') }}">
                                        @if ($errors->has('email'))
                                        <div class="error">{{ $errors->first('email') }}</div>
                                        @endif
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label" for="permanent_address">{{ _trans('employee.Permanent
                                            Address') }}</label>
                                        <input class="form-control" id="permanent_address" name="permanent_address"
                                            placeholder="{{ _trans('employee.Permanent Address') }}"
                                            value="{{ @$data['edit']->permanent_address ? $data['edit']->permanent_address : old('permanent_address') }}">
                                        @if ($errors->has('permanent_address'))
                                        <div class="error">{{ $errors->first('permanent_address') }}</div>
                                        @endif
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label" for="current_address">{{ _trans('employee.Current
                                            Address') }}</label>
                                        <input class="form-control " id="current_address" name="current_address"
                                            placeholder="{{ _trans('employee.Current Address') }}"
                                            value="{{ @$data['edit']->current_address ? $data['edit']->current_address : old('current_address') }}">
                                        @error('current_address')
                                        <span class="invalid-feedback" role="alert">
                                            <strong>{{ $message }}</strong>
                                        </span>
                                        @enderror
                                    </div>
                                </div>






                            </div>
                        </div>
                        <div class="text-center">
                            <button type="submit" class="btn btn-primary mb-3">{{ _trans('employee.Submit Now') }}</button>
                        </div>
                        {{Form::close() }}
                    </div>



                </div>
            </div>
        </div>
</div>
</section>
<!-- Main content -->
</div>
@endsection

@section('script')
<script src="{{url('backend/js/image_preview.js') }}"></script>
@endsection