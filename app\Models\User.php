<?php

namespace App\Models;

use App\Models\Company\Company;
use App\Models\coreApp\Setting\CompanyConfig;
use App\Models\coreApp\Setting\UserIpBind;
use App\Models\coreApp\Social\SocialIdentity;
use App\Models\coreApp\Traits\Relationship\StatusRelationTrait;
use App\Models\Hrm\Appreciate;
use App\Models\Hrm\Attendance\Attendance;
use App\Models\Hrm\Attendance\DutySchedule;
use App\Models\Hrm\Country\Country;
use App\Models\Hrm\Department\Department;
use App\Models\Hrm\Designation\Designation;
use App\Models\Hrm\Holiday\Holiday;
use App\Models\Hrm\Leave\LeaveAssign;
use App\Models\Hrm\Leave\LeaveRequest;
use App\Models\Hrm\Notice\Notice;
use App\Models\Payroll\SalarySetup;
use App\Models\Payroll\SalarySetupDetails;
use App\Models\Role\Role;
use App\Models\Settings\LocationBind;
use App\Models\Track\LocationLog;
use App\Models\Traits\CompanyBranchTrait;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Kreait\Firebase\Messaging\Notification;
use Laravel\Cashier\Billable;
use Laravel\Sanctum\HasApiTokens;
use Modules\Credential\Entities\CredentialAccess;
use Modules\SpecialAttendance\Entities\DutyCalendar;
use Modules\Tardy\Entities\TardyRuleAssign;
use UniversalChatbot\Traits\ChatbotIntegration;

class User extends Authenticatable
{
    use Billable, ChatbotIntegration, CompanyBranchTrait, HasApiTokens, HasFactory, Notifiable, SoftDeletes, StatusRelationTrait;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $guarded = ['id', 'created_at', 'updated_at'];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'password',
        'remember_token',
        'image',
    ];

    protected $appends = ['avatar'];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'company_id' => 'integer',
        'attendance_method' => 'json',
        'image' => 'json',
    ];

    public function getAvatarAttribute()
    {
        $avatar = null;
        if ($this->image) {
            $avatar = Storage::disk($this->image['disk'])->url($this->image['file']);
        }

        return $avatar;
    }

    public function scopeIsActive($query)
    {
        return $query->whereNotIn('status', ['suspended', 'terminated', 'retired', 'layoff', 'resigned']);
    }

    public function permissions(): HasOne
    {
        return $this->hasOne(UserWisePermission::class);
    }

    public function personalInfo(): HasOne
    {
        return $this->hasOne(UserInfo::class);
    }

    public function attendanceConfig(): HasOne
    {
        return $this->hasOne(AttendanceConfiguration::class);
    }

    public function salaryConfig(): HasOne
    {
        return $this->hasOne(SalaryConfiguration::class);
    }

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'company_id', 'id')->withDefault();
    }

    public function deActiveCompany(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'company_id', 'id')->where('status_id', '!=', 1)->withDefault();
    }

    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class, 'branch_id', 'id')->withDefault();
    }

    public function companyConfigs(): HasMany
    {
        return $this->hasMany(CompanyConfig::class, 'company_id', 'company_id');
    }

    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'country_id')->withDefault();
    }

    public function identities(): HasMany
    {
        return $this->hasMany(SocialIdentity::class);
    }

    public function uploads(): HasMany
    {
        return $this->hasMany(Upload::class);
    }

    public function personalDocument(): HasOne
    {
        return $this->hasOne(UserPersonalDocument::class);
    }

    public function department(): BelongsTo
    {
        return $this->belongsTo(Department::class, 'department_id', 'id');
    }

    public function designation(): BelongsTo
    {
        return $this->belongsTo(Designation::class, 'designation_id', 'id');
    }

    public function role(): BelongsTo
    {
        return $this->belongsTo(Role::class, 'role_id', 'id');
    }

    public function manager(): BelongsTo
    {
        return $this->belongsTo(User::class, 'manager_id');
    }

    // DutySchedule Start
    public function dutySchedules()
    {
        return $this->belongsToMany(DutySchedule::class, 'user_duty_schedules', 'user_id', 'duty_schedule_id');
    }
    // DutySchedule End

    // Employee Lifecycle Start
    public function lifecycle()
    {
        return $this->hasMany(EmployeeLifecycle::class, 'user_id', 'id');
    }
    // Employee Lifecycle End

    // Credential Access Start
    public function credentialAccesses()
    {
        return $this->hasMany(CredentialAccess::class);
    }
    // Credential Access End

    // Holiday Start
    public function holidays()
    {
        return $this->hasManyThrough(
            Holiday::class,
            HolidayAppliedDepartment::class,
            'department_id',
            'id',
            'department_id',
            'holiday_id'
        );
    }

    public function isHoliday($date)
    {
        return $this->holidays()->where('date', $date)->exists();
    }
    // Holiday End

    // Leave Start
    public function leaveAssigns(): HasMany
    {
        return $this->hasMany(LeaveAssign::class, 'user_id', 'id');
    }

    public function leaves(): HasMany
    {
        return $this->hasMany(LeaveAssign::class, 'department_id', 'department_id')
            ->where('status', 'active')
            ->whereHas('type', function ($query) {
                $query->where('status', 'active')
                    ->whereIn('gender', ['All', $this->gender]);
            })
            ->with('type');
    }

    public function personalLeaves(): HasMany
    {
        return $this->hasMany(LeaveAssign::class, 'user_id')
            ->where('status', 'active')
            ->whereHas('type', function ($query) {
                $query->where('status', 'active');
            })
            ->with('type');
    }

    public function leaveRequests()
    {
        return $this->hasMany(LeaveRequest::class, 'user_id', 'id');
    }

    public function approvedLeaves()
    {
        return $this->hasMany(LeaveRequest::class, 'user_id', 'id')->where('status', 'approved');
    }

    public function approvedLeaveDates($date = null)
    {
        $dates = \collect();

        $this->approvedLeaves()->get()->each(function ($leave) use (&$dates) {
            $from = Carbon::parse($leave->leave_from);
            $to = Carbon::parse($leave->leave_to);

            while ($from->lte($to)) {
                $dates->push([
                    'date' => $from->format('Y-m-d'),
                    'leave_request_id' => $leave->id,
                ]);
                $from->addDay();
            }
        });

        $dates = $dates->unique('date')->values()->toArray();

        if ($date) {
            $leaveDates = collect($dates)->pluck('date')->all();

            return in_array($date, $leaveDates);
        } else {
            return $dates;
        }
    }

    public function leaveSummary()
    {
        $leaves = $this->leaves()
            ->with(['leaveRequests' => function ($query) {
                $query->where('user_id', $this->id)
                    ->where('status', 'approved');
            }])
            ->get()
            ->map(function ($leave) {
                $approvedDays = $leave->leaveRequests->sum('days');
                $latestRequestId = $leave->leaveRequests->sortByDesc('created_at')->first()?->id;

                return [
                    'leave_type_id' => $leave->type_id,
                    'leave_assign_id' => $leave->id,
                    'leave_request_id' => $latestRequestId,
                    'type' => @$leave->type->name,
                    'assigns_days' => $leave->days,
                    'approved_days' => $approvedDays,
                    'remaining_days' => max(0, $leave->days - $approvedDays),
                    'status' => max(0, $leave->days - $approvedDays) > 0 ? 'Available' : 'Not Available',
                    'role' => 'Departmental',
                ];
            });

        $myLeaves = $this->personalLeaves()
            ->with(['leaveRequests' => function ($query) {
                $query->where('user_id', $this->id)
                    ->where('status', 'approved');
            }])
            ->get()
            ->map(function ($leave) {
                $approvedDays = $leave->leaveRequests->sum('days');
                $latestRequestId = $leave->leaveRequests->sortByDesc('created_at')->first()?->id;

                return [
                    'leave_type_id' => $leave->type_id,
                    'leave_assign_id' => $leave->id,
                    'leave_request_id' => $latestRequestId,
                    'type' => @$leave->type->name,
                    'assigns_days' => $leave->days,
                    'approved_days' => $approvedDays,
                    'remaining_days' => max(0, $leave->days - $approvedDays),
                    'status' => max(0, $leave->days - $approvedDays) > 0 ? 'Available' : 'Not Available',
                    'role' => 'Personal',
                ];
            });

        return collect($leaves->merge($myLeaves)->toArray());
    }
    // Leave End

    public function attendances(): HasMany
    {
        return $this->hasMany(Attendance::class, 'user_id', 'id');
    }

    public function notices(): HasMany
    {
        return $this->hasMany(Notice::class, 'department_id', 'id');
    }

    public function appreciates(): HasMany
    {
        return $this->hasMany(Appreciate::class, 'user_id', 'id');
    }

    public function tardyRules(): HasMany
    {
        return $this->hasMany(TardyRuleAssign::class, 'user_id', 'id');
    }

    public function myHr()
    {
        $hr_info = $this->where('is_hr', 1)->first();
        if ($hr_info) {
            return $hr_info;
        } else {
            $admin_info = $this->where('is_admin', 1)->first();
            if ($admin_info) {
                return $admin_info;
            } else {
                return null;
            }
        }
    }

    public function myTeam()
    {
        return $this->where('manager_id', Auth::user()->id)->get();
    }

    public function unreadNoticeNotifications()
    {
        return $this->hasMany(Notification::class, 'notifiable_id', 'id')->where('notification_for', 'notice')->where('read_at', null);
    }

    public function unreadNotifications()
    {
        return $this->hasMany(Notification::class, 'notifiable_id', 'id')->whereNull('read_at');
    }

    public function notifications()
    {
        return $this->hasMany(Notification::class, 'receiver_id', 'id');
    }

    public function allNotifications()
    {
        return $this->hasMany(Notification::class, 'notifiable_id', 'id');
    }

    public function salary_setup()
    {
        return $this->hasOne(SalarySetup::class, 'user_id', 'id');
    }

    public function staffCommissions()
    {
        return $this->hasMany(SalarySetupDetails::class, 'user_id', 'id');
    }

    public function location_log()
    {
        return $this->hasOne(LocationLog::class, 'user_id', 'id')->orderBy('id', 'desc');
    }

    protected static function boot()
    {
        parent::boot();
        if (! \app()->runningInConsole() && Auth::check() && ! \config('app.single_db')) {
            static::creating(function ($model) {
                $model->employee_id = 'EMP-'.$model->id;
                $model->permissions = $model->role->permissions;
                $model->is_hr = $model->role->slug == 'hr' ? 1 : 0;
            });
        }
    }

    public function notification_channels()
    {
        $notification_channels = [
            'user'.$this->id.\str_replace(' ', '', Auth::user()->company->name),
            'department'.$this->department_id.\str_replace(' ', '', Auth::user()->company->name),
            'company'.Auth::user()->company->id.\str_replace(' ', '', Auth::user()->company->name),
        ];

        return $notification_channels;
    }

    public function notification_subscribe_channel()
    {
        $notification_channels = [
            'user'.$this->id.'company'.$this->company_id,
            'department'.$this->department_id.'company'.$this->company_id,
            'company'.$this->company_id,
        ];

        return $notification_channels;
    }

    public function scopeActive($query)
    {
        return $query->where('users.status_id', '=', 1);
        // 1,2,3,33,34
    }

    // User.php

    public function staffMembers()
    {
        return $this->hasMany(User::class, 'manager_id', 'id');
    }

    public function dutyCalendars()
    {
        return $this->hasMany(DutyCalendar::class, 'employee_id');
    }

    public function lastDutyCalendar()
    {
        return $this->hasOne(DutyCalendar::class, 'employee_id', 'id')->orderByDesc('date');
    }

    public function ipBind(): HasOne
    {
        return $this->hasOne(UserIpBind::class, 'user_id', 'id');
    }

    public function locationBinds(): HasMany
    {
        return $this->hasMany(LocationBind::class, 'user_id', 'id');
    }

    public function hrUserIds($user)
    {
        return $this->where([
            'is_hr' => 1,
            'status_id' => 1,
        ])
            ->pluck('id')
            ->toArray();
    }

    /**
     * Define what content should be embedded for the chatbot
     *
     * @return array
     */
    public function getEmbeddableContent()
    {
        $content = [
            "Name: {$this->name}",
            "Email: {$this->email}",
            "Employee ID: {$this->employee_id}",
        ];

        if ($this->department) {
            $content[] = "Department: {$this->department->title}";
        }

        if ($this->designation) {
            $content[] = "Designation: {$this->designation->title}";
        }

        if ($this->manager) {
            $content[] = "Manager: {$this->manager->name}";
        }

        if ($this->joining_date) {
            $content[] = "Joining Date: {$this->joining_date}";
        }

        if ($this->is_admin) {
            $content[] = 'Role: Administrator';
        } elseif ($this->is_hr) {
            $content[] = 'Role: HR Manager';
        }

        return $content;
    }

    /**
     * Define context for this model
     *
     * @return array
     */
    public function getEmbeddingContext()
    {
        return [
            'type' => class_basename($this),
            'id' => $this->id,
            'category' => $this->department ? $this->department->title : 'Staff',
        ];
    }
}
