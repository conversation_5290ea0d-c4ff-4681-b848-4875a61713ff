<?php

namespace UniversalChatbot;

use Illuminate\Support\ServiceProvider;
use UniversalChatbot\Services\EnhancedGeminiService;
use UniversalChatbot\Services\GeminiService;
use UniversalChatbot\Services\QueryGenerationService;
use UniversalChatbot\Services\UniversalChatService;

class UniversalChatbotServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        // Register the main service
        $this->app->singleton('universal-chat', function ($app) {
            return new UniversalChatService;
        });

        // Register the Gemini service
        $this->app->singleton('gemini-service', function ($app) {
            return new GeminiService;
        });

        // Register individual services for the refactored EnhancedGeminiService
        $this->app->singleton(GeminiApiClient::class, function ($app) {
            return new GeminiApiClient();
        });

        $this->app->singleton(DatabaseSchemaService::class, function ($app) {
            return new DatabaseSchemaService();
        });

        $this->app->singleton(QueryExecutor::class, function ($app) {
            return new QueryExecutor();
        });

        $this->app->singleton(PerformanceTracker::class, function ($app) {
            return new PerformanceTracker();
        });

        $this->app->singleton(FollowUpDetector::class, function ($app) {
            return new FollowUpDetector();
        });

        $this->app->singleton(QueryAnalyzer::class, function ($app) {
            return new QueryAnalyzer(
                $app->make(GeminiApiClient::class),
                $app->make(DatabaseSchemaService::class)
            );
        });

        $this->app->singleton(ResponseFormatter::class, function ($app) {
            return new ResponseFormatter(
                $app->make(GeminiApiClient::class)
            );
        });

        // Register the enhanced Gemini service with dependencies
        $this->app->singleton('enhanced-gemini-service', function ($app) {
            return new EnhancedGeminiService(
                $app->make(GeminiApiClient::class),
                $app->make(DatabaseSchemaService::class),
                $app->make(QueryAnalyzer::class),
                $app->make(QueryExecutor::class),
                $app->make(ResponseFormatter::class),
                $app->make(PerformanceTracker::class),
                $app->make(FollowUpDetector::class)
            );
        });

        // Register config
        $this->mergeConfigFrom(
            __DIR__ . '/../config/universal_chatbot.php', 'universal_chatbot'
        );

        // Register models
        $this->app->bind('universal_chatbot.models.chatbot_query', function () {
            return config('universal_chatbot.models.chatbot_query', \UniversalChatbot\Models\ChatbotQuery::class);
        });

        // Register services
        $this->app->singleton('UniversalChatbot\Services\ChunkService', function ($app) {
            return new \UniversalChatbot\Services\ChunkService;
        });

        $this->app->singleton('UniversalChatbot\Services\EnhancedMerkleTreeService', function ($app) {
            return new \UniversalChatbot\Services\EnhancedMerkleTreeService;
        });

        $this->app->singleton('UniversalChatbot\Services\GeminiEmbeddingService', function ($app) {
            return new \UniversalChatbot\Services\GeminiEmbeddingService;
        });

        $this->app->singleton('UniversalChatbot\Services\UniversalEmbeddingService', function ($app) {
            return new \UniversalChatbot\Services\UniversalEmbeddingService(
                $app->make('UniversalChatbot\Services\GeminiEmbeddingService'),
                $app->make('UniversalChatbot\Services\ChunkService'),
                $app->make('UniversalChatbot\Services\EnhancedMerkleTreeService')
            );
        });

        $this->app->singleton('UniversalChatbot\Services\UniversalSearchService', function ($app) {
            return new \UniversalChatbot\Services\UniversalSearchService(
                $app->make('UniversalChatbot\Services\GeminiEmbeddingService'),
                $app->make('UniversalChatbot\Services\EnhancedMerkleTreeService')
            );
        });

        // Register both GeminiService and EnhancedGeminiService
        $this->app->singleton(GeminiService::class, function ($app) {
            return new GeminiService(
                $app->make(QueryGenerationService::class)
            );
        });

        $this->app->singleton(EnhancedGeminiService::class, function ($app) {
            return new EnhancedGeminiService;
        });

        // Use EnhancedGeminiService in UniversalChatService
        $this->app->singleton('UniversalChatbot\Services\UniversalChatService', function ($app) {
            return new \UniversalChatbot\Services\UniversalChatService(
                $app->make('UniversalChatbot\Services\UniversalSearchService'),
                $app->make('UniversalChatbot\Services\EnhancedGeminiService'),
                $app->make('UniversalChatbot\Services\EnhancedMerkleTreeService')
            );
        });

        $this->app->singleton('UniversalChatbot\Services\MerkleVerificationService', function ($app) {
            return new \UniversalChatbot\Services\MerkleVerificationService(
                $app->make('UniversalChatbot\Services\EnhancedMerkleTreeService')
            );
        });

        $this->app->singleton('UniversalChatbot\Services\ChatAnalyticsService', function ($app) {
            return new \UniversalChatbot\Services\ChatAnalyticsService;
        });

        $this->app->singleton(QueryGenerationService::class, function ($app) {
            return new QueryGenerationService;
        });
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        // Register commands
        if ($this->app->runningInConsole()) {
            $this->commands([
                Commands\TestGeminiService::class,
                Commands\TestEnhancedGeminiService::class,
                Commands\ProcessEntityEmbeddings::class,
                Commands\UpdateDatabaseSchema::class,
                Commands\GetDatabaseSchema::class,
            ]);
        }

        // Register routes
        $this->loadRoutesFrom(__DIR__ . '/Routes/api.php');
        $this->loadRoutesFrom(__DIR__ . '/Routes/web.php');

        // Register views with both namespaces for backwards compatibility
        $this->loadViewsFrom(__DIR__ . '/Views', 'universal_chatbot');
        $this->loadViewsFrom(__DIR__ . '/Views', 'universal-chatbot');

        // Register migrations
        $this->loadMigrationsFrom(__DIR__ . '/../database/migrations');

        // Publish assets
        $this->publishes([
            __DIR__ . '/../config/universal_chatbot.php' => config_path('universal_chatbot.php'),
            __DIR__ . '/Views'                           => resource_path('views/vendor/universal_chatbot'),
        ], 'universal-chatbot');

        // Register observers
        $this->registerObservers();

        // Set default configuration values if not explicitly set
        if (!config('universal_chatbot.api_keys.gemini')) {
            config(['universal_chatbot.api_keys.gemini' => env('GEMINI_API_KEY')]);
        }

        if (!config('universal_chatbot.chat.temperature')) {
            config(['universal_chatbot.chat.temperature' => 0.3]);
        }

        if (!config('universal_chatbot.chat.max_tokens')) {
            config(['universal_chatbot.chat.max_tokens' => 1024]);
        }

        // Database schema is now retrieved directly from default connection
    }

    /**
     * Register model observers.
     *
     * @return void
     */
    protected function registerObservers()
    {
        if ($this->app->runningInConsole()) {
            return;
        }

        // Register entity observer for models with HasEmbeddings trait
        // These are example models - they should be defined in your application
        // or configured in the universal_chatbot.supported_entities config

        // Get supported entities from config
        $supportedEntities = config('universal_chatbot.supported_entities', []);

        // Register observers for each supported entity
        foreach ($supportedEntities as $entityClass) {
            if (class_exists($entityClass)) {
                $entityClass::observe(\UniversalChatbot\Observers\EntityObserver::class);
            }
        }
    }
}
