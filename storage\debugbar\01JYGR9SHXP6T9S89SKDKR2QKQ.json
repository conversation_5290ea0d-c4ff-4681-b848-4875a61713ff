{"__meta": {"id": "01JYGR9SHXP6T9S89SKDKR2QKQ", "datetime": "2025-06-24 16:38:50", "utime": **********.941603, "method": "GET", "uri": "/api/universal-chatbot/chat/conversations", "ip": "127.0.0.1"}, "php": {"version": "8.3.10", "interface": "cgi-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.071063, "end": **********.941615, "duration": 0.8705520629882812, "duration_str": "871ms", "measures": [{"label": "Booting", "start": **********.071063, "relative_start": 0, "end": **********.881336, "relative_end": **********.881336, "duration": 0.****************, "duration_str": "810ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.881344, "relative_start": 0.****************, "end": **********.941616, "relative_end": 9.5367431640625e-07, "duration": 0.****************, "duration_str": "60.27ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.888282, "relative_start": 0.****************, "end": **********.904225, "relative_end": **********.904225, "duration": 0.015943050384521484, "duration_str": "15.94ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.938801, "relative_start": 0.****************, "end": **********.939133, "relative_end": **********.939133, "duration": 0.000331878662109375, "duration_str": "332μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.940152, "relative_start": 0.***************, "end": **********.940191, "relative_end": **********.940191, "duration": 3.910064697265625e-05, "duration_str": "39μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "51MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "GET api/universal-chatbot/chat/conversations", "middleware": "api, auth:sanctum", "controller": "UniversalChatbot\\Controllers\\UniversalChatController@getUserConversations<a href=\"cursor://file/C%3A%2Flaragon%2Fwww%2Fonest-hrm%2Fpackages%2FUniversalChatbot%2Fsrc%2FControllers%2FUniversalChatController.php:103\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "api/universal-chatbot", "file": "<a href=\"cursor://file/C%3A%2Flaragon%2Fwww%2Fonest-hrm%2Fpackages%2FUniversalChatbot%2Fsrc%2FControllers%2FUniversalChatController.php:103\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/UniversalChatbot/src/Controllers/UniversalChatController.php:103-121</a>"}, "queries": {"count": 3, "nb_statements": 3, "nb_visible_statements": 3, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00371, "accumulated_duration_str": "3.71ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from \"users\" where \"id\" = 1 and \"users\".\"company_id\" = 1 and \"users\".\"branch_id\" = 1 and \"users\".\"deleted_at\" is null limit 1", "type": "query", "params": [], "bindings": [1, 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 56}, {"index": 23, "namespace": null, "name": "vendor/laravel/sanctum/src/Http/Middleware/AuthenticateSession.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\AuthenticateSession.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}], "start": **********.911, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "cursor://file/C%3A%2Flaragon%2Fwww%2Fonest-hrm%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php:58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "onest_single_hrm", "explain": null, "start_percent": 0, "width_percent": 35.58}, {"sql": "select * from \"chat_conversations\" where \"user_id\" = 1 order by \"updated_at\" desc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "packages/UniversalChatbot/src/Controllers/UniversalChatController.php", "file": "C:\\laragon\\www\\onest-hrm\\packages\\UniversalChatbot\\src\\Controllers\\UniversalChatController.php", "line": 114}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.915703, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "UniversalChatController.php:114", "source": {"index": 15, "namespace": null, "name": "packages/UniversalChatbot/src/Controllers/UniversalChatController.php", "file": "C:\\laragon\\www\\onest-hrm\\packages\\UniversalChatbot\\src\\Controllers\\UniversalChatController.php", "line": 114}, "xdebug_link": {"url": "cursor://file/C%3A%2Flaragon%2Fwww%2Fonest-hrm%2Fpackages%2FUniversalChatbot%2Fsrc%2FControllers%2FUniversalChatController.php:114", "ajax": false, "filename": "UniversalChatController.php", "line": "114"}, "connection": "onest_single_hrm", "explain": null, "start_percent": 35.58, "width_percent": 28.302}, {"sql": "select * from \"chat_messages\" where \"chat_messages\".\"conversation_id\" in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17) order by \"id\" asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "packages/UniversalChatbot/src/Controllers/UniversalChatController.php", "file": "C:\\laragon\\www\\onest-hrm\\packages\\UniversalChatbot\\src\\Controllers\\UniversalChatController.php", "line": 114}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\onest-hrm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.9274611, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "UniversalChatController.php:114", "source": {"index": 20, "namespace": null, "name": "packages/UniversalChatbot/src/Controllers/UniversalChatController.php", "file": "C:\\laragon\\www\\onest-hrm\\packages\\UniversalChatbot\\src\\Controllers\\UniversalChatController.php", "line": 114}, "xdebug_link": {"url": "cursor://file/C%3A%2Flaragon%2Fwww%2Fonest-hrm%2Fpackages%2FUniversalChatbot%2Fsrc%2FControllers%2FUniversalChatController.php:114", "ajax": false, "filename": "UniversalChatController.php", "line": "114"}, "connection": "onest_single_hrm", "explain": null, "start_percent": 63.881, "width_percent": 36.119}]}, "models": {"data": {"UniversalChatbot\\Models\\ChatMessage": {"value": 30, "xdebug_link": {"url": "cursor://file/C%3A%2Flaragon%2Fwww%2Fonest-hrm%2Fpackages%2FUniversalChatbot%2Fsrc%2FModels%2FChatMessage.php:1", "ajax": false, "filename": "ChatMessage.php", "line": "?"}}, "UniversalChatbot\\Models\\ChatConversation": {"value": 17, "xdebug_link": {"url": "cursor://file/C%3A%2Flaragon%2Fwww%2Fonest-hrm%2Fpackages%2FUniversalChatbot%2Fsrc%2FModels%2FChatConversation.php:1", "ajax": false, "filename": "ChatConversation.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "cursor://file/C%3A%2Flaragon%2Fwww%2Fonest-hrm%2Fapp%2FModels%2FUser.php:1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 48, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"session_company_id": "1", "_token": "m001t84IVZwwquSQyIS0B97SjcqtA2Arq60vPBXU", "url": "array:1 [\n  \"intended\" => \"http://onest-hrm.test/chatbot\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://onest-hrm.test/api/universal-chatbot/chat/conversations\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "session_branch_id": "1", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "PHPDEBUGBAR_STACK_DATA": "[]", "password_hash_web": "$2y$10$6QCozhwVP14YWarDth2to.85wrvBG4n4lolY2yyDWMNcu.R4VFzAC"}, "request": {"data": {"status": "200 OK", "full_url": "http://onest-hrm.test/api/universal-chatbot/chat/conversations", "action_name": null, "controller_action": "UniversalChatbot\\Controllers\\UniversalChatController@getUserConversations", "uri": "GET api/universal-chatbot/chat/conversations", "controller": "UniversalChatbot\\Controllers\\UniversalChatController@getUserConversations<a href=\"cursor://file/C%3A%2Flaragon%2Fwww%2Fonest-hrm%2Fpackages%2FUniversalChatbot%2Fsrc%2FControllers%2FUniversalChatController.php:103\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "api/universal-chatbot", "file": "<a href=\"cursor://file/C%3A%2Flaragon%2Fwww%2Fonest-hrm%2Fpackages%2FUniversalChatbot%2Fsrc%2FControllers%2FUniversalChatController.php:103\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/UniversalChatbot/src/Controllers/UniversalChatController.php:103-121</a>", "middleware": "api, auth:sanctum", "telescope": "<a href=\"http://onest-hrm.test/_debugbar/telescope/9f3b17e9-f33c-4ff7-ac90-f71819b824d4\" target=\"_blank\">View in Telescope</a>", "duration": "872ms", "peak_memory": "56MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-939922607 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-939922607\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1777130879 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1777130879\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1064403617 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"709 characters\">XSRF-TOKEN=eyJpdiI6Imcxc2theHZndjhGTUhVNHUxRDd4Q0E9PSIsInZhbHVlIjoiNW0wemF0M3ZsQlMwVkFtcXNyakNRQzhGM0Vtc3h0dEF6Y0IxZkdYZC83NDVxZzZyc0Z5ZGNCSFhCd1F3T2JHRDJhV1ZzRTF1L01NVUVMcnRBTGpwWloyVXNNVnpsZU52Z01nNGRGdjFIMlRCcms5WS9FcExjTGJvV1R6b1g4SjkiLCJtYWMiOiJmN2VjMGI2YTAyNjcxMDM4ZjY4NDUwODI0OTk5NmY0ZmJkNGRkZmFhZDgxMzMwYzY4MjU5MmQ2MDA4OTc4Y2E3IiwidGFnIjoiIn0%3D; idl_session=eyJpdiI6IjUwOE52cllCZmFTMERtdXZYWld5VUE9PSIsInZhbHVlIjoiZ3dsbUdhNEthQXNFbEFNNFR4L1pFQUtSLytVazQ2anJobkFWbTlmdE9obXRLbVlLUkw5cVkxUjBQTXVHTlEyYlpFZVZTS2swNHNpb2NURElzUFIwNzhFN0d4U3JESnlWUThTL3ZwcitBRDBXVjRPM24vaTJJSjV5VTZ6S3hBclQiLCJtYWMiOiJmN2QyNWU3MzVkZTgwZTQ1NWRlMTBmNWJlYTUxMTYzNzNkODAxNjNkOGU4YzYyMTcwYzdkODg4YzFjNWNmNmM5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://onest-hrm.test/chatbot</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">m001t84IVZwwquSQyIS0B97SjcqtA2Arq60vPBXU</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">onest-hrm.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1064403617\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-327997821 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">m001t84IVZwwquSQyIS0B97SjcqtA2Arq60vPBXU</span>\"\n  \"<span class=sf-dump-key>idl_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">DYqGMQxh1PyKl8EE4iSo1uy4iIOtOotZHjuAQr15</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-327997821\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 24 Jun 2025 10:38:50 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">60</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">59</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkdJMXhkL3JNck92UzJxRlo5RHNHVkE9PSIsInZhbHVlIjoiTHJxS2YxUXU0TlU2MllLQXFkUlRZS0kwTmNnSTcyOElhU3JjMk5rT1hScGFGQk5mMGNUMlNGaDZSWTZTRDBUdlN2MnFyMy83Z0RRZnl0L1owa3FiVHhReHByREgyNjh3aFI5azR1MTcydG9ia3pMc1RXYzBtZ3pTd1Y4emZHeXAiLCJtYWMiOiJlMzQzMjg5ODgwODFmYjFmMGYyMzFhNTFkOGQ4ZWU2MzIyYTM4M2ZjMTg5MDIyY2EwN2Y3ZGM3M2JlZWU3ZjQ0IiwidGFnIjoiIn0%3D; expires=Tue, 24 Jun 2025 12:38:50 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"439 characters\">idl_session=eyJpdiI6IlNaTUFXclJ5djZ4bjU2b1VhVmlXS0E9PSIsInZhbHVlIjoienpqVS9GNmY4TXg0RFFkZDhydzBkR1N6eDY1WHlDcWcvNnFIYzZUckNCNHd5a3VSRkthMi9DMHVHV3pITEt3bnFrNUpBUXpheHJaenRrSndKT29vMWw5ZkZyUWRUaEVBc2dkVUxmcmZrWmhEM05CZnB3eEV0dnI0VTh4aHpsbkQiLCJtYWMiOiIwMWU3ZTE1ZmFjMzg3NGJiMTYwYWQ4NmIxMmMyMjkwYWI1NTFlNTZjZmIzNTVlYmFjMGJkNmZmZGQ1YmZkYjhiIiwidGFnIjoiIn0%3D; expires=Tue, 24 Jun 2025 12:38:50 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkdJMXhkL3JNck92UzJxRlo5RHNHVkE9PSIsInZhbHVlIjoiTHJxS2YxUXU0TlU2MllLQXFkUlRZS0kwTmNnSTcyOElhU3JjMk5rT1hScGFGQk5mMGNUMlNGaDZSWTZTRDBUdlN2MnFyMy83Z0RRZnl0L1owa3FiVHhReHByREgyNjh3aFI5azR1MTcydG9ia3pMc1RXYzBtZ3pTd1Y4emZHeXAiLCJtYWMiOiJlMzQzMjg5ODgwODFmYjFmMGYyMzFhNTFkOGQ4ZWU2MzIyYTM4M2ZjMTg5MDIyY2EwN2Y3ZGM3M2JlZWU3ZjQ0IiwidGFnIjoiIn0%3D; expires=Tue, 24-Jun-2025 12:38:50 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"411 characters\">idl_session=eyJpdiI6IlNaTUFXclJ5djZ4bjU2b1VhVmlXS0E9PSIsInZhbHVlIjoienpqVS9GNmY4TXg0RFFkZDhydzBkR1N6eDY1WHlDcWcvNnFIYzZUckNCNHd5a3VSRkthMi9DMHVHV3pITEt3bnFrNUpBUXpheHJaenRrSndKT29vMWw5ZkZyUWRUaEVBc2dkVUxmcmZrWmhEM05CZnB3eEV0dnI0VTh4aHpsbkQiLCJtYWMiOiIwMWU3ZTE1ZmFjMzg3NGJiMTYwYWQ4NmIxMmMyMjkwYWI1NTFlNTZjZmIzNTVlYmFjMGJkNmZmZGQ1YmZkYjhiIiwidGFnIjoiIn0%3D; expires=Tue, 24-Jun-2025 12:38:50 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1092503263 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_company_id</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">m001t84IVZwwquSQyIS0B97SjcqtA2Arq60vPBXU</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://onest-hrm.test/chatbot</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"62 characters\">http://onest-hrm.test/api/universal-chatbot/chat/conversations</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>session_branch_id</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$10$6QCozhwVP14YWarDth2to.85wrvBG4n4lolY2yyDWMNcu.R4VFzAC</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1092503263\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://onest-hrm.test/api/universal-chatbot/chat/conversations", "controller_action": "UniversalChatbot\\Controllers\\UniversalChatController@getUserConversations"}, "badge": null}}