<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateDutySchedulesTable extends Migration
{
    public function up()
    {
        Schema::create('duty_schedules', function (Blueprint $table) {
            $table->id();
            $table->string('shift')->nullable();
            $table->time('start_time')->nullable();
            $table->time('end_time')->nullable();
            $table->string('consider_time')->default(0)->nullable();
            $table->integer('hour')->default(0);
            $table->integer('max_over_time')->default(0);
            $table->foreignId('status_id')->constrained('statuses');
            $table->boolean('end_on_same_date')->default(1);
            $table->foreignId('company_id')->nullable()->constrained('companies')->default(1);
            $table->foreignId('branch_id')->nullable()->constrained('branches')->default(1);
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('duty_schedules');
    }
}
