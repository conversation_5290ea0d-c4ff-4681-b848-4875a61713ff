<?php

namespace App\Console\Commands;

use App\Models\Resignation;
use Illuminate\Console\Command;

class UserStatusCheck extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:user-status-check';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command is used to check and update user status, whether user is terminated, retired, laid off or resigned';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // Termination
        $terminations = \App\Models\Termination::with('employee:id,name,status')
            ->where('date', '<=', date('Y-m-d'))
            ->where('status', 'approved')
            ->where('is_executed', 0)
            ->get();

        foreach ($terminations->chunk(50) as $termination) {
            \App\Jobs\UserTerminationCheckJob::dispatch($termination);
        }

        // Resignation
        $resignations = \App\Models\Resignation::with('employee:id,name,status')
            ->where('date', '<=', date('Y-m-d'))
            ->where('status', 'approved')
            ->where('is_executed', 0)
            ->get();

        foreach ($resignations->chunk(50) as $resignation) {
            \App\Jobs\UserResignationCheckJob::dispatch($resignation);
        }

        // Promotion
        $promotions = \App\Models\Promotion::with([
            'employee:id,name,status',
            'fromPromotion:id,title',
            'toPromotion:id,title',
        ])
            ->where('date', '<=', date('Y-m-d'))
            ->where('status', 'approved')
            ->where('is_executed', 0)
            ->get();

        foreach ($promotions->chunk(50) as $promotion) {
            \App\Jobs\UserPromotionCheckJob::dispatch($promotion);
        }
    }
}
