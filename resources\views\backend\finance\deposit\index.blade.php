@extends('backend.layouts.app')
@section('title', @$data['title'])

@section('content')

<div class="ot-card">
    <!-- toolbar table start -->
    <div
        class="table-toolbar d-flex flex-wrap gap-2 flex-xl-row justify-content-center justify-content-xxl-between align-content-center pb-3">
        <div class="align-self-center">
            <div class="d-flex flex-wrap gap-2  flex-lg-row justify-content-center align-content-center">
                <!-- show per page -->
                <div class="align-self-center">
                    <label>
                        <span class="mr-8">{{ _trans('common.Show') }}</span>
                        <select class="form-select d-inline-block" id="entries" onchange="depositDatatable()">
                            @include('backend.partials.tableLimit')
                        </select>
                        <span class="ml-8">{{ _trans('common.Entries') }}</span>
                    </label>
                </div>



                <div class="align-self-center d-flex flex-wrap gap-2">
                    @if (hasPermission('deposit_create'))
                    <div class="align-self-center">
                        <a href="{{ @$data['create'] }}" role="button" class="btn-add">
                            <span><i class="fa-solid fa-plus"></i> </span>
                            <span class="d-none d-xl-inline"> {{ _trans('common.Create') }}</span>
                        </a>
                    </div>
                    @endif
                    <div class="align-self-center">
                        <button type="button" class="btn-daterange" id="daterange">
                            <span class="icon"><i class="fa-solid fa-calendar-days"></i>
                            </span>
                            <span class="d-none d-xl-inline">{{ _trans('common.Date Range') }}</span>
                        </button>
                        <input type="hidden" id="daterange-input" onchange="depositDatatable()">
                    </div>

                    <div class="align-self-center">
                        <div class="dropdown dropdown-designation" data-bs-title="{{ _trans('account.Accounts') }}">
                            <button type="button" class="btn-designation" data-bs-toggle="dropdown"
                                aria-expanded="false" data-bs-auto-close="false">
                                <span class="icon"><i class="fa-solid fa-user-shield"></i></span>

                                <span class="d-none d-xl-inline">{{ _trans('account.Accounts') }}</span>
                            </button>

                            <div class="dropdown-menu align-self-center ">
                                <select name="account" id="account" class="form-control pl-2 select2 w-100"
                                    onchange="depositDatatable()">
                                    <option value="0" disabled selected>
                                        {{ _trans('common.Choose Account') }} </option>
                                    @foreach ($data['accounts'] as $account)
                                    <option value="{{ $account->id }}">{{ $account->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                    </div>



                    <!-- search -->
                    <div class="align-self-center">
                        <div class="search-box d-flex">
                            <input class="form-control" placeholder="{{ _trans('common.Search') }}" name="search"
                                onkeyup="depositDatatable()" autocomplete="off">
                            <span class="icon"><i class="fa-solid fa-magnifying-glass"></i></span>
                        </div>
                    </div>

                    <!-- dropdown action -->
                    <div class="align-self-center">
                        <div class="dropdown dropdown-action">
                            <button type="button" class="btn-dropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fa-solid fa-ellipsis"></i>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end">
                                @if (hasPermission('deposit_delete'))
                                <li>
                                    <a class="dropdown-item" href="#"
                                        onclick="tableAction('delete', `{{ route('hrm.deposits.deleteData') }}`)">
                                        <span class="icon mr-16"><i class="fa-solid fa-trash-can"></i></span>
                                        {{ _trans('common.Delete') }} <span class="count">({{ _trans('common.0')
                                            }})</span>
                                    </a>
                                </li>
                                @endif
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- export -->
        @include('backend.partials.buttons')
    </div>
    <!-- toolbar table end -->
    <!--  table start -->
    <div class="table-responsive min-height-300">
        @include('backend.partials.table')
    </div>
    <!--  table end -->
</div>

@endsection
@section('script')
@include('backend.partials.table_js')
@endsection