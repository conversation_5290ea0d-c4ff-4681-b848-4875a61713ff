<?php

namespace App\Http\Controllers\Backend\Leave;

use App\Http\Controllers\Controller;
use App\Http\Requests\Leave\LeaveTypeRequest;
use App\Models\Hrm\Attendance\Attendance;
use App\Models\Hrm\Leave\LeaveType;
use App\Repositories\Hrm\Leave\LeaveTypeRepository;
use App\Traits\LogAttendance;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class LeaveTypeController extends Controller
{
    use LogAttendance;

    public function __construct(
        protected LeaveTypeRepository $repository
    ) {}

    public function loginLogTest()
    {
        $att = Attendance::where('user_id', Auth::user()->id)->latest()->first();

        $this->syncLog('back', $att);
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $data['title'] = _trans('common.Leave Type');
        $data['formTitle'] = _trans('common.Add New Leave Type');
        $data['collection'] = $this->repository->getPaginateData($request);

        if ($request->filled('id')) {
            $data['formTitle'] = _trans('common.Edit Leave Type');
            $data['leave'] = $this->repository->show($request->id);
        }

        return \view('backend.leave.type.index')->with($data);
    }

    /**
     * Store a newly created leave type in storage.
     *
     * @return \Illuminate\Http\RedirectResponse
     *
     * This method attempts to store a new leave type using the validated data
     * from the request. On success, it redirects to the leave type index with a
     * success message. If an exception occurs, it logs the error and redirects
     * back with an error message.
     */
    public function store(LeaveTypeRequest $request)
    {
        try {
            $this->repository->store($request->validated());

            return \redirect()->route('leave.type.index')->with('success', _trans('alert.Created Successfully'));
        } catch (\Exception $e) {
            return catchHandler($e);

            return \redirect()->back()->with('error', _trans('alert.Something went wrong'));
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @return \Illuminate\Http\RedirectResponse
     *
     * This method attempts to update a leave type using the validated data
     * from the request. On success, it redirects to the leave type index with a
     * success message. If an exception occurs, it logs the error and redirects
     * back with an error message.
     */
    public function update(LeaveTypeRequest $request, LeaveType $leaveType)
    {
        if (config('app.style') === 'demo' || env('APP_STYLE') === 'demo') {
            return redirect()->back()->with('error', _trans('alert.You are not allowed to perform the delete action in demo mode'));
        }

        try {
            $this->repository->update($request->validated(), $leaveType);

            return \redirect()->route('leave.type.index')->with('success', _trans('alert.Updated Successfully'));
        } catch (\Exception $e) {
            Log::error($e->getMessage());

            return \redirect()->back()->with('error', _trans('alert.Something went wrong'));
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @return \Illuminate\Http\RedirectResponse
     *
     * This method attempts to delete a leave type using the provided leave type model.
     * Upon successful deletion, it redirects to the leave type index route with a success message.
     * In case of an exception, it logs the error and redirects back with an error message.
     */
    public function destroy(LeaveType $leaveType)
    {
        if (config('app.style') === 'demo' || env('APP_STYLE') === 'demo') {
            return redirect()->back()->with('error', _trans('alert.You are not allowed to perform the delete action in demo mode'));
        }

        try {
            $this->repository->delete($leaveType);

            return \redirect()->route('leave.type.index')->with('success', _trans('alert.Deleted Successfully'));
        } catch (\Exception $e) {
            Log::error($e->getMessage());

            return \redirect()->back()->with('error', _trans('alert.Something went wrong'));
        }
    }
}
