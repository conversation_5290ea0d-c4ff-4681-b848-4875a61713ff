<?php

namespace App\Http\Controllers\Backend\Holiday;

use App\Http\Controllers\Controller;
use App\Http\Requests\Holiday\HolidayRequest;
use App\Models\Hrm\Holiday\Holiday;
use App\Repositories\Hrm\Holiday\HolidayRepository;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class HolidayController extends Controller
{
    public function __construct(
        protected HolidayRepository $repository
    ) {}

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $data['title'] = _trans('common.Holidays');
        $data['formTitle'] = _trans('common.Add New Holiday');
        $data['collection'] = $this->repository->getPaginateData($request);
        $data['departments'] = $this->repository->getDepartments();

        if ($request->filled('id')) {
            $data['formTitle'] = _trans('common.Edit Holiday');
            $data['holiday'] = $this->repository->show($request->id);
        }

        return \view('backend.holiday.index')->with($data);
    }

    /**
     * Store a newly created holiday in storage.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(HolidayRequest $request)
    {
        // dd($request);
        try {
            $this->repository->store($request->validated());

            return \redirect()->route('holiday.index')->with('success', _trans('alert.Created Successfully'));
        } catch (\Exception $e) {
            Log::error($e->getMessage());

            return \redirect()->back()->with('error', _trans('alert.Something went wrong'));
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(HolidayRequest $request, Holiday $holiday)
    {
        if (config('app.style') === 'demo' || env('APP_STYLE') === 'demo') {
            return redirect()->back()->with('error', _trans('alert.You are not allowed to perform the update action in demo mode'));
        }

        try {
            $this->repository->update($request->validated(), $holiday);

            return \redirect()->route('holiday.index')->with('success', _trans('alert.Updated Successfully'));
        } catch (\Exception $e) {
            Log::error($e->getMessage());

            return \redirect()->back()->with('error', _trans('alert.Something went wrong'));
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(Holiday $holiday)
    {
        if (config('app.style') === 'demo' || env('APP_STYLE') === 'demo') {
            return redirect()->back()->with('error', _trans('alert.You are not allowed to perform the delete action in demo mode'));
        }

        try {
            $this->repository->delete($holiday);

            return \redirect()->route('holiday.index')->with('success', _trans('alert.Deleted Successfully'));
        } catch (\Exception $e) {
            Log::error($e->getMessage());

            return \redirect()->back()->with('error', _trans('alert.Something went wrong'));
        }
    }
}
