<?php

namespace App\Helpers\CoreApp\Traits;

use App\Models\Company\Company;
use App\Models\Hrm\Department\Department;
use App\Models\Hrm\Designation\Designation;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

trait PermissionTrait
{
    public function customPermissions($role)
    {
        if ($role == 'superadmin') {
            return $this->adminPermissions();
        } elseif ($role == 'admin') {
            return $this->adminPermissions();
        } elseif ($role == 'staff') {
            return $this->staffPermissions();
        } elseif ($role == 'hr') {
            return $this->hrPermissions();
        } else {
            return [];
        }
    }

    public function adminPermissions(): array
    {
        return [
            // ================== Employee management ====================
            // Employee menu
            'employee_menu',

            // Department
            'department_read',
            'department_create',
            'department_update',
            'department_delete',

            // Designation
            'designation_read',
            'designation_create',
            'designation_update',
            'designation_delete',

            // Role
            'role_read',
            'role_create',
            'role_update',
            'role_delete',

            // Employee
            'user_read',
            'user_create',
            'user_update',
            'user_delete',
            'profile_read',
            'profile_update',
            'make_hr',
            'user_permission_update',
            'user_banned',
            'user_unbanned',
            'user_restore',
            'password_reset_mail',
            'password_update',
            'profile_image_view',

            // Official document menu
            'official_document_menu',

            // Official document type
            'official_document_type_read',
            'official_document_type_create',
            'official_document_type_update',
            'official_document_type_delete',

            // Official document request
            'official_document_request_read',
            'official_document_request_create',
            'official_document_request_update',
            'official_document_request_delete',

            // Write up menu
            'write_up_menu',

            // Complain
            'complain_read',
            'complain_create',
            'complain_update',
            'complain_delete',

            // Verbal Warning
            'verbal_warning_read',
            'verbal_warning_create',
            'verbal_warning_update',
            'verbal_warning_delete',

            // Performance
            'performance_read',
            'performance_create',
            'performance_update',
            'performance_delete',

            // ================== Attendance management ====================
            // Attendance menu
            'attendance_menu',

            // Attendance setting menu
            'attendance_setting_menu',

            // Weekend
            'weekend_read',
            'weekend_update',

            // Holiday
            'holiday_read',
            'holiday_create',
            'holiday_update',
            'holiday_delete',

            // Schedule
            'schedule_read',
            'schedule_create',
            'schedule_update',
            'schedule_delete',

            // Duty calender
            'duty_calendar_read',
            'duty_calendar_create',
            'duty_calendar_update',

            // Ip
            'ip_read',
            'ip_create',
            'ip_update',
            'ip_delete',

            // Location
            'location_read',
            'location_create',
            'location_update',
            'location_delete',

            // Attendance
            'attendance_read',
            'attendance_create',
            'attendance_update',
            'attendance_delete',

            // Break menu
            'break_menu',

            // Break
            'break_read',
            'break_create',
            'break_update',
            'generate_qr_code',

            // ================== Leave management ====================
            // Leave menu
            'leave_menu',

            // Leave type
            'leave_type_read',
            'leave_type_create',
            'leave_type_update',
            'leave_type_delete',

            // Leave assign
            'leave_assign_read',
            'leave_assign_create',
            'leave_assign_update',
            'leave_assign_delete',

            // Leave request
            'leave_request_read',
            'leave_request_create',
            'leave_request_update',
            'leave_request_delete',
            'leave_request_approve',
            'leave_request_reject',
            'leave_request_refer',

            // Leave balance
            'leave_balance_read',
            'leave_balance_create',
            'leave_balance_update',
            'leave_balance_delete',

            // ================== Payroll management ====================
            // Payroll menu
            'payroll_menu',

            // Commission
            'commission_read',
            'commission_create',
            'commission_update',
            'commission_delete',

            // Setup
            'setup_read',
            'set_contract',
            'set_commission',

            // Advance type
            'advance_type_read',
            'advance_type_create',
            'advance_type_update',
            'advance_type_delete',

            // Advance
            'advance_read',
            'advance_create',
            'advance_update',
            'advance_delete',
            'advance_approved',
            'advance_pay',

            // Salary generate
            'salary_read',
            'salary_create',
            'salary_update',
            'salary_delete',
            'salary_calculate',
            'salary_pay',
            'salary_payslip',

            // ================== Communication management ====================
            // Communication menu
            'communication_menu',

            // Conference
            'conference_read',
            'conference_create',
            'conference_update',
            'conference_delete',

            // Meeting
            'meeting_read',
            'meeting_create',
            'meeting_update',
            'meeting_delete',

            // Appointment
            'appointment_read',
            'appointment_create',
            'appointment_update',
            'appointment_delete',
            'appointment_approve',
            'appointment_reject',

            // Notice
            'notice_read',
            'notice_create',
            'notice_update',
            'notice_delete',

            // Bulletin
            'bulletin_read',
            'bulletin_create',
            'bulletin_update',
            'bulletin_delete',

            // ================== Office work management ====================
            // Office work menu
            'office_work_menu',

            // Client
            'client_read',
            'client_create',
            'client_update',
            'client_delete',

            // Project
            'project_read',
            'project_create',
            'project_update',
            'project_delete',
            'project_member_read',
            'project_member_delete',
            'project_complete',
            'project_payment',
            'project_activity_read',

            // Project file
            'project_file_read',
            'project_file_create',
            'project_file_update',
            'project_file_delete',
            'project_file_comment',

            // Project discussion
            'project_discussion_read',
            'project_discussion_create',
            'project_discussion_update',
            'project_discussion_delete',
            'project_discussion_comment',

            // Project note
            'project_note_read',
            'project_note_create',
            'project_note_update',
            'project_note_delete',

            // Task
            'task_read',
            'task_create',
            'task_update',
            'task_delete',
            'task_activity_read',
            'task_assign_read',
            'task_assign_delete',
            'task_complete',
            'task_member_read',

            // Task discussion
            'task_discussion_read',
            'task_discussion_create',
            'task_discussion_update',
            'task_discussion_delete',
            'task_discussion_comment',

            // Task note
            'task_note_read',
            'task_note_create',
            'task_note_update',
            'task_note_delete',

            // Task file
            'task_file_read',
            'task_file_create',
            'task_file_update',
            'task_file_delete',
            'task_file_comment',

            // Travel menu
            'travel_menu',

            // Travel plan
            'travel_plan_read',
            'travel_plan_create',
            'travel_plan_update',
            'travel_plan_delete',
            'travel_plan_approve',
            'travel_plan_reject',

            // Travel meeting
            'travel_meeting_read',
            'travel_meeting_create',
            'travel_meeting_update',
            'travel_meeting_delete',

            // Travel expense
            'travel_expense_read',
            'travel_expense_create',
            'travel_expense_update',
            'travel_expense_delete',
            'travel_expense_approve',

            // Travel workflow
            'travel_workflow_read',
            'travel_workflow_create',
            'travel_workflow_update',
            'travel_workflow_delete',
            'travel_workflow_approve',

            // Award menu
            'award_menu',

            // Award type
            'award_type_read',
            'award_type_create',
            'award_type_update',
            'award_type_delete',

            // Award
            'award_read',
            'award_create',
            'award_update',
            'award_delete',

            // Visit
            'visit_read',
            'visit_update',

            // ================== Report management ====================
            // Report menu
            'report_menu',

            // Report
            'live_tracking_read',
            'location_history_read',
            'location_timeline_read',
            'attendance_report_read',
            'payment_report_read',
            'visit_report_read',
            'leave_report_read',

            // ================== Account management ====================
            // Account menu
            'account_menu',
            'account_setting_menu',

            // Account
            'account_read',
            'account_create',
            'account_update',
            'account_delete',

            // Deposit
            'deposit_read',
            'deposit_create',
            'deposit_update',
            'deposit_delete',

            // Expense
            'expense_read',
            'expense_create',
            'expense_update',
            'expense_delete',
            'expense_approve',
            'expense_pay',

            // Transaction
            'transaction_read',
            'transaction_update',
            'transaction_delete',

            // Deposit category
            'deposit_category_read',
            'deposit_category_create',
            'deposit_category_update',
            'deposit_category_delete',

            // Expense category
            'expense_category_read',

            // Payment method
            'payment_method_read',
            'payment_method_create',
            'payment_method_update',
            'payment_method_delete',

            // ================== Setting management ====================
            // Setting menu
            'setting_menu',

            // Setting
            'general_settings_read',
            'general_settings_update',
            'email_setup_read',
            'email_setup_update',
            'firebase_setup_read',
            'firebase_setup_update',
            'geocoding_setup_read',
            'geocoding_setup_update',
            'pusher_setup_read',
            'pusher_setup_update',
            'storage_setup_read',
            'storage_setup_update',
            'database_backup',
            'about_system_read',
            'app_theme_setup_read',
            'app_theme_setup_update',
            'app_screen_setting',
            'user_device_read',
            'reset_device',

            // Currency
            'currency_read',
            'currency_create',
            'currency_update',
            'currency_delete',

            // Language
            'language_read',
            'language_create',
            'language_update',
            'language_delete',
            'language_setup',
            'language_make_default',

            // Branch
            'branch_read',
            'branch_create',
            'branch_update',
            'branch_delete',

            // Configuration
            'configuration_read',
            'configuration_update',

            // Activation
            'activation_read',
            'activation_update',

            // Branding
            'branding_read',
            'branding_update',

            // Profile
            'contract_profile',
            'attendance_profile',
            'notice_profile',
            'leave_request_profile',
            'visit_profile',
            'phonebook_profile',
            'appointment_profile',
            'support_ticket_profile',
            'advance_profile',
            'commission_profile',
            'salary_profile',
            'project_profile',
            'task_profile',
            'award_profile',
            'travel_profile',

            'tardy_menu',

            'tardy_group_read',
            'tardy_group_create',
            'tardy_group_update',
            'tardy_group_delete',

            'tardy_rule_read',
            'tardy_rule_create',
            'tardy_rule_update',
            'tardy_rule_delete',

            'tardy_rule_assign_read',
            'tardy_rule_assign_create',
            'tardy_rule_assign_update',
            'tardy_rule_assign_delete',

            'tardy_record_read',
            'tardy_record_create',
            'tardy_record_update',
            'tardy_record_delete',

            'tardy_request_read',
            'tardy_request_create',
            'tardy_request_update',
            'tardy_request_delete',
            'tardy_request_approve',
            'tardy_request_reject',

            'duty_calendar_read',
            'duty_calendar_create',
            'duty_calendar_update',

            'deduction_generate_read',
            'deduction_generate_create',
            'deduction_generate_update',
        ];
    }

    public function hrPermissions(): array
    {
        return [
            // ================== Employee management ====================
            // Employee menu
            'employee_menu',

            // Department
            'department_read',
            'department_create',
            'department_update',
            'department_delete',

            // Designation
            'designation_read',
            'designation_create',
            'designation_update',
            'designation_delete',

            // Role
            'role_read',
            'role_create',
            'role_update',
            'role_delete',

            // Employee
            'user_read',
            'user_create',
            'user_update',
            'user_delete',
            'profile_read',
            'profile_update',
            'make_hr',
            'user_permission_update',
            'user_banned',
            'user_unbanned',
            'user_restore',
            'password_reset_mail',
            'password_update',
            'profile_image_view',

            // Official document menu
            'official_document_menu',

            // Official document type
            'official_document_type_read',
            'official_document_type_create',
            'official_document_type_update',
            'official_document_type_delete',

            // Official document request
            'official_document_request_read',
            'official_document_request_create',
            'official_document_request_update',
            'official_document_request_delete',

            // Write up menu
            'write_up_menu',

            // Complain
            'complain_read',
            'complain_create',
            'complain_update',
            'complain_delete',

            // Verbal Warning
            'verbal_warning_read',
            'verbal_warning_create',
            'verbal_warning_update',
            'verbal_warning_delete',

            // Performance
            'performance_read',
            'performance_create',
            'performance_update',
            'performance_delete',

            // ================== Attendance management ====================
            // Attendance menu
            'attendance_menu',

            // Attendance setting menu
            'attendance_setting_menu',

            // Weekend
            'weekend_read',
            'weekend_update',

            // Holiday
            'holiday_read',
            'holiday_create',
            'holiday_update',
            'holiday_delete',

            // Schedule
            'schedule_read',
            'schedule_create',
            'schedule_update',
            'schedule_delete',

            // Duty calender
            'duty_calendar_read',
            'duty_calendar_create',
            'duty_calendar_update',

            // Ip
            'ip_read',
            'ip_create',
            'ip_update',
            'ip_delete',

            // Location
            'location_read',
            'location_create',
            'location_update',
            'location_delete',

            // Attendance
            'attendance_read',
            'attendance_create',
            'attendance_update',
            'attendance_delete',

            // Break menu
            'break_menu',

            // Break
            'break_read',
            'break_create',
            'break_update',
            'generate_qr_code',

            // ================== Leave management ====================
            // Leave menu
            'leave_menu',

            // Leave type
            'leave_type_read',
            'leave_type_create',
            'leave_type_update',
            'leave_type_delete',

            // Leave assign
            'leave_assign_read',
            'leave_assign_create',
            'leave_assign_update',
            'leave_assign_delete',

            // Leave request
            'leave_request_read',
            'leave_request_create',
            'leave_request_update',
            'leave_request_delete',
            'leave_request_approve',
            'leave_request_reject',
            'leave_request_refer',

            // Leave balance
            'leave_balance_read',
            'leave_balance_create',
            'leave_balance_update',
            'leave_balance_delete',

            // ================== Payroll management ====================
            // Payroll menu
            'payroll_menu',

            // Commission
            'commission_read',
            'commission_create',
            'commission_update',
            'commission_delete',

            // Setup
            'setup_read',
            'set_contract',
            'set_commission',

            // Advance type
            'advance_type_read',
            'advance_type_create',
            'advance_type_update',
            'advance_type_delete',

            // Advance
            'advance_read',
            'advance_create',
            'advance_update',
            'advance_delete',
            'advance_approved',
            'advance_pay',

            // Salary generate
            'salary_read',
            'salary_create',
            'salary_update',
            'salary_delete',
            'salary_calculate',
            'salary_pay',
            'salary_payslip',

            // ================== Communication management ====================
            // Communication menu
            'communication_menu',

            // Conference
            'conference_read',
            'conference_create',
            'conference_update',
            'conference_delete',

            // Meeting
            'meeting_read',
            'meeting_create',
            'meeting_update',
            'meeting_delete',

            // Appointment
            'appointment_read',
            'appointment_create',
            'appointment_update',
            'appointment_delete',
            'appointment_approve',
            'appointment_reject',

            // Notice
            'notice_read',
            'notice_create',
            'notice_update',
            'notice_delete',

            // Bulletin
            'bulletin_read',
            'bulletin_create',
            'bulletin_update',
            'bulletin_delete',

            // ================== Office work management ====================
            // Office work menu
            'office_work_menu',

            // Client
            'client_read',
            'client_create',
            'client_update',
            'client_delete',

            // Project
            'project_read',
            'project_create',
            'project_update',
            'project_delete',
            'project_member_read',
            'project_member_delete',
            'project_complete',
            'project_payment',
            'project_activity_read',

            // Project file
            'project_file_read',
            'project_file_create',
            'project_file_update',
            'project_file_delete',
            'project_file_comment',

            // Project discussion
            'project_discussion_read',
            'project_discussion_create',
            'project_discussion_update',
            'project_discussion_delete',
            'project_discussion_comment',

            // Project note
            'project_note_read',
            'project_note_create',
            'project_note_update',
            'project_note_delete',

            // Task
            'task_read',
            'task_create',
            'task_update',
            'task_delete',
            'task_activity_read',
            'task_assign_read',
            'task_assign_delete',
            'task_complete',
            'task_member_read',

            // Task discussion
            'task_discussion_read',
            'task_discussion_create',
            'task_discussion_update',
            'task_discussion_delete',
            'task_discussion_comment',

            // Task note
            'task_note_read',
            'task_note_create',
            'task_note_update',
            'task_note_delete',

            // Task file
            'task_file_read',
            'task_file_create',
            'task_file_update',
            'task_file_delete',
            'task_file_comment',

            // Travel menu
            'travel_menu',

            // Travel plan
            'travel_plan_read',
            'travel_plan_create',
            'travel_plan_update',
            'travel_plan_delete',
            'travel_plan_approve',
            'travel_plan_reject',

            // Travel meeting
            'travel_meeting_read',
            'travel_meeting_create',
            'travel_meeting_update',
            'travel_meeting_delete',

            // Travel expense
            'travel_expense_read',
            'travel_expense_create',
            'travel_expense_update',
            'travel_expense_delete',
            'travel_expense_approve',

            // Travel workflow
            'travel_workflow_read',
            'travel_workflow_create',
            'travel_workflow_update',
            'travel_workflow_delete',
            'travel_workflow_approve',

            // Award menu
            'award_menu',

            // Award type
            'award_type_read',
            'award_type_create',
            'award_type_update',
            'award_type_delete',

            // Award
            'award_read',
            'award_create',
            'award_update',
            'award_delete',

            // Visit
            'visit_read',
            'visit_update',

            // ================== Report management ====================
            // Report menu
            'report_menu',

            // Report
            'live_tracking_read',
            'location_history_read',
            'location_timeline_read',
            'attendance_report_read',
            'payment_report_read',
            'visit_report_read',
            'leave_report_read',

            // ================== Account management ====================
            // Account menu
            'account_menu',
            'account_setting_menu',

            // Account
            'account_read',
            'account_create',
            'account_update',
            'account_delete',

            // Deposit
            'deposit_read',
            'deposit_create',
            'deposit_update',
            'deposit_delete',

            // Expense
            'expense_read',
            'expense_create',
            'expense_update',
            'expense_delete',
            'expense_approve',
            'expense_pay',

            // Transaction
            'transaction_read',
            'transaction_update',
            'transaction_delete',

            // Deposit category
            'deposit_category_read',
            'deposit_category_create',
            'deposit_category_update',
            'deposit_category_delete',

            // Expense category
            'expense_category_read',

            // Payment method
            'payment_method_read',
            'payment_method_create',
            'payment_method_update',
            'payment_method_delete',

            // ================== Setting management ====================
            // Setting menu
            'setting_menu',

            // Setting
            'general_settings_read',
            'general_settings_update',
            'email_setup_read',
            'email_setup_update',
            'firebase_setup_read',
            'firebase_setup_update',
            'geocoding_setup_read',
            'geocoding_setup_update',
            'pusher_setup_read',
            'pusher_setup_update',
            'storage_setup_read',
            'storage_setup_update',
            'database_backup',
            'about_system_read',
            'app_theme_setup_read',
            'app_theme_setup_update',
            'app_screen_setting',

            // Currency
            'currency_read',
            'currency_create',
            'currency_update',
            'currency_delete',

            // Language
            'language_read',
            'language_create',
            'language_update',
            'language_delete',
            'language_setup',
            'language_make_default',

            // Configuration
            'configuration_read',
            'configuration_update',

            // Activation
            'activation_read',
            'activation_update',

            // Branding
            'branding_read',
            'branding_update',

            // Profile
            'contract_profile',
            'attendance_profile',
            'notice_profile',
            'leave_request_profile',
            'visit_profile',
            'phonebook_profile',
            'appointment_profile',
            'support_ticket_profile',
            'advance_profile',
            'commission_profile',
            'salary_profile',
            'project_profile',
            'task_profile',
            'award_profile',
            'travel_profile',

            'tardy_menu',

            'tardy_group_read',
            'tardy_group_create',
            'tardy_group_update',
            'tardy_group_delete',

            'tardy_rule_read',
            'tardy_rule_create',
            'tardy_rule_update',
            'tardy_rule_delete',

            'tardy_rule_assign_read',
            'tardy_rule_assign_create',
            'tardy_rule_assign_update',
            'tardy_rule_assign_delete',

            'tardy_record_read',
            'tardy_record_create',
            'tardy_record_update',
            'tardy_record_delete',

            'tardy_request_read',
            'tardy_request_create',
            'tardy_request_update',
            'tardy_request_delete',
            'tardy_request_approve',
            'tardy_request_reject',

            'duty_calendar_read',
            'duty_calendar_create',
            'duty_calendar_update',

            'deduction_generate_read',
        ];
    }

    public function staffPermissions(): array
    {
        return [
            // ================== Employee management ====================
            // Employee menu
            'employee_menu',

            // Employee
            'profile_read',
            'profile_update',
            'user_permission_update',
            'password_reset_mail',
            'password_update',
            'profile_image_view',

            // Official document menu
            'official_document_menu',

            // Official document request
            'official_document_request_read',
            'official_document_request_create',
            'official_document_request_update',
            'official_document_request_delete',

            // Write up menu
            'write_up_menu',

            // Complain
            'complain_read',
            'complain_create',
            'complain_update',

            // Verbal Warning
            'verbal_warning_read',
            'verbal_warning_create',
            'verbal_warning_update',
            'verbal_warning_delete',

            // Performance
            'performance_read',
            'performance_create',
            'performance_update',
            'performance_delete',

            // ================== Attendance management ====================
            // Attendance menu
            'attendance_menu',

            // Attendance setting menu
            'attendance_setting_menu',

            // Duty calender
            'duty_calendar_read',

            // Attendance
            'attendance_read',
            'attendance_create',

            // Break menu
            'break_menu',

            // Break
            'break_read',
            'break_create',
            'break_update',

            // ================== Leave management ====================
            // Leave menu
            'leave_menu',

            // Leave request
            'leave_request_read',
            'leave_request_create',
            'leave_request_delete',

            // Leave balance
            'leave_balance_read',

            // ================== Payroll management ====================
            // Payroll menu
            'payroll_menu',

            // Advance
            'advance_read',

            // Salary generate
            'salary_read',

            // ================== Communication management ====================
            // Communication menu
            'communication_menu',

            // Conference
            'conference_read',
            'conference_create',
            'conference_update',
            'conference_delete',

            // Meeting
            'meeting_read',
            'meeting_create',
            'meeting_update',
            'meeting_delete',

            // Appointment
            'appointment_read',
            'appointment_create',
            'appointment_update',
            'appointment_delete',

            // Notice
            'notice_read',

            // Bulletin
            'bulletin_read',

            // ================== Office work management ====================
            // Office work menu
            'office_work_menu',

            // Project
            'project_read',
            'project_create',
            'project_update',
            'project_delete',
            'project_member_read',
            'project_member_delete',
            'project_complete',
            'project_payment',
            'project_activity_read',

            // Project file
            'project_file_read',
            'project_file_create',
            'project_file_update',
            'project_file_delete',
            'project_file_comment',

            // Project discussion
            'project_discussion_read',
            'project_discussion_create',
            'project_discussion_update',
            'project_discussion_delete',
            'project_discussion_comment',

            // Project note
            'project_note_read',
            'project_note_create',
            'project_note_update',
            'project_note_delete',

            // Task
            'task_read',
            'task_create',
            'task_update',
            'task_delete',
            'task_activity_read',
            'task_assign_read',
            'task_assign_delete',
            'task_complete',
            'task_member_read',

            // Task discussion
            'task_discussion_read',
            'task_discussion_create',
            'task_discussion_update',
            'task_discussion_delete',
            'task_discussion_comment',

            // Task note
            'task_note_read',
            'task_note_create',
            'task_note_update',
            'task_note_delete',

            // Task file
            'task_file_read',
            'task_file_create',
            'task_file_update',
            'task_file_delete',
            'task_file_comment',

            // Travel menu
            'travel_menu',

            // Travel plan
            'travel_plan_read',
            'travel_plan_create',
            'travel_plan_update',
            'travel_plan_delete',

            // Travel meeting
            'travel_meeting_read',
            'travel_meeting_create',
            'travel_meeting_update',
            'travel_meeting_delete',

            // Travel expense
            'travel_expense_read',
            'travel_expense_create',
            'travel_expense_update',
            'travel_expense_delete',

            // Travel workflow
            'travel_workflow_read',
            'travel_workflow_create',
            'travel_workflow_update',
            'travel_workflow_delete',

            // Award menu
            'award_menu',

            // Award
            'award_read',

            // Visit
            'visit_read',
            'visit_update',

            // ================== Profile management ====================
            // Profile
            'contract_profile',
            'attendance_profile',
            'notice_profile',
            'leave_request_profile',
            'visit_profile',
            'phonebook_profile',
            'appointment_profile',
            'support_ticket_profile',
            'advance_profile',
            'commission_profile',
            'salary_profile',
            'project_profile',
            'task_profile',
            'award_profile',
            'travel_profile',

            'tardy_menu',

            'tardy_record_read',
            'tardy_record_create',
            'tardy_record_update',

            'tardy_request_read',
            'tardy_request_create',
            'tardy_request_update',

            'duty_calendar_read',

            'deduction_generate_read',
        ];
    }

    public function adminRolePermissions(): array
    {
        return [
            // Employee
            'employee' => [
                'menu_order' => 2,
                'permissions' => [
                    'employee_' => [
                        // Employee menu
                        'employee_menu' => [
                            'employee_menu' => 'employee_menu',
                        ],

                        // Department
                        'department' => [
                            'read' => 'department_read',
                            'create' => 'department_create',
                            'update' => 'department_update',
                            'delete' => 'department_delete',
                        ],

                        // Designation
                        'designation' => [
                            'read' => 'designation_read',
                            'create' => 'designation_create',
                            'update' => 'designation_update',
                            'delete' => 'designation_delete',
                        ],

                        // Role
                        'role' => [
                            'read' => 'role_read',
                            'create' => 'role_create',
                            'update' => 'role_update',
                            'delete' => 'role_delete',
                        ],

                        // User
                        'employee' => [
                            'read' => 'user_read',
                            'create' => 'user_create',
                            'update' => 'user_update',
                            'profile_read' => 'profile_read',
                            'profile_update' => 'profile_update',
                            'delete' => 'user_delete',
                            'user_permission' => 'user_permission_update',
                            'user_banned' => 'user_banned',
                            'user_unbanned' => 'user_unbanned',
                            'user_restore' => 'user_restore',
                            'make_hr' => 'make_hr',
                            'profile_image_view' => 'profile_image_view',
                            'password_reset_mail' => 'password_reset_mail',
                            'password_update' => 'password_update',
                            'bulk_upload_menu' => 'bulk_upload_menu',
                            'bulk_upload' => 'bulk_upload',
                        ],

                        // Performance
                        'performance' => [
                            'read' => 'performance_read',
                            'create' => 'performance_create',
                            'update' => 'performance_update',
                            'delete' => 'performance_delete',
                        ],
                    ],

                    'official_document' => [
                        // Official document menu
                        'official_document_menu' => [
                            'official_document_menu' => 'official_document_menu',
                        ],

                        // Official document type
                        'official_document_type' => [
                            'read' => 'official_document_type_read',
                            'create' => 'official_document_type_create',
                            'update' => 'official_document_type_update',
                            'delete' => 'official_document_type_delete',
                        ],

                        // official document request
                        'official_document_request' => [
                            'read' => 'official_document_request_read',
                            'create' => 'official_document_request_create',
                            'update' => 'official_document_request_update',
                            'delete' => 'official_document_request_delete',
                        ],
                    ],

                    'write_up' => [
                        // Write up menu
                        'write_up_menu' => [
                            'write_up_menu' => 'write_up_menu',
                        ],

                        // Complain
                        'complain' => [
                            'read' => 'complain_read',
                            'create' => 'complain_create',
                            'update' => 'complain_update',
                            'delete' => 'complain_delete',
                        ],

                        // Verbal Warning
                        'verbal_warning' => [
                            'read' => 'verbal_warning_read',
                            'create' => 'verbal_warning_create',
                            'update' => 'verbal_warning_update',
                            'delete' => 'verbal_warning_delete',
                        ],
                    ],
                ],
            ],

            // Attendance
            'attendance' => [
                'menu_order' => 3,
                'permissions' => [
                    // All
                    'attendance_' => [
                        // Attendance menu
                        'attendance_menu' => [
                            'attendance_menu' => 'attendance_menu',
                        ],

                        // Attendance
                        'attendance' => [
                            'read' => 'attendance_read',
                            'create' => 'attendance_create',
                            'update' => 'attendance_update',
                            'delete' => 'attendance_delete',
                        ],
                    ],

                    // Tardy
                    // 'tardy' => [
                    //     // Tardy Menu
                    //     'tardy_menu' => [
                    //         'tardy_menu' => 'tardy_menu',
                    //     ],

                    //     // Tardy group
                    //     'tardy_group' => [
                    //         'read'    => 'tardy_group_read',
                    //         'create'  => 'tardy_group_create',
                    //         'update'  => 'tardy_group_update',
                    //         'delete'  => 'tardy_group_delete',
                    //     ],

                    //     // Tardy Rule
                    //     'tardy_rule' => [
                    //         'read'    => 'tardy_rule_read',
                    //         'create'  => 'tardy_rule_create',
                    //         'update'  => 'tardy_rule_update',
                    //         'delete'  => 'tardy_rule_delete',
                    //     ],

                    //     // Tardy Rule assign
                    //     'tardy_rule_assign' => [
                    //         'read'    => 'tardy_rule_assign_read',
                    //         'create'  => 'tardy_rule_assign_create',
                    //         'update'  => 'tardy_rule_assign_update',
                    //         'delete'  => 'tardy_rule_assign_delete',
                    //     ],

                    //     // Tardy Record
                    //     'tardy_record' => [
                    //         'read'    => 'tardy_record_read',
                    //         'create'  => 'tardy_record_create',
                    //         'update'  => 'tardy_record_update',
                    //         'delete'  => 'tardy_record_delete',
                    //     ],

                    //     // Tardy Appeal
                    //     'tardy_request' => [
                    //         'read'    => 'tardy_request_read',
                    //         'create'  => 'tardy_request_create',
                    //         'update'  => 'tardy_request_update',
                    //         'delete'  => 'tardy_request_delete',
                    //         'approve' => 'tardy_request_approve',
                    //         'reject'  => 'tardy_request_reject',
                    //     ],
                    // ],

                    // Setting
                    'setting' => [
                        // Setting menu
                        'attendance_setting_menu' => [
                            'attendance_setting_menu' => 'attendance_setting_menu',
                        ],

                        // Weekend
                        'weekend' => [
                            'read' => 'weekend_read',
                            'update' => 'weekend_update',
                        ],

                        // Holiday
                        'holiday' => [
                            'read' => 'holiday_read',
                            'create' => 'holiday_create',
                            'update' => 'holiday_update',
                            'delete' => 'holiday_delete',
                        ],

                        // Schedule
                        'schedule' => [
                            'read' => 'schedule_read',
                            'create' => 'schedule_create',
                            'update' => 'schedule_update',
                            'delete' => 'schedule_delete',
                        ],

                        // Duty calender
                        'duty_calendar' => [
                            'read' => 'duty_calendar_read',
                            'create' => 'duty_calendar_create',
                            'update' => 'duty_calendar_update',
                        ],

                        // Ip wishlist
                        'ip_wishlist' => [
                            'read' => 'ip_read',
                            'create' => 'ip_create',
                            'update' => 'ip_update',
                            'delete' => 'ip_delete',
                        ],

                        // Location
                        'location' => [
                            'read' => 'location_read',
                            'create' => 'location_create',
                            'update' => 'location_update',
                            'delete' => 'location_delete',
                        ],
                    ],

                    // Break
                    'break' => [
                        // Break menu
                        'break_menu' => [
                            'break_menu' => 'break_menu',
                        ],

                        // Break
                        'break' => [
                            'read' => 'break_read',
                            'create' => 'break_create',
                            'update' => 'break_update',
                            'qr_code' => 'generate_qr_code',
                        ],
                    ],
                ],
            ],

            'leave' => [
                'menu_order' => 5,
                'permissions' => [
                    // All
                    'leave' => [
                        // Leave menu
                        'leave_menu' => [
                            'leave_menu' => 'leave_menu',
                        ],

                        // Leave type
                        'leave_type' => [
                            'read' => 'leave_type_read',
                            'create' => 'leave_type_create',
                            'update' => 'leave_type_update',
                            'delete' => 'leave_type_delete',
                        ],

                        // Leave assign
                        'leave_assign' => [
                            'read' => 'leave_assign_read',
                            'create' => 'leave_assign_create',
                            'update' => 'leave_assign_update',
                            'delete' => 'leave_assign_delete',
                        ],

                        // Leave request
                        'leave_request' => [
                            'read' => 'leave_request_read',
                            'create' => 'leave_request_create',
                            'update' => 'leave_request_update',
                            'delete' => 'leave_request_delete',
                            'approve' => 'leave_request_approve',
                            'reject' => 'leave_request_reject',
                            'refer' => 'leave_request_refer',
                        ],

                        // Leave balance
                        'leave_balance' => [
                            'read' => 'leave_balance_read',
                            'create' => 'leave_balance_create',
                            'update' => 'leave_balance_update',
                            'delete' => 'leave_balance_delete',
                        ],

                    ],
                ],
            ],

            // Payroll
            'payroll' => [
                'menu_order' => 6,
                'permissions' => [
                    'payroll' => [
                        // Payroll menu
                        'payroll_menu' => [
                            'payroll_menu' => 'payroll_menu',
                        ],

                        // Commissions
                        'commission' => [
                            'read' => 'commission_read',
                            'create' => 'commission_create',
                            'update' => 'commission_update',
                            'delete' => 'commission_delete',
                        ],

                        // Setup
                        'setup' => [
                            'read' => 'setup_read',
                            'set_contract' => 'set_contract',
                            'set_commission' => 'set_commission',
                        ],

                        // Advance type
                        'advance_type' => [
                            'read' => 'advance_type_read',
                            'create' => 'advance_type_create',
                            'update' => 'advance_type_update',
                            'delete' => 'advance_type_delete',
                        ],

                        // Advance
                        'advance' => [
                            'read' => 'advance_read',
                            'create' => 'advance_create',
                            'update' => 'advance_update',
                            'delete' => 'advance_delete',
                            'approved' => 'advance_approved',
                            'pay' => 'advance_pay',
                        ],

                        // Salary generate
                        'deduction_generate' => [
                            'read' => 'deduction_generate_read',
                            'create' => 'deduction_generate_create',
                            'update' => 'deduction_generate_update',
                        ],

                        // Salary generate
                        'salary_generate' => [
                            'read' => 'salary_read',
                            'create' => 'salary_create',
                            'update' => 'salary_update',
                            'delete' => 'salary_delete',
                            'calculate' => 'salary_calculate',
                            'payslip' => 'salary_pay',
                            'payslip' => 'salary_payslip',
                        ],
                    ],
                ],
            ],

            // Communication
            'communication' => [
                'menu_order' => 7,
                'permissions' => [
                    'communication' => [
                        // Communication menu
                        'communication_menu' => [
                            'communication_menu' => 'communication_menu',
                        ],

                        // Conference
                        'conference' => [
                            'read' => 'conference_read',
                            'create' => 'conference_create',
                            'update' => 'conference_update',
                            'delete' => 'conference_delete',
                        ],

                        // Meeting
                        'meeting' => [
                            'read' => 'meeting_read',
                            'create' => 'meeting_create',
                            'update' => 'meeting_update',
                            'delete' => 'meeting_delete',
                        ],

                        // Appointment
                        'appointment' => [
                            'read' => 'appointment_read',
                            'create' => 'appointment_create',
                            'update' => 'appointment_update',
                            'delete' => 'appointment_delete',
                            'approve' => 'appointment_approve',
                            'reject' => 'appointment_reject',
                        ],

                        // Notice
                        'notice' => [
                            'read' => 'notice_read',
                            'create' => 'notice_create',
                            'update' => 'notice_update',
                            'delete' => 'notice_delete',
                        ],

                        // Bulletin
                        'bulletin' => [
                            'read' => 'bulletin_read',
                            'create' => 'bulletin_create',
                            'update' => 'bulletin_update',
                            'delete' => 'bulletin_delete',
                        ],
                    ],
                ],
            ],

            'office_work' => [
                'menu_order' => 8,
                'permissions' => [
                    'office_work' => [
                        // Office work menu
                        'office_work_menu' => [
                            'office_work_menu' => 'office_work_menu',
                        ],

                        // Client menu
                        'client' => [
                            'read' => 'client_read',
                            'create' => 'client_create',
                            'update' => 'client_update',
                            'delete' => 'client_delete',
                        ],

                        // Project
                        'project' => [
                            'read' => 'project_read',
                            'create' => 'project_create',
                            'update' => 'project_update',
                            'delete' => 'project_delete',
                            'project_member_read' => 'project_member_read',
                            'project_member_delete' => 'project_member_delete',
                            'project_complete' => 'project_complete',
                            'project_payment' => 'project_payment',
                            'project_activity_read' => 'project_activity_read',
                        ],

                        // Project file
                        'project_file' => [
                            'read' => 'project_file_read',
                            'create' => 'project_file_create',
                            'update' => 'project_file_update',
                            'delete' => 'project_file_delete',
                            'comment' => 'project_file_comment',
                        ],

                        // Project discussion
                        'project_discussion' => [
                            'read' => 'project_discussion_read',
                            'create' => 'project_discussion_create',
                            'update' => 'project_discussion_update',
                            'delete' => 'project_discussion_delete',
                            'comment' => 'project_discussion_comment',
                        ],

                        // Project discussion
                        'project_note' => [
                            'read' => 'project_note_read',
                            'create' => 'project_note_create',
                            'update' => 'project_note_update',
                            'delete' => 'project_note_delete',
                        ],

                        // Task
                        'task' => [
                            'read' => 'task_read',
                            'create' => 'task_create',
                            'update' => 'task_update',
                            'delete' => 'task_delete',
                            'task_activity' => 'task_activity_read',
                            'task_assign' => 'task_assign_read',
                            'task_assign_delete' => 'task_assign_delete',
                            'task_complete' => 'task_complete',
                            'task_member' => 'task_member_read',
                        ],

                        // Task discussion
                        'task_discussion' => [
                            'read' => 'task_discussion_read',
                            'create' => 'task_discussion_create',
                            'update' => 'task_discussion_update',
                            'delete' => 'task_discussion_delete',
                            'comment' => 'task_discussion_comment',
                        ],

                        // Task file
                        'task_file' => [
                            'read' => 'task_file_read',
                            'create' => 'task_file_create',
                            'update' => 'task_file_update',
                            'delete' => 'task_file_delete',
                            'comment' => 'task_file_comment',
                        ],

                        // Task note
                        'task_note' => [
                            'read' => 'task_note_read',
                            'create' => 'task_note_create',
                            'update' => 'task_note_update',
                            'delete' => 'task_note_delete',
                        ],

                        // visit
                        'visit' => [
                            'read' => 'visit_read',
                            'update' => 'visit_update',
                        ],

                        // contact
                        'contact' => [
                            'read' => 'contact_read',
                            'create' => 'contact_create',
                        ],

                        // Support Ticket
                        'support_ticket' => [
                            'read' => 'support_ticket_read',
                            'create' => 'support_ticket_create',
                            'reply' => 'support_ticket_reply',
                            'delete' => 'support_ticket_delete',
                        ],
                    ],

                    // Travel
                    'travels' => [
                        // Travel menu
                        'travel_menu' => [
                            'travel_menu' => 'travel_menu',
                        ],

                        // Travel plan
                        'travel_plan' => [
                            'read' => 'travel_plan_read',
                            'create' => 'travel_plan_create',
                            'update' => 'travel_plan_update',
                            'delete' => 'travel_plan_delete',
                            'approve' => 'travel_plan_approve',
                            'reject' => 'travel_plan_reject',
                        ],

                        // Travel plan meeting
                        'travel_meeting' => [
                            'read' => 'travel_meeting_read',
                            'create' => 'travel_meeting_create',
                            'update' => 'travel_meeting_update',
                            'delete' => 'travel_meeting_delete',
                        ],

                        // Travel plan meeting
                        'travel_expense' => [
                            'read' => 'travel_expense_read',
                            'create' => 'travel_expense_create',
                            'update' => 'travel_expense_update',
                            'delete' => 'travel_expense_delete',
                            'approved' => 'travel_expense_approve',
                        ],

                        // Travel
                        'travel_workflow' => [
                            'read' => 'travel_workflow_read',
                            'create' => 'travel_workflow_create',
                            'update' => 'travel_workflow_update',
                            'delete' => 'travel_workflow_delete',
                            'approve' => 'travel_workflow_approve',
                        ],
                    ],

                    // Award
                    'awards' => [
                        // awards menu
                        'awards_menu' => [
                            'award_menu' => 'award_menu',
                        ],

                        // Award type
                        'award_type' => [
                            'read' => 'award_type_read',
                            'create' => 'award_type_create',
                            'update' => 'award_type_update',
                            'delete' => 'award_type_delete',
                        ],

                        // Award
                        'award' => [
                            'read' => 'award_read',
                            'create' => 'award_create',
                            'update' => 'award_update',
                            'delete' => 'award_delete',
                        ],
                    ],
                ],
            ],

            'report' => [
                'menu_order' => 9,
                'permissions' => [
                    'report_' => [
                        // Menu
                        'report_menu' => [
                            'report_menu' => 'report_menu',
                        ],

                        // Report
                        'report' => [
                            'live_tracking' => 'live_tracking_read',
                            'location_timeline' => 'location_timeline_read',
                            'attendance_report' => 'attendance_report_read',
                            'leave_report' => 'leave_report_read',
                            'payment_report' => 'payment_report_read',
                            'visit_report' => 'visit_report_read',
                            'location_history' => 'location_history_read',
                        ],
                    ],
                ],
            ],

            'account' => [
                'menu_order' => 10,
                'permissions' => [
                    'account_' => [
                        // Account menu
                        'account_menu' => [
                            'account_menu' => 'account_menu',
                        ],

                        // account
                        'account' => [
                            'read' => 'account_read',
                            'create' => 'account_create',
                            'update' => 'account_update',
                            'delete' => 'account_delete',
                        ],

                        // deposit
                        'deposit' => [
                            'read' => 'deposit_read',
                            'create' => 'deposit_create',
                            'update' => 'deposit_update',
                            'delete' => 'deposit_delete',
                        ],

                        // expense
                        'expense' => [
                            'read' => 'expense_read',
                            'create' => 'expense_create',
                            'update' => 'expense_update',
                            'delete' => 'expense_delete',
                            'approve' => 'expense_approve',
                            'pay' => 'expense_pay',
                        ],

                        // transaction
                        'transaction' => [
                            'read' => 'transaction_read',
                            'update' => 'transaction_update',
                            'delete' => 'transaction_delete',
                        ],
                    ],

                    // Account setting
                    'account_setting' => [
                        // Account setting menu
                        'account_setting_menu' => [
                            'account_setting_menu' => 'account_setting_menu',
                        ],

                        // deposit_category
                        'deposit_category' => [
                            'read' => 'deposit_category_read',
                            'create' => 'deposit_category_create',
                            'update' => 'deposit_category_update',
                            'delete' => 'deposit_category_delete',
                        ],

                        // deposit_category
                        'expense_category' => [
                            'read' => 'expense_category_read',
                        ],

                        // Payment method
                        'payment_method' => [
                            'read' => 'payment_method_read',
                            'create' => 'payment_method_create',
                            'update' => 'payment_method_update',
                            'delete' => 'payment_method_delete',
                        ],
                    ],
                ],
            ],

            'setting' => [
                'menu_order' => 11,
                'permissions' => [
                    'setting' => [
                        'setting_menu' => [
                            'setting_menu' => 'setting_menu',
                        ],

                        // General setting
                        'general_setting' => [
                            'general_settings_read' => 'general_settings_read',
                            'general_settings_update' => 'general_settings_update',
                            'email_setup_read' => 'email_setup_read',
                            'email_setup_update' => 'email_setup_update',
                            'firebase_setup_read' => 'firebase_setup_read',
                            'firebase_setup_update' => 'firebase_setup_update',
                            'geocoding_setup_read' => 'geocoding_setup_read',
                            'geocoding_setup_update' => 'geocoding_setup_update',
                            'pusher_setup_read' => 'pusher_setup_read',
                            'pusher_setup_update' => 'pusher_setup_update',
                            'storage_setup_read' => 'storage_setup_read',
                            'storage_setup_update' => 'storage_setup_update',
                            'database_backup' => 'database_backup',
                            'about_system_read' => 'about_system_read',
                            'app_theme_setup_read' => 'app_theme_setup_read',
                            'app_theme_setup_update' => 'app_theme_setup_update',
                            'app_setting' => 'app_screen_setting',
                            'user_device_read' => 'user_device_read',
                            'reset_device' => 'reset_device',
                        ],

                        // Content
                        'content' => [
                            'read' => 'content_read',
                            'update' => 'content_update',
                        ],

                        // Currency
                        'currency' => [
                            'read' => 'currency_read',
                            'create' => 'currency_create',
                            'update' => 'currency_update',
                            'delete' => 'currency_delete',
                        ],

                        // Language
                        'language' => [
                            'read' => 'language_read',
                            'create' => 'language_create',
                            'update' => 'language_update',
                            'delete' => 'language_delete',
                            'setup' => 'language_setup',
                            'make_default' => 'language_make_default',
                        ],

                        // Language
                        'branch' => [
                            'read' => 'branch_read',
                            'create' => 'branch_create',
                            'update' => 'branch_update',
                            'delete' => 'branch_delete',
                        ],

                        // configuration
                        'configuration' => [
                            'configuration_read' => 'configuration_read',
                            'configuration_update' => 'configuration_update',
                        ],

                        // activation
                        'activation' => [
                            'activation_read' => 'activation_read',
                            'activation_update' => 'activation_update',
                        ],

                        // branding
                        'branding' => [
                            'read' => 'branding_read',
                            'update' => 'branding_update',
                        ],
                    ],
                ],
            ],

            'profile' => [
                'menu_order' => 12,
                'permissions' => [
                    'profile_' => [
                        'profile_menus' => [
                            'attendance_profile' => 'attendance_profile',
                            'contract_profile' => 'contract_profile',
                            'notice_profile' => 'notice_profile',
                            'leave_request_profile' => 'leave_request_profile',
                            'visit_profile' => 'visit_profile',
                            'phonebook_profile' => 'phonebook_profile',
                            'appointment_profile' => 'appointment_profile',
                            'support_ticket_profile' => 'support_ticket_profile',
                            'advance_profile' => 'advance_profile',
                            'commission_profile' => 'commission_profile',
                            'salary_profile' => 'salary_profile',
                            'project_profile' => 'project_profile',
                            'task_profile' => 'task_profile',
                            'award_profile' => 'award_profile',
                            'travel_profile' => 'travel_profile',
                        ],
                    ],
                ],
            ],

            // asset management permission
            'asset_management' => [
                'menu_order' => 13,
                'permissions' => [

                    // Asset menu
                    'asset_menu' => [
                        'asset_menu' => 'asset_menu',
                    ],

                    // Asset category
                    'asset_category' => [
                        'read' => 'asset_category_read',
                        'create' => 'asset_category_create',
                        'update' => 'asset_category_update',
                        'delete' => 'asset_category_delete',
                    ],

                    // Asset Manage
                    'asset_manage' => [
                        'read' => 'asset_read',
                        'create' => 'asset_create',
                        'update' => 'asset_update',
                        'delete' => 'asset_delete',
                        'assign' => 'asset_assign',
                    ],

                    // Asset History
                    'asset_manage' => [
                        'read' => 'asset_history_read',
                    ],
                ],
            ],

        ];
    }

    public function createNewUser($list)
    {
        $fake = \Faker\Factory::create();

        $roleId = $list['role_id'] ?? 1;
        $role = DB::table('roles')->where('id', $roleId)->first();

        $userData = [
            'name' => $list['name'] ?? $fake->name,
            'email' => $list['email'] ?? $fake->email,
            'password' => Hash::make('12345678'),
            'is_admin' => $list['is_admin'] ?? 0,
            'is_hr' => $list['is_hr'] ?? 0,
            'status_id' => 1,
            'company_id' => $list['company_id'] ?? 1,
            'country_id' => $list['country_id'] ?? 17,
            'department_id' => $list['department_id'] ?? 1,
            'designation_id' => $list['designation_id'] ?? 1,
            'phone' => $list['phone'] ?? $fake->phoneNumber,
            'email_verified_at' => \now(),
            'remember_token' => Str::random(10),
            'is_email_verified' => 'verified',
            'email_verify_token' => Str::random(10),
            'role_id' => $roleId,
            'permissions' => $list['is_hr'] == 1 ? \json_encode($this->hrPermissions()) : \json_encode($this->customPermissions($role->slug)),
        ];

        DB::table('users')->insert($userData);
        $user = DB::table('users')->where('email', $userData['email'])->first();

        $input = \session()->get('input');
        if ($input) {
            $input['user_id'] = $user->id;
            \session()->put('input', $input);
        }
    }

    public function createCompanyAdmin()
    {
        $input = \session()->get('input');

        $companies = Company::all();
        $count = 0;
        foreach ($companies as $company) {
            $department = Department::where('company_id', $company->id)->orderBy('id', 'asc')->first();
            $designation = Designation::where('company_id', $company->id)->orderBy('id', 'asc')->first();

            $companyId = $company->id;
            $slug = ($companyId == 1) ? 'superadmin' : 'admin';

            $userEmail = ($count != 0) ? 'admin'.$count.'@demo.com' : '<EMAIL>';

            // Create user for the first role
            $list1 = [
                'name' => 'Super Admin',
                'email' => \time().'@demo.com',
                'is_admin' => 1,
                'is_hr' => 0,
                'role_id' => 1,
                'company_id' => 1,
                'country_id' => $input['country_id'] ?? 17,
                'department_id' => $input['department_id'] ?? 16,
                'designation_id' => $input['designation_id'] ?? 30,
                'phone' => '+88017'.\time().\rand(100, 999),
            ];
            $this->createNewUser($list1);

            $list1 = [
                'name' => $input['name'] ?? 'Admin',
                'email' => $input['email'] ?? $userEmail,
                'is_admin' => 1,
                'is_hr' => 0,
                'role_id' => 2,
                'company_id' => $input['company_id'] ?? 2,
                'country_id' => $input['country_id'] ?? 17,
                'department_id' => $input['department_id'] ?? 16,
                'designation_id' => $input['designation_id'] ?? 30,
                'phone' => '+88017'.\time().\rand(100, 999),
            ];
            $this->createNewUser($list1);

            $count++;

            if ($input != '') {
                $roles = DB::table('roles')
                    ->where('company_id', $companyId)
                    ->whereIn('slug', [$slug])
                    ->get();

                foreach ($roles as $key => $role) {
                    $list = [
                        'name' => $role->name,
                        'email' => $role->name.'@demo.com',
                        'is_admin' => 1,
                        'is_hr' => 0,
                        'role_id' => $role->id,
                        'company_id' => $company->id,
                        'country_id' => 17,
                        'department_id' => $department->id ?? 16 + $key,
                        'designation_id' => $designation->id ?? 30 + $key,
                        'phone' => '+88017'.\time().\rand(100, 999),
                    ];
                    $this->createNewUser($list);
                }
            }
        }
    }
}
