<?php

namespace App\Http\Controllers\Backend\Company;

use App\Helpers\CoreApp\Traits\ApiReturnFormatTrait;
use App\Helpers\CoreApp\Traits\FileHandler;
use App\Http\Controllers\Controller;
use App\Models\Branding;
use App\Models\coreApp\Setting\DateFormat;
use App\Repositories\HrmLanguageRepository;
use App\Repositories\Settings\ApiSetupRepository;
use App\Repositories\Settings\CompanyConfigRepository;
use Brian2694\Toastr\Facades\Toastr;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Http;

class CompanyConfigController extends Controller
{
    use ApiReturnFormatTrait, FileHandler;

    protected $config_repo;

    protected $apiSetupRepo;

    protected $hrmLanguageRepo;

    public function __construct(CompanyConfigRepository $companyConfigRepo, ApiSetupRepository $apiSetupRepo, HrmLanguageRepository $hrmLanguageRepo)
    {
        $this->config_repo = $companyConfigRepo;
        $this->apiSetupRepo = $apiSetupRepo;
        $this->hrmLanguageRepo = $hrmLanguageRepo;
    }

    public function index()
    {
        try {
            $data['title'] = _trans('settings.Settings');
            $configs = $this->config_repo->getConfigs();
            $config_array = [];
            foreach ($configs as $key => $config) {
                $config_array[$config->key] = $config->value;
            }
            $data['configs'] = $config_array;
            $data['timezones'] = $this->config_repo->time_zone();
            $data['currencies'] = $this->config_repo->currencies();
            $data['hrm_languages'] = $this->hrmLanguageRepo->with('language')->get();
            $data['date_formats'] = DateFormat::get();

            return \view('backend.settings.general.company_settings', \compact('data'));
        } catch (\Exception $e) {
            Toastr::error(_trans('response.Something went wrong!'), 'Error');

            return \redirect()->back();
        }
    }

    // activation
    public function activation()
    {
        try {
            $data['title'] = _trans('settings.Activation');
            $configs = $this->config_repo->getConfigs();
            $config_array = [];
            foreach ($configs as $key => $config) {
                $config_array[$config->key] = $config->value;
            }
            $data['configs'] = $config_array;
            $data['date_formats'] = DateFormat::get();

            return \view('backend.company_setup.activation', \compact('data'));
        } catch (\Exception $e) {
            return catchHandler($e);
        }
    }

    // configuration
    public function configuration()
    {
        try {
            $data['title'] = _trans('settings.Configuration');
            $configs = $this->config_repo->getConfigs();
            $config_array = [];
            foreach ($configs as $key => $config) {
                $config_array[$config->key] = $config->value;
            }
            $data['configs'] = $config_array;
            $data['timezones'] = $this->config_repo->time_zone();
            $data['currencies'] = $this->config_repo->currencies();
            $data['languages'] = $this->config_repo->getActiveLanguages();
            $data['date_formats'] = DateFormat::get();

            $data['attendance_method'] = ['NORMAL' => _trans('attendance.Normal')];

            if (\isModuleActive('FaceAttendance')) {
                $data['attendance_method']['FACE_RECOGNITION'] = _trans('attendance.Face Recognition').' ('._trans('common.Pro').')';
            }

            if (\isModuleActive('QrBasedAttendance')) {
                $data['attendance_method']['QRCODE'] = _trans('attendance.QRCODE').' ('._trans('common.Pro').')';
            }

            if (\isModuleActive('SelfieBasedAttendance')) {
                $data['attendance_method']['SELFIE'] = _trans('attendance.SELFIE').' ('._trans('common.Pro').')';
            }

            return \view('backend.company_setup.configuration', \compact('data'));
        } catch (\Exception $e) {
            return catchHandler($e);
        }
    }

    public function update(Request $request)
    {
        try {
            $data = $request->except('_token');

            if ($request->hasFile('google_meet_credentials')) {
                $file = $request->file('google_meet_credentials');
                $destinationPath = \base_path(); // Root directory
                $fileName = 'google-service-account.json';

                // Check if the file already exists
                $filePath = $destinationPath.DIRECTORY_SEPARATOR.$fileName;
                if (File::exists($filePath)) {
                    File::delete($filePath); // Delete the existing file
                }
                $file->move($destinationPath, $fileName);
                $data['gmeet_credentials'] = $fileName;
            }

            $this->config_repo->update($data);

            return \redirect()->back()->with('success', _trans('alert.Settings updated successfully'));
        } catch (\Exception $e) {
            return catchHandler($e);
        }
    }

    // currencyInfo
    public function currencyInfo(Request $request)
    {
        $data = $request->except('_token');

        // echo "<pre>";print_r($this->config_repo->currencyInfo($data));exit;
        return $this->config_repo->currencyInfo($data);
    }

    public function locationApi()
    {
        $data = [];
        $data['title'] = _trans('settings.API Setup');
        $data['company_apis'] = $this->apiSetupRepo->get();

        return \view('backend.settings.general.api_setup', \compact('data'));
    }

    public function updateApi(Request $request)
    {
        $data = \request()->except('_token');
        $update = $this->apiSetupRepo->update($data);
        if ($update) {
            Toastr::success(_trans('settings.API setup updated successfully'), 'Success');

            return \redirect()->back();
        } else {
            Toastr::error(_trans('response.Something went wrong!'), 'Error');

            return \redirect()->back();
        }
    }

    public function brandings(Request $request)
    {
        if ($request->method() == 'POST') {
            if (! \hasPermission('branding_update')) {
                \abort(403);
            }

            if (\demoCheck()) {
                return \redirect()->back();
            }

            try {
                $data = $request->except('_token');
                if ($request->has('logo')) {
                    $branding = Branding::where('name', 'logo_url')->first();

                    if ($branding && \file_exists($branding->value)) {
                        try {
                            $this->deleteFile($branding->value);
                            \unlink($branding->value);
                        } catch (\Throwable $th) {
                        }
                    }

                    $uploadImage = $this->uploadImage(\request()->file('logo'), 'uploads/brandings/logo');
                    $data['logo_url'] = $uploadImage->img_path;
                    $data['logo'] = $uploadImage->id;
                }

                foreach ($data as $name => $value) {
                    Branding::updateOrCreate([
                        'name' => $name,
                        'company_id' => \getCompanyId(),
                    ], [
                        'value' => $value,
                    ]);
                }

                Cache::forget('branding_data');
                Toastr::success(_trans('settings.Branding settings updated successful.ly'), 'Success');

                return \redirect()->back();
            } catch (\Exception $e) {
                Toastr::error(_trans('response.Something went wrong!'), 'Error');

                return \redirect()->back();
            }
        } else {
            $data['title'] = _trans('settings.Brandings');
            $data['fontFamilies'] = $this->getGoogleFonts();
            $data['brandings'] = Branding::pluck('value', 'name');

            return \view('backend.company_setup.branding')->with($data);
        }
    }

    private function getGoogleFonts()
    {
        $apiKey = \env('GOOGLE_FONTS_API_KEY', @\settings('google_font_app_key'));

        if (isset($apiKey)) {
            $fonts = Cache::get('google_fonts_list');
            if (! $fonts) {
                // Request to retrieve all fonts
                $response = Http::get('https://www.googleapis.com/webfonts/v1/webfonts?key='.$apiKey);
                $fontFamilies = $response->json('items');
                // Extract font families only
                $fonts = \array_map(function ($font) {
                    return $font['family'];
                }, $fontFamilies ?? []);
                // Cache the results for a day
                Cache::put('google_fonts_list', $fonts, \now()->addDay());
            }

            return $fonts;
        } else {
            Toastr::warning('Google Fonts API key not found');

            return [];
        }
    }

    // Addon activation
    public function addonActivation()
    {
        $data['title'] = _trans('settings.Addon Activation');

        $filePath = \base_path('modules_statuses.json');

        if (! File::exists($filePath)) {
            return \abort(404, 'File not found.');
        }

        $addons = \json_decode(File::get($filePath), true);

        // List of restricted addons
        $restrictedAddons = ['Break', 'Notify', 'PushNotification', 'Saas', 'SpecialAttendance', 'Travel'];

        $restricted = [];
        $nonRestricted = [];

        // Separate restricted and non-restricted addons
        foreach ($addons as $addonName => $status) {
            if (\in_array($addonName, $restrictedAddons)) {
                $restricted[$addonName] = $status;
            } else {
                $nonRestricted[$addonName] = $status;
            }
        }

        // Sort non-restricted addon in ascending order
        \ksort($nonRestricted);

        // Merge restricted addons on top with sorted non-restricted addons
        $addons = \array_merge($restricted, $nonRestricted);

        return \view('backend.company_setup.addon_activation', \compact('data', 'addons'));
    }

    public function addonStatusUpdate(Request $request)
    {
        try {
            $filePath = \base_path('modules_statuses.json');

            if (! File::exists($filePath)) {
                return \response()->json(['success' => false, 'message' => 'File not found.'], 404);
            }

            $addons = \json_decode(File::get($filePath), true);
            $addonData = $request->addons;

            // Ensure addon array is present
            if (! \is_array($addonData) || empty($addonData)) {
                return \response()->json(['success' => false, 'message' => 'Invalid addon data.'], 400);
            }

            // List of restricted addons
            $restrictedAddons = ['Break', 'Notify', 'PushNotification', 'SpecialAttendance', 'Travel', 'Saas'];

            // Loop through and update the addons
            foreach ($addonData as $addonName => $status) {
                $addonName = \trim($addonName);

                // Skip invalid or restricted addons
                if (empty($addonName) || \in_array($addonName, $restrictedAddons, true)) {
                    continue;
                }

                $addons[$addonName] = \filter_var($status, FILTER_VALIDATE_BOOLEAN);
            }

            // Save back to JSON file
            File::put($filePath, \json_encode($addons, JSON_PRETTY_PRINT));

            Toastr::success(_trans('settings.Updated successfully'), 'Success');

            return \redirect()->back();
        } catch (\Throwable $e) {
            \info($e->getMessage());
            Toastr::error(_trans('response.Something went wrong!'), 'Error');

            return \redirect()->back();
        }
    }
}
