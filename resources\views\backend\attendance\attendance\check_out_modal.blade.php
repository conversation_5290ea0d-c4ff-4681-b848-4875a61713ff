<div class="checkout-modal modal lead-modal" id="lead-modal" aria-labelledby="modalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content data">
            <div class="modal-header modal-header-style mb-3">
                <h5 class="modal-title text-white">{{ @$data['title'] }} </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                    <i class="fa fa-times" aria-hidden="true"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="row pb-4 text-align-center">
                    <div class="col-md-12">
                        <div class="form-group">
                            <p class="text-center mb-0">
                                {{ _trans('common.Choose your option') }}
                            </p>
                            <div class="place-switch">
                                <div class="switch-field">

                                    <input type="radio" id="place_office" name="place_mode" value="1" checked="">
                                    <label for="place_office">
                                        <i class="fas fa-building"></i>
                                        <p class="on-half-expanded">{{ _trans('attendance.Office') }}</p>
                                    </label>

                                    <input type="radio" id="place_home" name="place_mode" value="0">
                                    <label for="place_home">
                                        <i class="fas fa-home"></i>
                                        <p class="on-half-expanded">{{ _trans('common.Home') }}</p>
                                    </label>

                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="timer-field pt-2 pb-2">
                                <h1 class="text-center">
                                    <div class="clock company_name_clock fs-16 clock" id="clock" onload="currentTime()">
                                        {{ _trans('attendance.00:00:00') }}</div>
                            </div>
                        </div>


                        @if (@$data['reason'][0] == 'LE')
                        <div class="form-group w-50 mx-auto mb-3">
                            <label class="form-label float-left">{{ _trans('common.Note') }} </label>
                            <textarea type="text" name="reason" id="reason" rows="2" class="form-control ot-input mt-0"
                                required
                                placeholder="{{ _trans('common.Are You Leaving Early?') }}">{{ old('reason') }}</textarea>
                            <small class="error_show_reason text-left text-danger">

                            </small>
                        </div>
                        @endif

                        <div class="form-group button-hold-container">
                            <button class="check-in-out-button-hold" id="check-in-out-button-hold">
                                <div>
                                    <div class="icon_text">
                                        <div class="icon text-white">
                                            @include('backend.dashboard.attendance.finger_icon')
                                        </div>
                                        <span class="text-14 fw-semibold text-white">{{ _trans('common.Check Out')
                                            }}</span>
                                    </div>
                                    <svg class="progress" viewBox="0 0 32 32">
                                        <circle r="8" cx="16" cy="16" stroke="transparent" />
                                    </svg>
                                    <svg class="tick" viewBox="0 0 32 32">
                                        <polyline points="18,7 11,16 6,12" />
                                    </svg>
                                </div>
                            </button>
                        </div>

                        <input type="hidden" id="checkInOrCheckOutUrl" value="{{ @$data['url'] }}">
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="{{ global_asset('backend/js/fs_d_ecma/components/__attendance.js') }}"></script>