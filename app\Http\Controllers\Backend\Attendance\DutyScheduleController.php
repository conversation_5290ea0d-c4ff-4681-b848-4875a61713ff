<?php

namespace App\Http\Controllers\Backend\Attendance;

use App\Helpers\CoreApp\Traits\ApiReturnFormatTrait;
use App\Http\Controllers\Controller;
use App\Models\coreApp\Relationship\RelationshipTrait;
use App\Repositories\DutyScheduleRepository;
use Brian2694\Toastr\Facades\Toastr;
use Illuminate\Http\Request;

class DutyScheduleController extends Controller
{
    use ApiReturnFormatTrait,  RelationshipTrait;

    protected $dutyScheduleRepository;

    protected $model;

    public function __construct(DutyScheduleRepository $dutyScheduleRepository)
    {
        $this->dutyScheduleRepository = $dutyScheduleRepository;
    }

    public function index(Request $request)
    {
        if ($request->ajax()) {
            return $this->dutyScheduleRepository->table($request);
        }
        $data['table'] = \route('dutySchedule.index');
        $data['url_id'] = 'duty_schedule_table_url';
        $data['class'] = 'table_class';
        $data['delete_url'] = \route('dutySchedule.deleteData');
        $data['checkbox'] = true;
        $data['fields'] = $this->dutyScheduleRepository->fields();

        $data['title'] = _trans('common.Duty Schedule');

        return \view('backend.attendance.duty_schedule.index', \compact('data'));
    }

    public function create()
    {
        $data['title'] = 'Create Duty Schedule';

        return \view('backend.attendance.duty_schedule.create', \compact('data'));
    }

    public function store(Request $request)
    {
        $this->validate($request, [
            'start_time' => 'required|date_format:H:i',
            'end_time' => 'required|date_format:H:i|after_or_equal:start_time',
            'consider_time' => 'nullable|numeric',
            'status_id' => 'required',
        ]);

        try {
            $this->dutyScheduleRepository->store($request);
            Toastr::success(_trans('response.Operation successful'), 'Success');

            return \redirect()->route('dutySchedule.index');
        } catch (\Throwable $th) {
            \info($th->getMessage());
            Toastr::error(_trans('response.Something went wrong'), 'Error');

            return \redirect()->back();
        }
    }

    public function edit($id)
    {
        $data['title'] = 'Edit Duty Schedule';
        $data['duty_schedule'] = $this->dutyScheduleRepository->show($id);

        if (! $data['duty_schedule']) {
            return \redirect()->back()->with('error', 'Duty Schedule not found.');
        }

        return \view('backend.attendance.duty_schedule.edit', \compact('data'));
    }

    public function update(Request $request, $id)
    {
        $this->validate($request, [
            'start_time' => 'required',
            'end_time' => 'required|after_or_equal:start_time',
            'consider_time' => 'numeric',
            'status_id' => 'required',
        ]);

        try {
            $this->dutyScheduleRepository->update($request, $id);

            Toastr::success(_trans('response.Operation successful'), 'Success');

            return \redirect()->route('dutySchedule.index');
        } catch (\Exception $th) {
            Toastr::error($th->getMessage(), 'Error');

            return \redirect()->back();
        } catch (\Throwable $th) {
            \info($th->getMessage());
            Toastr::error(_trans('response.Something went wrong'), 'Error');

            return \redirect()->back();
        }
    }

    public function delete($id)
    {
        try {
            $this->dutyScheduleRepository->destroy($id);

            Toastr::success(_trans('attendance.Duty schedule has been deleted'), 'Success');

            return \redirect()->back();
        } catch (\Throwable $exception) {
            \info($exception->getMessage());
            Toastr::error(_trans('attendance.Something went wrong'), 'Error');

            return \redirect()->back();
        }
    }

    public function deleteData(Request $request)
    {
        if (\demoCheck()) {
            return $this->responseWithError(_trans('message.You cannot delete for demo'), [], 400);
        }

        return $this->dutyScheduleRepository->destroyAll($request);
    }

    public function bulkCreate()
    {
        $data['title'] = 'Create Duty Schedule';

        return \view('backend.attendance.duty_schedule.bulk_create', \compact('data'));
    }

    public function bulkStore(Request $request)
    {
        $request->validate([
            'file' => 'required|file|mimes:csv,txt|max:2048',
        ], [
            'file.required' => 'Please upload a CSV file.',
        ]);

        try {
            $this->dutyScheduleRepository->bulkStore($request);

            return \redirect()->back()->with('success', 'Schedule import successfully.');
        } catch (\Exception $exception) {
            \info($exception->getMessage());

            return \redirect()->back()->withErrors(['errors' => $exception->getMessage()]);
        }
    }
}
