<div class="flex-fill">
    <!-- profile body nav start -->
    <div class="profile-body-form 0">
        <div class="ot-card" data-select2-id="698">
            <div class="row">
                <div class="col-lg-6 d-flex">
                    <div class="col-lg-6">
                        <div class="fw-bold title-color" for="#">
                            <h6 class="mb-0">{{ _trans('common.Contract Date') }}</h6>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <p class="content-color mb-0">
                            {{ showDate(@$data['show']->original['data']['contract_start_date']) }}</p>
                    </div>
                </div>
                <div class="col-lg-6 d-flex">
                    <div class="col-lg-6">
                        <div class="fw-bold title-color" for="#">
                            <h6 class="mb-0">{{ _trans('common.Contract End') }}</h6>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <p class="content-color mb-0">
                            {{ showDate($data['show']->original['data']['contract_end_date']) }}</p>
                    </div>
                </div>
            </div>
            <hr>
            <div class="row">
                <div class="col-lg-6 d-flex">
                    <div class="col-lg-6">
                        <div class="fw-bold title-color" for="#">
                            <h6 class="mb-0">{{ _trans('common.Basic Salary') }}</h6>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <p class="content-color mb-0">
                            {{ showAmount($data['show']->original['data']['basic_salary']) }}</p>
                    </div>
                </div>
                <div class="col-lg-6 d-flex">
                    <div class="col-lg-6">
                        <div class="fw-bold title-color" for="#">
                            <h6 class="mb-0">{{ _trans('payroll.Payslip Type') }}</h6>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <p class="content-color mb-0">{{ _trans('payroll.Per Month') }}</p>
                    </div>
                </div>
            </div>
            <hr>
            <div class="row">
                <div class="col-lg-6 d-flex">
                    <div class="col-lg-6">
                        <div class="fw-bold title-color" for="#">
                            <h6 class="mb-0">{{ _trans('payroll.Late Check In') }}</h6>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <p class="content-color mb-0">{{ @$data['show']->original['data']['late_check_in'] }}
                            {{ _trans('common.Days') }}</p>
                    </div>
                </div>
                <div class="col-lg-6 d-flex">
                    <div class="col-lg-6">
                        <div class="fw-bold title-color" for="#">
                            <h6 class="mb-0">{{ _trans('payroll.Late Check out') }}</h6>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <p class="content-color mb-0">{{ @$data['show']->original['data']['early_check_out'] }}
                            {{ _trans('common.Days') }}</p>
                    </div>
                </div>
            </div>
            <hr>
            <div class="row">
                <div class="col-lg-6 d-flex">
                    <div class="col-lg-6">
                        <div class="fw-bold title-color" for="#">
                            <h6 class="mb-0">{{ _trans('payroll.Extra Leave') }}</h6>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <p class="content-color mb-0">{{ @$data['show']->original['data']['monthly_leave'] }}
                            {{ _trans('common.Days') }}</p>
                    </div>
                </div>
                <div class="col-lg-6 d-flex">
                    <div class="col-lg-6">
                        <div class="fw-bold title-color" for="#">
                            <h6 class="mb-0">{{ _trans('payroll.Monthly Leave') }}</h6>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <p class="content-color mb-0">{{ @$data['show']->original['data']['monthly_leave'] }}
                            {{ _trans('common.Days') }}</p>
                    </div>
                </div>
            </div>



        </div>
    </div>
<!-- profile body form end -->
</div>
