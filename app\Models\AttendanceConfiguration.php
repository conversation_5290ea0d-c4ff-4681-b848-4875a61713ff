<?php

namespace App\Models;

use App\Models\Hrm\Attendance\DutySchedule;
use Illuminate\Database\Eloquent\Model;

class AttendanceConfiguration extends Model
{
    protected $table = 'attendance_configurations';

    protected $guarded = [
        'id',
        'created_at',
        'updated_at',
    ];

    protected $casts = [
        'attendance_method' => 'array',
        'weekends' => 'array',
    ];

    protected $with = [
        'dutySchedules',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function dutySchedules()
    {
        return $this->belongsToMany(DutySchedule::class, 'user_duty_schedules', 'user_id', 'duty_schedule_id');
    }
}
