<?php

namespace App\Helpers\CoreApp\Traits;

use App\Mail\User\UserCreate;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

trait MailHandler
{
    public function sendEmail($user, $password)
    {
        try {
            Mail::to($user->email)->send(new UserCreate($user, $password));
            Log::info(_trans('message.Mail has been sent.'));

            return true;
        } catch (\Throwable $th) {
            Log::error($th);

            return false;
        }
    }
}
