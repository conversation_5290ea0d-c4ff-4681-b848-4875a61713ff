<?php

namespace UniversalChatbot\Providers;

use Illuminate\Support\ServiceProvider;
use UniversalChatbot\Services\EnhancedGeminiService;
use UniversalChatbot\Services\GeminiApiClient;
use UniversalChatbot\Services\DatabaseSchemaService;
use UniversalChatbot\Services\QueryAnalyzer;
use UniversalChatbot\Services\QueryExecutor;
use UniversalChatbot\Services\ResponseFormatter;
use UniversalChatbot\Services\PerformanceTracker;
use UniversalChatbot\Services\FollowUpDetector;

class ChatbotServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        // Register individual services
        $this->app->singleton(GeminiApiClient::class, function ($app) {
            return new GeminiApiClient();
        });

        $this->app->singleton(DatabaseSchemaService::class, function ($app) {
            return new DatabaseSchemaService();
        });

        $this->app->singleton(QueryExecutor::class, function ($app) {
            return new QueryExecutor();
        });

        $this->app->singleton(PerformanceTracker::class, function ($app) {
            return new PerformanceTracker();
        });

        $this->app->singleton(FollowUpDetector::class, function ($app) {
            return new FollowUpDetector();
        });

        // Register services that depend on other services
        $this->app->singleton(QueryAnalyzer::class, function ($app) {
            return new QueryAnalyzer(
                $app->make(GeminiApiClient::class),
                $app->make(DatabaseSchemaService::class)
            );
        });

        $this->app->singleton(ResponseFormatter::class, function ($app) {
            return new ResponseFormatter(
                $app->make(GeminiApiClient::class)
            );
        });

        // Register the main service
        $this->app->singleton(EnhancedGeminiService::class, function ($app) {
            return new EnhancedGeminiService(
                $app->make(GeminiApiClient::class),
                $app->make(DatabaseSchemaService::class),
                $app->make(QueryAnalyzer::class),
                $app->make(QueryExecutor::class),
                $app->make(ResponseFormatter::class),
                $app->make(PerformanceTracker::class),
                $app->make(FollowUpDetector::class)
            );
        });

        // Bind the interface to the implementation if you have one
        // $this->app->bind(ChatbotServiceInterface::class, EnhancedGeminiService::class);
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        // Publish configuration files
        $this->publishes([
            __DIR__ . '/../../config/universal_chatbot.php' => config_path('universal_chatbot.php'),
        ], 'config');

        // Load package routes
        $this->loadRoutesFrom(__DIR__ . '/../../routes/web.php');

        // Load package views
        $this->loadViewsFrom(__DIR__ . '/../../resources/views', 'universal_chatbot');

        // Load package migrations
        $this->loadMigrationsFrom(__DIR__ . '/../../database/migrations');
    }
}
