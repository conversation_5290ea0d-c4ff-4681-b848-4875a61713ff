<?php

use App\Models\Company\Company;
use App\Models\coreApp\Setting\CompanyConfig;
use App\Models\coreApp\Setting\Setting;
use App\Models\ExpireNotification;
use App\Models\Frontend\Menu;
use App\Models\Hrm\Attendance\EmployeeBreak;
use App\Models\Notification;
use App\Models\Role\Role;
use App\Models\Settings\ApiSetup;
use App\Models\Settings\HrmLanguage;
use App\Models\Subscription;
use App\Models\Upload;
use App\Models\User;
use App\Models\Visit\VisitSchedule;
use App\Notifications\HrmSystemNotification;
use Brian2694\Toastr\Facades\Toastr;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Modules\Break\Entities\UserBreak;
use Modules\Saas\Entities\SaasSubscription;
use Modules\Tardy\Entities\TardyRule;

function menu_active_by_route($route)
{
    return \request()->routeIs($route) ? 'active' : 'in-active';
}
function menu_active_by_url($url)
{
    return \url()->current() == $url ? 'active' : 'in-active';
}

function set_active($path, $active = 'mm-show mm-active')
{
    return \call_user_func_array('Request::is', (array) $path) ? $active : '';
}

function set_menu(array $path, $active = 'show')
{
    foreach ($path as $route) {
        \print_r($route);
        if (Route::currentRouteName() == $route) {
            return $active;
        }
    }

    return (\request()->is($path)) ? $active : '';
}

function getMonthDays($month): array
{
    $date = Carbon::parse($month);
    $startOfMonth = $date->copy()->startOfMonth()->subDay();
    $endOfMonth = $date->copy()->endOfMonth()->format('d');
    $monthDays = [];

    for ($i = 0; $i < $endOfMonth; $i++) {
        $monthDays[] = $startOfMonth->addDay()->startOfDay()->copy();
    }

    return $monthDays;
}

if (! \function_exists('getCompanyId')) {
    function getCompanyId()
    {
        if (Session::has('session_company_id')) {
            return Session::get('session_company_id');
        }

        $companyId = \getCurrentDomainCompany()->id ?? 1;

        Session::put('session_company_id', $companyId);

        return $companyId;
    }
}

if (! \function_exists('getBranchId')) {
    function getBranchId()
    {
        $branchId = Session::get('session_branch_id');

        if ($branchId) {
            return $branchId;
        }

        $branchId = Auth::user()->branch_id ?? 1;

        return $branchId;
    }
}

if (! \function_exists('appMode')) {
    function appMode()
    {
        if (\config('app.style') === 'demo') {
            return true;
        }

        return false;
    }
}

if (! \function_exists('demoCheck')) {
    function demoCheck($message = '')
    {
        if (\appMode()) {
            if (empty($message)) {
                $message = 'For the demo version, you cannot change this';
            }
            Toastr::error($message, 'Failed');

            return true;
        } else {
            return false;
        }
    }
}

if (! \function_exists('getSetting')) {
    function getSetting($key)
    {
        try {
            $data = ApiSetup::where('name', $name)
                ->select('key', 'secret', 'endpoint', 'status_id')
                ->first();

            return $data;
        } catch (Throwable $th) {
            return null;
        }
    }
}

if (! \function_exists('_trans')) {
    function _trans($value)
    {
        try {
            $local = \app()->getLocale();

            $langPath = \resource_path('lang/'.$local.'/');
            if (! \file_exists($langPath)) {
                \mkdir($langPath, 0777, true);
            }

            // Determine the file name and translation key
            if (\str_contains($value, '.')) {
                [$file_name, $trans_key] = \explode('.', $value);
                $file_path = $langPath.$file_name.'.json';
            } else {
                $trans_key = $value;
                $file_path = \resource_path('lang/'.$local.'/'.$local.'.json');
            }

            // Ensure the file exists and initialize if necessary
            if (! \file_exists($file_path)) {
                \file_put_contents($file_path, \json_encode([]));
            }

            // Read the existing translations
            $file_data = \json_decode(\file_get_contents($file_path), true) ?? [];

            // Add the translation key if it doesn't exist
            if (! \array_key_exists($trans_key, $file_data)) {
                $file_data[$trans_key] = $trans_key; // Default value can be the key itself
                \file_put_contents($file_path, \json_encode($file_data, JSON_PRETTY_PRINT));
            }

            return $file_data[$trans_key];
        } catch (Exception $exception) {
            return $value;
        }
    }
}

function string_clean($string)
{
    $string = \str_replace(['[\', \']'], '', $string);
    $string = \preg_replace(['/[^a-z0-9]/i', '/[-]+/'], ' ', $string);

    return \strtolower(\trim($string, ' '));
}

function main_date_format($data)
{
    return \date('d M y', \strtotime($data));
}

function main_time_format($data)
{
    return \date('H:i:s', \strtotime($data));
}

if (! \function_exists('uploaded_asset')) {
    function uploaded_asset($id, $default = 'static/blank_small.png')
    {
        return Cache::remember("uploaded_asset_{$id}", 60 * 60, function () use ($id, $default) {
            $asset = Upload::find($id);

            return $asset ? \my_asset($asset->img_path) : \url($default);
        });
    }
}

if (! \function_exists('uploaded_asset_path')) {
    function uploaded_asset_path($id)
    {
        if (($asset = Cache::get("uploaded_asset_{$id}")) != null) {
            return \my_asset($asset->img_path);
        } else {
            return \url('assets/logo-white.png');
        }

        return \url('assets/logo-white.png');
    }
}

if (! \function_exists('logo_light')) {
    function logo_light($id)
    {
        if (($asset = Upload::find($id)) != null) {
            return \my_asset($asset->img_path);
        } else {
            return \url('assets/logo-white.png');
        }

        return \url('assets/logo-white.png');
    }
}

if (! \function_exists('logo_dark')) {
    function logo_dark($id)
    {
        if (($asset = Cache::get("uploaded_asset_{$id}")) != null) {
            return $asset;
        } else {
            return \url('assets/logo-dark.png');
        }

        return \url('assets/logo-dark.png');
    }
}
if (! \function_exists('company_fav_icon')) {
    function company_fav_icon($id)
    {
        if (($asset = Cache::get("uploaded_asset_{$id}")) != null) {
            return $asset;
        } else {
            return \url('assets/favicon.png');
        }

        return \url('assets/favicon.png');
    }
}
if (! \function_exists('background_asset')) {
    function background_asset($id)
    {
        if ((Cache::get("uploaded_asset_{$id}")) != null) {
            return Cache::get("uploaded_asset_{$id}");
        } else {
            return \url('/assets/images/BG2.jpg');
        }

        return \url('/assets/images/BG2.jpg');
    }
}

if (! \function_exists('uploaded_asset_with_type')) {
    function uploaded_asset_with_type($id)
    {
        if (($asset = Cache::get("uploaded_asset_{$id}")) != null) {
            return [
                'path' => \my_asset($asset->img_path),
                'type' => $asset->type,
            ];
        } else {
            return [
                'path' => \url('static/blank_small.png'),
                'type' => 'image',
            ];
        }
    }
}

if (! \function_exists('uploaded_asset_with_user')) {
    function uploaded_asset_with_user($id)
    {
        if ($user = User::find($id)) {
            if ($asset = Upload::find($user->avatar_id)) {
                return \my_asset($asset->img_path);
            } else {
                return \url('static/blank_small.png');
            }
        } else {
            return \url('static/blank_small.png');
        }
    }
}

if (! \function_exists('check_file_exist')) {
    function check_file_exist($file_path)
    {
        if (\file_exists($file_path)) {
            return true;
        } else {
            return false;
        }
    }
}

if (! \function_exists('my_asset')) {
    function my_asset($path, $secure = null)
    {
        try {
            if ($path == '') {
                return \url('static/blank_small.png');
            } else {
                if (\env('FILESYSTEM_DISK') == 's3' && Storage::disk('s3')->exists($path)) {
                    return Storage::disk('s3')->url($path);
                } elseif ((\env('FILESYSTEM_DISK') == 'local') && File::exists($path)) {
                    return \global_asset($path);
                }
            }

            return null;
        } catch (Exception $e) {
            \info($e->getMessage());

            return null;
        }
    }
}

function asset_path($id)
{
    $path = Cache::get("uploaded_asset_{$id}");
    if ($path) {
        return $path->img_path;
    }

    return false;
}

function date_format_for_view($date)
{
    $strtotime = \strtotime($date);
    $date = \date('d/m/Y', $strtotime);

    return $date;
}

// where between is date search string
if (! \function_exists('start_end_datetime')) {
    function start_end_datetime($start_date, $end_date)
    {
        $date = [$start_date.' '.'00:00:00', $end_date.' '.'23:59:59'];

        return $date;
    }
}

// translate funcation for laravel
if (! \function_exists('_translate')) {
    function _translate($key, $type = true)
    {
        return $key;
    }
}

function actionButton($string, $param, $type = null)
{
    if ($type == 'delete') {
        return
            '<a href="javascript:;" class="dropdown-item" onclick="'.$param.'">
            '.$string.'
        </a>';
    } elseif ($type == 'approve') {
        return Auth::user() ?
        '<a href="javascript:;" class="dropdown-item" onclick="'.$param.'">
        '.$string.'
        </a>'
        : '';
    } elseif ($type == 'reject') {
        return Auth::user() ?
        '<a href="javascript:;" class="dropdown-item" onclick="'.$param.'">
        '.$string.'
        </a>'
        : '';
    } elseif ($type == 'modal') {
        return Auth::user() ?
        '<a href="javascript:;" class="dropdown-item" onclick="'.$param.'">
        '.$string.'
        </a>'
        : '';
    } elseif ($type == 'new_page') {
        return Auth::user() ?
        '<a target="_blank" class="dropdown-item" href="'.$param.'">
            '.\_translate($string, false).'
            </a>'
        : '';
    } else {
        return Auth::user() ?
        '<a class="dropdown-item" href="'.$param.'">
            '.\_translate($string, false).'
            </a>'
        : '';
    }
}

function actionHTML($action_button)
{
    return '<div class="dropdown dropdown-action">
            <button type="button" class="btn-dropdown" data-bs-toggle="dropdown"
                aria-expanded="false">
                <i class="fa-solid fa-ellipsis"></i>
            </button>
            <ul class="
            ">
            '.$action_button.'
            </ul>
        </div>';
}

if (! \function_exists('settings')) {
    function settings($key)
    {
        try {
            $companyId = \getCompanyId();
            $cacheKey = "company_settings_{$companyId}";
            // Cache all settings for the company for 1 hour (adjust as needed)
            $settings = Cache::remember($cacheKey, 3600, function () {
                return CompanyConfig::pluck('value', 'key')->toArray();
            });

            return $settings[$key] ?? null;
        } catch (Exception $e) {
            return null;
        }
    }
}

if (! \function_exists('showAmount')) {
    function showAmount($amount)
    {
        try {
            return \settings('currency_symbol').' '.$amount;
        } catch (Exception $e) {
            return null;
        }
    }
}

function teams($members)
{
    $make_membars = '';
    foreach ($members as $member) {
        if (\hasPermission('profile_view')) {
            $url = $member->user ? \route('user.profile', [@$member->user->id, 'official']) : '#';
        } else {
            $url = '#';
        }
        $make_membars .= '<a target="_blank" href="'.$url.'"><img data-toggle="tooltip" data-placement="top" title="'.@$member->user->name.'" src="'.\uploaded_asset(@$member->user->avatar_id).'" class="staff-profile-image-small" ></a>';
    }

    return $make_membars;
}

if (! \function_exists('showDate')) {
    function showDate($date)
    {
        try {
            if ($date != null) {
                $date_setting = \settings('date_format') ?? 'd-m-Y';

                return Carbon::parse($date)->locale(\app()->getLocale())->translatedFormat($date_setting);
            } else {
                return 'N/A';
            }
        } catch (Exception $e) {
            return;
        }
    }
}
if (! \function_exists('isModuleActive')) {
    function isModuleActive($module_name)
    {
        try {
            $json = \file_get_contents(\base_path('modules_statuses.json'));
            $modules = \json_decode($json, true);
            if (\array_key_exists($module_name, $modules ?? [])) {
                if ($modules[$module_name]) {
                    // check module.json file exists in module directory
                    $module_json = \base_path('Modules/'.$module_name.'/module.json');
                    if (\file_exists($module_json)) {
                        // $module = json_decode(file_get_contents($module_json), true);
                        return true;
                    } else {
                        return false;
                    }
                } else {
                    return false;
                }
            } else {
                return false;
            }
        } catch (Exception $e) {
            return false;
        }
    }
}

function base_settings($data, $default = null)
{
    $companyId = \getCompanyId();

    $cacheKey = "settings_{$companyId}";

    // Load from cache or DB if not cached
    $settings = Cache::rememberForever($cacheKey, function () {
        return Setting::pluck('value', 'name')->toArray();
    });

    return $settings[$data] ?? $default;
}

if (! \function_exists('getCompanyLogo')) {
    function getCompanyLogo($name, $companyId)
    {
        return Setting::where('name', $name)->first()->value ?? null;
    }
}

function attachment($attachment_id)
{
    // image show
    if ($attachment_id != null) {
        $image = '<img src="'.\uploaded_asset($attachment_id).'" class="img-fluid" alt="image">';
    } else {
        $image = '';
    }

    return $image;
}
if (! \function_exists('timeDiff')) {
    function timeDiff($start_time, $end_time, $format, $start_date = null, $end_date = null)
    {
        if ($start_date == null) {
            $start_date = \date('Y-m-d');
        }
        if ($end_date == null) {
            $end_date = \date('Y-m-d');
        }
        $start_time = Carbon::parse($start_date.' '.$start_time);
        $end_time = Carbon::parse($end_date.' '.$end_time);
        $diff = $start_time->diffInSeconds($end_time);

        $hours = \floor($diff / 3600);
        $minutes = \floor(($diff - ($hours * 3600)) / 60);
        $seconds = $diff - ($hours * 3600) - ($minutes * 60);
        if ($format == 'h') {
            return $hours;
        }
        if ($format == 'm') {
            return $minutes;
        }
        if ($format == 's') {
            return $seconds;
        }

        return $hours.':'.$minutes.':'.$seconds;
    }
}

function date_diff_days($date, $date2 = null)
{
    $date1 = Carbon::parse($date);
    $date2 = $date2 ?? Carbon::now();

    return $date1->diffInDays($date2);
}

if (! \function_exists('is_Admin')) {
    function is_Admin()
    {
        if (Auth::user()->role->slug == 'superadmin' || Auth::user()->role->slug == 'admin' || Auth::user()->role->slug == 'hr') {
            return true;
        } else {
            return false;
        }
    }
}

if (! \function_exists('myCompanyData')) {
    function myCompanyData($company_id)
    {
        if ($company_id == Auth::user()->company->id || Auth::user()->role_id == 1) {
            return true;
        } else {
            return false;
        }
    }
}
if (! \function_exists('dateFormet')) {
    function dateFormet($date, $format)
    {
        try {
            return Carbon::parse($date)->locale(\app()->getLocale())->translatedFormat($format);
        } catch (Throwable $th) {
            return;
        }
    }
}
if (! \function_exists('hasPermission')) {
    function hasPermission($key_word): bool
    {
        $user = Auth::user();

        // Allow access if the user is a superadmin
        if ($user->role_id == 1) {
            return true;
        }
        // Store in memory per request
        static $cachedPermissions = null;

        if ($cachedPermissions === null) {
            $permissions = Cache::flexible("user_permissions_{$user->id}", [3600, 7200], function () use ($user) {
                return optional($user->permissions)->permissions;
            });

            $cachedPermissions = is_array($permissions) ? $permissions : json_decode($permissions ?? '[]', true);
        }

        return in_array($key_word, $cachedPermissions);
    }

}

// if function not exists
if (! \function_exists('sendDatabaseNotification')) {
    function sendDatabaseNotification($user, $details, $notification_for = null)
    {
        try {
            \Notification::send($user, new HrmSystemNotification($details, $notification_for = null));
        } catch (Throwable $th) {
            Log::error('Notification Error:'.$th->getMessage());
        }
    }
}

if (! \function_exists('getActionButtons')) {
    function getActionButtons($action_button)
    {
        return '<div class="flex-nowrap">
                    <div class="dropdown">
                        <button class="btn btn-white dropdown-toggle align-text-top action-dot-btn" data-boundary="viewport" data-toggle="dropdown">
                            <i class="fas fa-ellipsis-v"></i>
                        </button>
                        <div class="dropdown-menu dropdown-menu-right">'.$action_button.'</div>
                    </div>
                </div>';
    }
}
if (! \function_exists('visitStatusColor')) {
    function visitStatusColor($status)
    {
        $status_color = 'FF8F5E';
        switch ($status) {
            case 'started':
                $status_color = 'FF8F5E';
                break;
            case 'reached':
                $status_color = '8A21F3';
                break;
            case 'cancelled':
                $status_color = 'BBC0CC';
                break;
            case 'created':
                $status_color = 'FF8F5E';
                break;
            case 'completed':
                $status_color = '12B89D';
                break;

            default:
                $status_color = 'FF8F5E';
                break;
        }

        return '0xFF'.$status_color;
    }
}

function appColorCodePrefix(): string
{
    return '0xFF';
}
if (! \function_exists('plain_text')) {
    function plain_text($text)
    {
        return Str::title(Str::replace('_', ' ', Str::replace('-', ' ', $text)));
    }
}
function getReached($visit)
{
    $reached_at = VisitSchedule::where('visit_id', $visit->id)->where('status', 'reached')->latest()->first();
    if ($reached_at) {
        return \onlyTimePlainText($reached_at->reached_at);
    }

    return null;
}
function getDurration($visit)
{
    $started_at = VisitSchedule::where('visit_id', $visit->id)->where('status', 'started')->first();
    $reached_at = VisitSchedule::where('visit_id', $visit->id)->where('status', 'reached')->latest()->first();
    if ($started_at != '' && $reached_at != '') {
        $start_time = \strtotime($started_at->started_at);
        $end_time = \strtotime($reached_at->reached_at);
        $diff = $end_time - $start_time;
        $hours = \floor($diff / 3600);
        $minutes = \floor(($diff - ($hours * 3600)) / 60);
        $seconds = $diff - ($hours * 3600) - ($minutes * 60);

        return $hours.'hr '.$minutes.'m '.$seconds.'s';
    }

    return null;
}

function dateTimeFormatInPlainText($date): string
{
    return Carbon::parse($date)->format('F j, Y, g:i a');
}

function dateFormatInPlainText($date): string
{
    return Carbon::parse($date)->format('F j, Y');
}

if (! \function_exists('dateTimeInAmPm')) {
    function dateTimeInAmPm($time)
    {
        return $time ? Carbon::parse($time)->format('g:i A') : '';
    }
}

function onlyDateMonthYear($date): string
{
    return Carbon::parse($date)->isoFormat('Do MMM, YYYY');
}

if (! function_exists('timeInHourMinutes')) {
    function timeInHourMinutes($time)
    {
        if (! $time) {
            return '0h 0m';
        }

        [$hours, $minutes, $seconds] = explode(':', $time);

        return (int) $hours.'h '.(int) $minutes.'m';
    }
}

if (! \function_exists('showTime')) {
    function showTime($time)
    {
        if (empty($time)) {
            return 'N/A';
        }
        if (\settings('time_format') == 'h') {
            return Carbon::createFromFormat('H:i:s', $time)->format('h:i A');
        } else {
            return Carbon::createFromFormat('H:i:s', $time)->format('H:i');
        }
    }
}

if (! \function_exists('showTimeFromTimeStamp')) {
    function showTimeFromTimeStamp($time)
    {
        if (\settings('time_format') == 'h') {
            return Carbon::createFromFormat('Y-m-d H:i:s', $time)->format('h:i A');
        } else {
            return Carbon::createFromFormat('Y-m-d H:i:s', $time)->format('H:i');
        }
    }
}

if (! \function_exists('aboutSystem')) {
    function aboutSystem()
    {
        $data = [
            'version' => '',
            'release_date' => '',
        ];
        try {
            $about_system = \base_path('version.json');
            $about_system = \file_get_contents($about_system);
            $about_system = \json_decode($about_system, true);
            $data['version'] = $about_system['version'];
            $data['release_date'] = $about_system['release_date'];

            return $data;
        } catch (Throwable $th) {
            return $data;
        }
    }
}

function getBetweenDates($startDate, $endDate)
{
    $rangArray = [];

    $startDate = \strtotime($startDate);
    $endDate = \strtotime($endDate);

    for (
        $currentDate = $startDate;
        $currentDate <= $endDate;
        $currentDate += (86400)
    ) {
        $date = \date('Y-m-d', $currentDate);
        $rangArray[] = $date;
    }

    return $rangArray;
}
function isBreakRunning()
{
    $user = Auth::user();

    $takeBreak = EmployeeBreak::query()
        ->where([
            'user_id' => $user->id,
            'date' => \date('Y-m-d'),
        ])->whereNull('back_time')
        ->first();
    if ($takeBreak) {
        $status = 'start';
    } else {
        $status = 'end';
    }

    return $status;
}

function isAttendee()
{
    if (! Auth::check()) {
        return \emptyAttendance();
    }

    $user = Auth::user();
    $multiCheckinEnabled = \filter_var(\settings('multi_checkin') ?? false, FILTER_VALIDATE_BOOLEAN);

    // Directly query the attendance without cache
    $attendance = $user->attendances()
        ->select('id', 'check_in', 'check_out', 'date')
        ->when(! $multiCheckinEnabled, fn ($q) => $q->where('date', \now()->format('Y-m-d')))
        ->latest('id')
        ->first();

    if (! $attendance) {
        return \emptyAttendance();
    }

    $currentDate = now()->format('Y-m-d');

    if ($attendance->check_out && $attendance->date === $currentDate) {
        return [
            'id' => $attendance->id,
            'checkin' => true,
            'checkout' => true,
        ];
    }

    $maxWorkHours = \intval(\settings('max_work_hours') ?? 16);
    $timeDiff = \intval(\timeDifferenceHour($attendance->check_in, \now()));
    $checkoutExceeded = $timeDiff > $maxWorkHours;

    if (! $attendance->check_out && ! $checkoutExceeded) {
        return [
            'id' => $attendance->id,
            'checkin' => true,
            'checkout' => false,
        ];
    }

    return \emptyAttendance();
}

function timeDifferenceHour($start, $end = null): string
{
    $startTime = Carbon::parse($start);
    $endTime = Carbon::parse($end);
    $totalDuration = $startTime->diffInHours($endTime);

    return $totalDuration;
}

function emptyAttendance()
{
    return [
        'checkin' => false,
        'checkout' => false,
        'in_time' => null,
        'out_time' => null,
        'stay_time' => null,
    ];
}

function RawTable($table)
{
    return DB::table($table);
}

function dbTable($table, $select = '*', $where = [], $order = ['id', 'desc'])
{
    $query = \RawTable($table);
    if (\count($where) > 0) {
        $query = $query->where($where)->where('branch_id', \getBranchId());
    }

    return $query->select($select)->orderBy($order[0], $order[1]);
}

function menu($type = null)
{
    if ($type) {
        return Menu::with('page')->where('type', $type)->where('status_id', 1)->orderBy('position', 'asc')->get();
    } else {
        return Menu::with('page')->where('status_id', 1)->orderBy('position', 'asc')->get();
    }
}

if (! \function_exists('userLocal')) {
    function userLocal()
    {
        try {
            $user = Auth::user();
            if (isset($user->lang)) {
                $user_lang = $user->lang;
            } elseif (\settings('lang')) {
                $user_lang = \settings('lang');
            } else {
                $user_lang = App::getLocale();
            }

            return $user_lang;
        } catch (Throwable $th) {
            return 'en';
        }
    }
}

function isRTL()
{
    $rtlLocales = ['ar', 'he', 'fa', 'ur', 'ps', 'ckb', 'sd', 'ug', 'dv', 'yi'];

    return \in_array(\userLocal(), $rtlLocales);
    // return DB::table('languages')->where('code', userLocal())->first()->rtl;
}

if (! \function_exists('putEnvConfigration')) {
    function putEnvConfigration($envKey, $envValue)
    {
        $envValue = \str_replace('\\', '\\'.'\\', $envValue);
        $value = '"'.$envValue.'"';
        $envFile = \app()->environmentFilePath();
        $str = \file_get_contents($envFile);

        $str .= "\n";
        $keyPosition = \strpos($str, "{$envKey}=");

        if (\is_bool($keyPosition)) {
            $str .= $envKey.'="'.$envValue.'"';
        } else {
            $endOfLinePosition = \strpos($str, "\n", $keyPosition);
            $oldLine = \substr($str, $keyPosition, $endOfLinePosition - $keyPosition);
            $str = \str_replace($oldLine, "{$envKey}={$value}", $str);

            $str = \substr($str, 0, -1);
        }

        if (! \file_put_contents($envFile, $str)) {
            return false;
        } else {
            return true;
        }
    }
}

if (! \function_exists('hrm_languages')) {
    function hrm_languages()
    {
        $languages = HrmLanguage::where('status_id', 1)->get();

        return $languages;
    }
}
if (! \function_exists('api_setup')) {
    function api_setup($name, $slug)
    {
        $api_setup = ApiSetup::where('name', $name)->first("{$slug}");

        return $api_setup->{$slug};
    }
}

function currency_format($value, $symbol = null)
{
    if (Auth::check()) {
        $currency_symbol = Auth::user()->company->configs->where('key', 'currency_symbol')->first()->value;

        return $currency_symbol.''.$value;
    } elseif ($symbol) {
        return $symbol.''.$value;
    } else {
        return $value;
    }
}

function distanceCalculate($lat1, $lon1, $lat2, $lon2)
{
    $theta = $lon1 - $lon2;
    $dist = \sin(\deg2rad($lat1)) * \sin(\deg2rad($lat2)) + \cos(\deg2rad($lat1)) * \cos(\deg2rad($lat2)) * \cos(\deg2rad($theta));
    $dist = \acos($dist);
    $dist = \rad2deg($dist);

    // meters
    return $dist * 60 * 1.1515 * 1.609344 * 1000;
}
if (! \function_exists('plain_text')) {
    function plain_text($text)
    {
        return Str::title(Str::replace('_', ' ', Str::replace('-', ' ', $text)));
    }
}

function number_format_short($n)
{
    $n = \floatval($n);
    if ($n >= 0 && $n < 10) {
        $n_format = '0'.($n);
        $suffix = '';
    } elseif ($n >= 10 && $n < 1000) {
        // 1 - 999
        $n_format = \floor($n);
        $suffix = '';
    } elseif ($n > 1000) {
        $x = \round($n);
        $x_number_format = \number_format($x);
        $x_array = \explode(',', $x_number_format);
        $x_parts = ['k', 'm', 'b', 't'];
        $x_count_parts = \count($x_array) - 1;
        $x_display = $x;
        $x_display = $x_array[0].((int) $x_array[1][0] !== 0 ? '.'.$x_array[1][0] : '');
        $x_display .= $x_parts[$x_count_parts - 1];

        return $x_display;
    }

    return ! empty($n_format.$suffix) ? $n_format.$suffix : 0;
}

function cleanSpecialCharacters($string)
{
    $string = \str_replace(' ', '-', $string); // Replaces all spaces with hyphens.

    return \preg_replace('/[^A-Za-z0-9\-]/', '', $string); // Removes special chars.
}

// if function not exists
if (! \function_exists('getUserIpAddr')) {
    function getUserIpAddr()
    {
        try {
            $ipaddress = '';
            if (isset($_SERVER['HTTP_CLIENT_IP'])) {
                $ipaddress = $_SERVER['HTTP_CLIENT_IP'];
            } elseif (isset($_SERVER['HTTP_X_FORWARDED_FOR'])) {
                $ipaddress = $_SERVER['HTTP_X_FORWARDED_FOR'];
            } elseif (isset($_SERVER['HTTP_X_FORWARDED'])) {
                $ipaddress = $_SERVER['HTTP_X_FORWARDED'];
            } elseif (isset($_SERVER['HTTP_FORWARDED_FOR'])) {
                $ipaddress = $_SERVER['HTTP_FORWARDED_FOR'];
            } elseif (isset($_SERVER['HTTP_FORWARDED'])) {
                $ipaddress = $_SERVER['HTTP_FORWARDED'];
            } elseif (isset($_SERVER['REMOTE_ADDR'])) {
                $ipaddress = $_SERVER['REMOTE_ADDR'];
            } else {
                $ipaddress = \getUserIpAddr();
            }

            return $ipaddress;
        } catch (Throwable $th) {
            return \getUserIpAddr();
        }
    }
}
// spearate time and date from datetime
if (! \function_exists('separateDateAndTime')) {
    function separateDateAndTime($datetime, $date_or_time = null)
    {
        $date = \date('Y-m-d', \strtotime($datetime));
        $time = \date('H:i:s', \strtotime($datetime));
        if ($date_or_time == 'date') {
            return $date;
        } elseif ($date_or_time == 'time') {
            return $time;
        } else {
            return ['date' => $date, 'time' => $time];
        }
    }
}

function listCountStatus($count)
{
    $newCount = 0;
    $newCount = $count - 3;
    if ($newCount <= 0) {
        $newCount = 0;
    }

    return $newCount;
}
if (! \function_exists('file_logo')) {
    function file_logo($ext)
    {
        try {
            if ($ext != null) {
                return asset('assets/file_icons/'.$ext.'.png');
            } else {
                return asset('assets/file_icons/file.png');
            }
        } catch (Exception $e) {
            return \url('static/blank_small.png');
        }
    }
}
function getFileType($extension)
{
    try {
        $image = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg'];
        $video = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv', '3gp', 'webm'];
        $audio = ['mp3', 'wav', 'wma', 'ogg', 'aac', 'flac'];
        $pdf = ['pdf'];
        $doc = ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'csv'];
        $zip = ['zip', 'rar', '7z', 'tar', 'gz', 'gzip', 'iso', 'dmg'];
        if (\in_array($extension, $image)) {
            return 'image';
        } elseif (\in_array($extension, $video)) {
            return 'video';
        } elseif (\in_array($extension, $audio)) {
            return 'audio';
        } elseif (\in_array($extension, $pdf)) {
            return 'pdf';
        } elseif (\in_array($extension, $doc)) {
            return 'doc';
        } elseif (\in_array($extension, $zip)) {
            return 'zip';
        } else {
            return 'other';
        }
    } catch (Throwable $th) {
        return 'file';
    }
}

if (! \function_exists('static_asset')) {
    function static_asset($url)
    {
        if (\file_exists($url)) {
            return \url($url);
        } else {
            return \url('static/blank_small.png');
        }
    }
}

function setCurrentCompany($companyId)
{
    Session::forget('saas_company');
    Session::forget('session_branch_id');

    return Session::put('saas_company', $companyId);
}

if (! \function_exists('getCurrentCompany')) {
    function getCurrentCompany()
    {
        // Check if the user is authenticated
        if (Auth::check()) {
            // Check if the 'saas_company' value exists in the session
            if ($saasCompany = \session('saas_company')) {
                // Return the 'saas_company' value from the session
                return $saasCompany;
            } else {
                // Get the company ID from the authenticated user (if available)
                $companyId = \optional(Auth::user())->company_id;

                // Store the company ID in the session
                \session(['saas_company' => $companyId]);

                // Return the company ID
                return $companyId;
            }
        }

        // If the user is not authenticated, return null
        return Company::first();
    }
}

if (! \function_exists('getGeocodeData')) {
    function getGeocodeData($lat, $lng)
    {
        $apiKey = CompanyConfig::where('key', 'google')
            ->value('value');

        if (! $apiKey) {
            return 'Google API key not found';
        }

        $url = "https://maps.googleapis.com/maps/api/geocode/json?latlng={$lat},{$lng}&key={$apiKey}";

        try {
            $response = Http::get($url);
            $data = $response->json();

            if (isset($data['results']) && \is_array($data['results'])) {
                $largestFormattedAddress = '';

                foreach ($data['results'] as $result) {
                    if (isset($result['formatted_address'])) {
                        $formattedAddress = $result['formatted_address'];
                        if (empty($largestFormattedAddress) || \strlen($formattedAddress) > \strlen($largestFormattedAddress)) {
                            $largestFormattedAddress = $formattedAddress;
                        }
                    }
                }

                if (! empty($largestFormattedAddress)) {
                    return $largestFormattedAddress;
                }
            }

            return 'No address found';
        } catch (Exception $e) {
            return 'Error retrieving geocode data: '.$e->getMessage();
        }
    }
}

function expireNotification()
{
    return ExpireNotification::with('user.branch', 'user.company')->where(['receiver_id' => Auth::user()->id, 'is_read' => 0])->latest()->take(5)->get();
}

function expireNotificationCount()
{
    return ExpireNotification::where(['receiver_id' => Auth::user()->id, 'is_read' => 0])->count();
}

function unreadNotification()
{
    return Notification::where(['receiver_id' => Auth::user()->id, 'seen' => false])
        ->latest('id')
        ->take(5)
        ->get();
}

function allUnreadNotificationCount()
{
    return Notification::where(['receiver_id' => Auth::user()->id, 'seen' => false])->count();
}

function getNotifiableIds()
{
    $company_id = \getCompanyId();
    $cacheKey = 'notifiable_users_'.$company_id;

    // Check if the data is already cached
    if (Cache::has($cacheKey)) {
        return Cache::get($cacheKey);
    }

    $roles = Role::where('status_id', 1)
        ->where(function ($query) {
            $query->orWhere('slug', 'admin')
                ->orWhere('slug', 'hr');
        })
        ->pluck('id');

    $users = User::active()
        ->where(function ($query) use ($roles) {
            $query->where('is_hr', 1)
                ->orWhereIn('role_id', $roles);
        })
        ->select('id', 'status_id', 'company_id', 'role_id', 'name', 'email', 'department_id', 'designation_id')
        ->get();

    // Store the users data in cache for future use
    Cache::put($cacheKey, $users, \now()->addMinutes(60)); // You can adjust the cache duration as needed

    return $users;
}

function getChildMenu($menus)
{
    $str = '';
    if ($menus->count() > 0) {
        $str .= '<ol class="dd-list">';
        foreach ($menus as $menu) {
            $str .= '
                <li class="dd-item" data-parent_id="'.$menu->parent_id.'" data-id="'.$menu->id.'" data-name="'.$menu->menu_name.'" data-order="'.$menu->order.'">
                    <div class="dd-handle">'.$menu->menu_name.'</div>
            ';
            if ($menu->submenu && $menu->submenu->count() > 0) {
                $str .= \getChildMenu($menu->submenu);
            }
            $str .= '</li>';
        }
        $str .= '</ol>';
    }

    return $str;
}

if (! \function_exists('mainCompany')) {
    function mainCompany()
    {
        return Cache::remember('companies', 3600, function () {
            return DB::table('companies')->first();
        });
    }
}

if (! \function_exists('isMainCompany')) {
    function isMainCompany()
    {
        if (
            \in_array(\url('/'), \config('tenancy.central_domains'))
            && \isModuleActive('Saas')
            && \mainCompany()->is_main_company == 'yes'
            && \config('app.mood') === 'Saas'
        ) {
            return true;
        }

        return false;
    }
}

if (! \function_exists('isWhatsAppChatEnable')) {
    function isWhatsAppChatEnable()
    {
        return Cache::remember('isWhatsAppChatEnable', 3600, function () {
            return DB::table('settings')->where('name', 'is_whatsapp_chat_enable')->first()?->value;
        });
    }
}

if (! \function_exists('whatsAppChatNumber')) {
    function whatsAppChatNumber()
    {
        return Cache::remember('whatsAppChatNumber', 3600, function () {
            return DB::table('settings')->where('name', 'whatsapp_chat_number')->first()?->value;
        });
    }
}

if (! \function_exists('isTawkEnable')) {
    function isTawkEnable()
    {
        return Cache::remember('isTawkEnable', 3600, function () {
            return DB::table('settings')->where('name', 'is_tawk_enable')->first()?->value;
        });
    }
}

if (! \function_exists('tawkChatWidgetScript')) {
    function tawkChatWidgetScript()
    {
        return Cache::remember('tawkChatWidgetScript', 3600, function () {
            return DB::table('settings')->where('name', 'tawk_chat_widget_script')->first()?->value;
        });
    }
}

if (! \function_exists('isRecaptchaEnable')) {
    function isRecaptchaEnable()
    {
        return Cache::remember('isRecaptchaEnable', 3600, function () {
            return DB::table('settings')->where('name', 'is_recaptcha_enable')->first()?->value;
        });
    }
}

if (! \function_exists('recaptchaSitekey')) {
    function recaptchaSitekey()
    {
        return Cache::remember('recaptchaSitekey', 3600, function () {
            return DB::table('settings')->where('name', 'recaptcha_sitekey')->first()?->value;
        });
    }
}

if (! \function_exists('recaptchaSecret')) {
    function recaptchaSecret()
    {
        return Cache::remember('recaptchaSecret', 3600, function () {
            return DB::table('settings')->where('name', 'recaptcha_secret')->first()?->value;
        });
    }
}

if (! \function_exists('loadRecaptcha')) {
    function loadRecaptcha()
    {
        \Config::set('captcha.secret', \recaptchaSecret());
        \Config::set('captcha.sitekey', \recaptchaSitekey());
    }
}

if (! \function_exists('metaImage')) {
    function metaImage($id)
    {
        if (($asset = Cache::get("uploaded_asset_{$id}")) != null) {
            return \my_asset($asset->img_path);
        } else {
            return \url('assets/favicon.png');
        }

        return \url('assets/favicon.png');
    }
}

if (! \function_exists('subdomain')) {
    function subdomain()
    {
        if (! \isMainCompany() && \config('app.mood') === 'Saas') {
            $host = $host ?? \request()->getHost(); // default to current host
            $mainDomain = \config('app.domain'); // e.g., example.com

            // Remove the main domain to extract subdomain
            return \str_replace('.'.$mainDomain, '', $host);
        }

        return null;
    }
}

if (! \function_exists('activeSubscriptionFeatures')) {
    function activeSubscriptionFeatures()
    {
        if (! \isMainCompany() && \config('app.mood') == 'Saas') {
            return Cache::remember(\subdomain().'ActiveSubscriptionFeatures', 3600, function () {
                if (\config('app.single_db')) {
                    $featuresKey = \runningSubscription()?->features_key;
                } else {
                    $featuresKey = \runningSubscription()?->features_key;
                }

                if ($featuresKey) {
                    return \json_decode($featuresKey);
                }

                return [];
            });
        }

        return [];
    }
}

if (! \function_exists('hasFeature')) {
    function hasFeature($keyword)
    {
        if (\config('app.mood') != 'Saas' || ! \isModuleActive('Saas')) {
            return true;
        }
        if (\isMainCompany() && \config('app.mood') == 'Saas' && \isModuleActive('Saas')) {
            return true;
        }
        if (\in_array($keyword, \activeSubscriptionFeatures() ?? [])) {
            return true;
        }

        return false;
    }
}

if (! \function_exists('activeSubscriptionIsEmployeeLimit')) {
    function activeSubscriptionIsEmployeeLimit()
    {
        if (! \isMainCompany() && \config('app.mood') == 'Saas') {
            return Cache::remember(\subdomain().'activeSubscriptionIsEmployeeLimit', 3600, function () {
                return Subscription::active()->first()?->is_employee_limit ? true : false;
            });
        }

        return true;
    }
}

if (! \function_exists('activeSubscriptionEmployeeLimit')) {
    function activeSubscriptionEmployeeLimit()
    {
        if (! \isMainCompany() && \config('app.mood') == 'Saas') {
            return Cache::remember(\subdomain().'activeSubscriptionEmployeeLimit', 3600, function () {
                return Subscription::active()->first()?->employee_limit;
            });
        }

        return 999999999999;
    }
}

if (! \function_exists('getSubdomainName')) {
    function getSubdomainName()
    {
        $parsedUrl = \parse_url(\url()->full());
        $hostParts = \explode('.', $parsedUrl['host']);

        return $hostParts;
    }
}

if (! \function_exists('checkSingleCompanyIsDeactivated')) {
    function checkSingleCompanyIsDeactivated()
    {
        if (Schema::hasTable('companies')) {
            $subdomainParts = \getSubdomainName();

            $company = Company::where(function ($q) use ($subdomainParts) {
                $q->where('subdomain', @$subdomainParts[0])
                    ->orWhere('subdomain', @$subdomainParts[1]);
            })
                ->first();

            if (@$company->status_id == 4) {
                return true;
            }
        }

        return false;
    }
}

if (! \function_exists('getMainCompanyInfo')) {
    function getMainCompanyInfo()
    {
        $apiUrl = \env('APP_URL').'/api/saas/main-company/basic-info';
        $client = new Client;
        $res = $client->request('GET', $apiUrl);
        $responseBody = \json_decode($res->getBody(), true);

        return $responseBody['data'];
    }
}

if (! \function_exists('fetchDataViaAPI')) {
    function fetchDataViaAPI($apiUrl)
    {
        $client = new Client;
        $response = $client->request('GET', $apiUrl);
        $responseBody = $response->getBody()->getContents();
        $data = \json_decode($responseBody, true);

        $result = [];

        if (@$data['result']) {
            $result = $data['data'];
        }

        return $result;
    }
}

if (! \function_exists('emailTemplateShortCode')) {
    function emailTemplateShortCode()
    {
        return $list = [
            ['value' => 'name'],
            ['value' => 'email'],
            ['value' => 'phone'],
            ['value' => 'address'],
            ['value' => 'url'],
            ['value' => 'email'],
            ['value' => 'password'],
            ['value' => 'company_name'],
            ['value' => 'company_business'],
            ['value' => 'business_type'],
            ['value' => 'trade_license_number'],
            ['value' => 'company_credentials_table'],
            ['value' => 'company_subscription_plan_table'],
            ['value' => 'saas_admin_company'],
        ];
    }
}

if (! \function_exists('tenantMigrationPaths')) {
    function tenantMigrationPaths()
    {
        $filePath = \base_path('modules_statuses.json');

        // Define migrations paths
        if (\config('app.single_db')) {
            $migrationPaths = [
                'database/migrations/tenant',
            ];
        } else {
            $migrationPaths = [
                \database_path('migrations/tenant'),
            ];
        }

        // Add module migration paths
        if (\file_exists($filePath)) {
            $json_content = \file_get_contents($filePath);
            $modules = \json_decode($json_content, true);

            // Only keep modules with true value
            $modules = \array_filter($modules, function ($status) {
                return $status === true;
            });

            if (! \config('app.single_db')) {
                unset($modules['Saas']);

                foreach ($modules as $module => $status) {
                    $migrationPaths[] = \base_path("Modules/$module/Database/Migrations");
                }
            } else {
                foreach ($modules as $module => $status) {
                    $migrationPaths[] = "Modules/$module/Database/Migrations";
                }
            }
        }

        return $migrationPaths;
    }
}

if (! \function_exists('getCurrentDomainCompany')) {
    function getCurrentDomainCompany()
    {
        return Cache::rememberForever('company_by_host_'.\request()->getHost(), function () {
            return Company::query()
                ->where(function ($q) {
                    $q->where('subdomain', \request()->getHost())
                        ->orWhere('subdomain', \subdomainName(\request()->getHost()));
                })
                ->first() ?? Company::first();
        });
    }
}

if (! \function_exists('subdomainName')) {
    function subdomainName($host)
    {
        $segments = \explode('.', $host);

        return \array_shift($segments);
    }
}

if (! \function_exists('getMainCompanyStripeInfo')) {
    function getMainCompanyStripeInfo($name)
    {
        return Setting::where(['name' => $name])->first()?->value;
    }
}

if (! \function_exists('copyFiles')) {
    function copyFiles($dirName)
    {
        $sourceDir = \public_path('assets/copyable-assets');
        $destinationDir = \public_path('assets/'.$dirName);

        // Check if source exists
        if (! \is_dir($sourceDir)) {
            throw new Exception('Source directory does not exist: '.$sourceDir);
        }

        // Check if the destination directory exists
        if (! \is_dir($destinationDir)) {
            if (! \mkdir($destinationDir, 0775, true)) {
                throw new Exception('Failed to create directory: '.$destinationDir);
            }
        }

        // Ensure directory is writable
        if (! \is_writable($destinationDir)) {
            throw new Exception('Destination directory is not writable: '.$destinationDir);
        }

        File::copyDirectory($sourceDir, $destinationDir);
    }
}

if (! \function_exists('copyDirectory')) {
    function copyDirectory($source, $destination)
    {
        if (! File::exists($destination)) {
            File::makeDirectory($destination, 0755, true);
        }

        $items = File::allFiles($source);
        $directories = File::directories($source);

        foreach ($items as $file) {
            $destinationPath = $destination.'/'.$file->getRelativePathname();
            File::ensureDirectoryExists(\dirname($destinationPath));
            File::copy($file->getRealPath(), $destinationPath);
        }

        foreach ($directories as $dir) {
            $relativePath = \str_replace($source, '', $dir);
            \copyDirectory($dir, $destination.$relativePath);
        }
    }
}

if (! \function_exists('runningSubscription')) {
    function runningSubscription()
    {
        $cacheKey = 'is_expired_running_subscription';

        if (\config('app.single_db')) {
            $companyId = \getCurrentDomainCompany()?->id;
            $cacheKey .= '_company_'.$companyId;
        } else {
            $cacheKey .= '_multi_tenant';
        }

        return Cache::remember($cacheKey, 3600, function () {
            if (\config('app.single_db')) {
                $runningSubscription = SaasSubscription::approved()
                    ->runningSubscription()
                    ->where('company_id', \getCurrentDomainCompany()->id)
                    ->first();
            } else {
                $runningSubscription = Subscription::active()->first();
            }

            return $runningSubscription;
        });
    }
}

if (! \function_exists('isExpiredRunningSubscription')) {
    function isExpiredRunningSubscription()
    {
        return \runningSubscription()?->expiry_date && \runningSubscription()?->expiry_date < \date('Y-m-d') ? true : false;
    }
}

if (! \function_exists('isTardyApplicable')) {
    function isTardyApplicable($startOrEndTime, $checkInOrOutTime, $considerTime = 0, $isLate = true, $isSameDayShiftEnd = true, $type = 'checkin')
    {
        $diffInMinutes = \calculateTimeDifference($startOrEndTime, $checkInOrOutTime, $considerTime, $isLate, $isSameDayShiftEnd);

        return TardyRule::query()
            ->where('type', $type)
            ->whereHas('assigns', function ($query) {
                $query->where('user_id', Auth::id());
            })
            ->where('threshold_minutes', '<=', $diffInMinutes)
            ->first();
    }
}

if (! \function_exists('isBreakTimeOver')) {
    function isBreakTimeOver($totalDurationInMinutes)
    {
        return TardyRule::query()
            ->where('type', 'break')
            ->whereHas('assigns', function ($query) {
                $query->where('user_id', Auth::id());
            })
            ->where('threshold_minutes', '<=', $totalDurationInMinutes)
            ->first();
    }
}

function returnAuthorInforHtml($data)
{
    $createdAt = $data->created_at ? $data->created_at->format('d M Y h:i A') : 'Unknown';
    $createdBy = \optional($data->CreatedBy)->employee_id ?? '<span class="text-danger">Unknown</span>';
    $updatedAt = $data->updated_at ? $data->updated_at->format('d M Y h:i A') : 'Unknown';
    $updatedBy = \optional($data->UpdatedBy)->employee_id ?? '<span class="text-danger">Unknown</span>';
    $createdByProfile = $createdBy;
    $updatedByProfile = $updatedBy;
    if (! empty($data->CreatedBy)) {
        $createdByProfile = '<a class="text-success" target="_blank" href="'.\route('user.profile', [$data->CreatedBy->id, 'personal']).'">'.$createdBy.'</a>';
    }
    if (! empty($data->UpdatedBy)) {
        $updatedByProfile = '<a class="text-success" target="_blank" href="'.\route('user.profile', [$data->UpdatedBy->id, 'personal']).'">'.$updatedBy.'</a>';
    }

    return '
        <p class="d-flex align-items-center gap-2"> <i class="las la-clock font-size-16"></i> <span class="text-primary">'.$createdAt.' </span>  By '.$createdByProfile.'</p>
        <p class="d-flex align-items-center gap-2"> <i class="las la-history font-size-16"></i> <span class="text-warning">'.$updatedAt.' </span>  By '.$updatedByProfile.'</p>
    ';
}

if (! \function_exists('calculateTimeDifference')) {
    function calculateTimeDifference($startTime, $endTime, $considerTime = 0, $isLate = true, $isSameDayShiftEnd = true)
    {
        $startTime = Carbon::parse($startTime);
        $endTime = Carbon::parse($endTime);

        if ($isLate) {
            $adjustedStartTime = $startTime->copy()->addMinutes((int) $considerTime ?? 0);
            if ($endTime->greaterThan($adjustedStartTime)) {
                return $adjustedStartTime->diffInMinutes($endTime);
            }
        } else {
            if (! $isSameDayShiftEnd && $startTime->greaterThan($endTime)) {
                $endTime->subDay();
            }

            if ($endTime->lessThan($startTime)) {
                return $startTime->diffInMinutes($endTime);
            }
        }

        return 0;
    }
}

if (! \function_exists('convertTimeToReadableFormat')) {
    function convertTimeToReadableFormat($time)
    {
        [$hours, $minutes, $seconds] = explode(':', $time);

        $result = [];

        if ((int) $hours > 0) {
            $result[] = (int) $hours.'h';
        }

        if ((int) $minutes > 0) {
            $result[] = (int) $minutes.'m';
        }

        if ((int) $seconds > 0) {
            $result[] = (int) $seconds.'s';
        }

        return implode(' ', $result) ?: '0s';
    }
}

function storeNotification($data)
{
    try {
        $message = @$data['message'] ?? '';
        $sender_message = @$data['sender_message'] ?? '';
        $redirectUrlForWeb = @$data['redirectUrlForWeb'] ?? '';
        $redirectUrlForApp = @$data['redirectUrlForApp'] ?? '';
        $forSelf = @$data['forSelf'] ?? false;
        $sendOnlyAdmin = @$data['sendOnlyAdmin'] ?? false;
        (new Modules\Notify\Services\NotificationService)->storeNotification($message, $sender_message, $redirectUrlForWeb, $redirectUrlForApp, $forSelf, $data, $sendOnlyAdmin);

        return true;
    } catch (Throwable $e) {
        Log::error($e->getMessage());
    }
}

if (! \function_exists('calculateTotalHours')) {
    function calculateTotalHours($startTime, $endTime)
    {
        $start = Carbon::parse($startTime);
        $end = Carbon::parse($endTime);

        // Calculate the difference as a DateInterval object
        $diff = $start->diff($end);

        // Format the difference as H:m:s
        return \sprintf('%02d:%02d:%02d', $diff->h + ($diff->d * 24), $diff->i, $diff->s);
    }
}

if (! \function_exists('calculateTotalHoursHumanReadable')) {
    function calculateTotalHoursHumanReadable($startTime, $endTime)
    {
        $start = Carbon::parse($startTime);
        $end = Carbon::parse($endTime);

        // Calculate the difference in total minutes
        $totalMinutes = $start->diffInMinutes($end);

        // Calculate hours and remaining minutes
        $hours = \floor($totalMinutes / 60);
        $minutes = $totalMinutes % 60;

        // Build the human-readable format
        $humanReadable = '';

        if ($hours > 0) {
            $humanReadable .= $hours.' hour'.($hours > 1 ? 's' : '');
        }

        if ($minutes > 0) {
            $humanReadable .= ($hours > 0 ? ' ' : '').$minutes.' minute'.($minutes > 1 ? 's' : '');
        }

        return $humanReadable ?: '0 minutes'; // If no difference, return '0 minutes'
    }
}

if (! \function_exists('isValidPerson')) {
    function isValidPerson($createdById = null)
    {
        try {
            if (\isAdminOrHr()) {
                return true;
            }

            return $createdById ? ($createdById == Auth::id() ? true : false) : true;
        } catch (Throwable $th) {
            throw $th;
        }
    }
}

if (! \function_exists('getRelatedUserIds')) {
    function getRelatedUserIds()
    {
        try {
            return User::where('id', Auth::id())->orWhere('manager_id', Auth::id())->pluck('id')->toArray();
        } catch (Throwable $th) {
            throw $th;
        }
    }
}

if (! \function_exists('isSuperAdmin')) {
    function isSuperAdmin()
    {
        return @Auth::user()->role->slug == 'superadmin' ? true : false;
    }
}

if (! \function_exists('isAdmin')) {
    function isAdmin()
    {
        return \in_array(\optional(Auth::user()->role)->slug, ['superadmin', 'admin']);
    }
}

if (! \function_exists('isHr')) {
    function isHr()
    {
        return @Auth::user()->role->slug == 'hr' ? true : false;
    }
}

if (! \function_exists('isOfficeManager')) {
    function isOfficeManager()
    {
        return @Auth::user()->role->slug == 'officemanager' ? true : false;
    }
}

if (! \function_exists('isAdminOrHr')) {
    function isAdminOrHr()
    {
        if (Auth::user()->is_admin == 1 || Auth::user()->is_hr == 1) {
            return true;
        }

        $user = Auth::user();

        return \in_array(\optional($user->role)->slug, ['superadmin', 'admin', 'hr']);
    }
}

if (! \function_exists('isAdminOrOfficeManager')) {
    function isAdminOrOfficeManager()
    {
        return \in_array(@Auth::user()->role->slug, ['superadmin', 'admin', 'officemanager']);
    }
}

if (! \function_exists('isAdminOrHrOrOfficeManager')) {
    function isAdminOrHrOrOfficeManager()
    {
        return \in_array(@Auth::user()->role->slug, ['superadmin', 'admin', 'officemanager', 'hr']);
    }
}

if (! \function_exists('isStaffManager')) {
    function isStaffManager()
    {
        $users = User::where('manager_id', Auth::id())->count();

        return $users > 0 ? true : false;
    }
}

if (! \function_exists('getUserIdsUnderManager')) {
    function getUserIdsUnderManager()
    {
        try {
            return User::where('manager_id', Auth::id())->pluck('id')->toArray();
        } catch (Throwable $th) {
            throw $th;
        }
    }
}

if (! \function_exists('userCaseFirstLetter')) {
    function userCaseFirstLetter($keyWord)
    {
        return \ucwords(\str_replace('_', ' ', \strtolower($keyWord)));
    }
}

if (! \function_exists('generateDateRange')) {
    function generateDateRange($startDate, $endDate)
    {
        $dates = [];
        $currentDate = Carbon::parse($startDate);

        while ($currentDate->lte(Carbon::parse($endDate))) {
            $dates[] = $currentDate->format('Y-m-d');
            $currentDate->addDay();
        }

        return $dates;
    }
}

if (! \function_exists('getAllDaysOfMonth')) {
    function getAllDaysOfMonth($month)
    {
        $date = Carbon::createFromDate($month, 1);
        $daysInMonth = $date->daysInMonth;
        $days = [];

        for ($day = 1; $day <= $daysInMonth; $day++) {
            $days[] = $day;
        }

        return $days;
    }
}

if (! \function_exists('getDateRange')) {
    function getDateRange($start_date, $end_date)
    {
        $start = Carbon::parse($start_date);
        $end = Carbon::parse($end_date);

        $period = CarbonPeriod::create($start, $end);

        $dates = [];

        foreach ($period as $date) {
            $dates[] = $date->format('Y-m-d');
        }

        return $dates;
    }
}

if (! \function_exists('convertMinutesToTime')) {
    function convertMinutesToTime(int $minutes): string
    {
        $hours = \floor($minutes / 60);
        $remainingMinutes = $minutes % 60;
        $seconds = 0;

        return \sprintf('%02d:%02d:%02d', $hours, $remainingMinutes, $seconds);
    }
}

if (! \function_exists('convertTimeToMinutes')) {
    function convertTimeToMinutes(string $time): int
    {
        [$hours, $minutes, $seconds] = \explode(':', $time);

        return ($hours * 60) + $minutes + \floor($seconds / 60);
    }
}

// if (! \function_exists('lateCheckinCheck')) {
//     function lateCheckinCheck($startTime, $checkInTime, $considerTime = 0, $isSameDayShiftEnd = true)
//     {
//         return \calculateTimeDifference($startTime, $checkInTime, $considerTime, true, $isSameDayShiftEnd);
//     }
// }

// if (! \function_exists('lateCheckInHours')) {
//     function lateCheckInHours($startTime, $checkInTime, $considerTime = 0, $isSameDayShiftEnd = true)
//     {
//         $minutes = \calculateTimeDifference($startTime, $checkInTime, $considerTime, true, $isSameDayShiftEnd) ?? 0;
//         $hours = \floor($minutes / 60);

//         return $hours;
//     }
// }

if (! function_exists('isUserBreakRunning')) {
    function isUserBreakRunning()
    {
        $userBreak = UserBreak::where('user_id', Auth::user()->id)
            ->whereNull('end_time')
            ->first();

        return $userBreak ? true : false;
    }
}

// Image Placeholder if missing User image
if (! \function_exists('defaultAvatar')) {
    // defaultAvatar(FilePath, Name, ImageSize, Gender, ImageId)
    // Use: <img src="{{ defaultAvatar($file->path, Auth::user()->name, 150, Auth::user()->gender, "") }}">
    // FilePath: Image Path. (With Old Image Upload System it's Optional let it '')(Full Path).
    // Name: User Name.
    // ImageSize: Render 1:1 Image Ration with Required Size.
    // Gender: [male, female, other, ''] (Optional). Color will be different based on Gender
    // ImageId: Image Id (Optional). On Old Image Upload System Used (image_id)

    function defaultAvatar($image = null, $name = 'No Name', $size = 96, $gender = 'other', $id = null)
    {
        return null;
    }
}

if (! function_exists('catchHandler')) {
    function catchHandler($exception)
    {
        if (request()->expectsJson()) {
            return response()->json([
                'result' => false,
                'message' => $exception->getMessage(),
                'error' => $exception->getMessage(),
            ], $exception->getCode() ?? 500);
        }

        if (config('app.debug') == true) {
            dd($exception);
        }

        info($exception->getMessage());

        return \redirect()->back()->with('error', _trans('alert.Something went wrong'));
    }
}

if (! \function_exists('defaultAvatar')) {
    function defaultAvatar($image = null, $name = 'No Name', $size = 96, $gender = 'other', $id = null)
    {
        return null;
    }
}

if (! \function_exists('isWeekend')) {
    function isWeekend(array $weekends)
    {
        $currentDay = now()->format('l');

        return in_array($currentDay, $weekends);

    }
}

if (! \function_exists('throwException')) {
    function throwException($message, $code = 400)
    {
        throw new Exception($message, $code);
    }
}

if (! function_exists('defaultTitle')) {
    function defaultTitle($text)
    {
        return Str::title(str_replace('-', ' ', str_replace('_', ' ', $text)));
    }
}

if (! function_exists('isPreviousDay')) {
    function isPreviousDay(string $shiftStart, string $checkIn): bool
    {
        // Convert to DateTime objects
        $format = 'Y-m-d H:i:s';

        // Assume shift start and check-in are both today initially
        $today = date('Y-m-d');
        $shiftTime = new DateTime("{$today} {$shiftStart}");
        $checkInTime = new DateTime("{$today} {$checkIn}");

        // Midnight reference (next day 00:00)
        $midnight = new DateTime($shiftTime->format('Y-m-d').' 00:00:00');
        $midnight->modify('+1 day');

        // If check-in is earlier than shift (i.e., after midnight), add 1 day to check-in
        if ($checkInTime < $shiftTime) {
            $checkInTime->modify('+1 day');
        }

        // Duration from shift to midnight
        $shiftToMidnight = $midnight->getTimestamp() - $shiftTime->getTimestamp();
        $shiftToCheckIn = $checkInTime->getTimestamp() - $shiftTime->getTimestamp();

        // Convert to hours
        $shiftHours = $shiftToMidnight / 3600;
        $checkInHours = $shiftToCheckIn / 3600;

        return $shiftHours < $checkInHours;
    }
}

if (! function_exists('getDurationFormatted')) {
    function getDurationFormatted($startTime, $endTime): string
    {
        $start = Carbon::parse($startTime);
        $end = Carbon::parse($endTime);

        $diff = $start->diff($end);

        return sprintf('%dh %02dm', $diff->h + ($diff->d * 24), $diff->i);
    }
}
