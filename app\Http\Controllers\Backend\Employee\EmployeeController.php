<?php

namespace App\Http\Controllers\Backend\Employee;

use App\Helpers\CoreApp\Traits\FileHandler;
use App\Http\Controllers\Controller;
use App\Models\coreApp\Relationship\RelationshipTrait;
use App\Repositories\Admin\RoleRepository;
use App\Repositories\Hrm\Attendance\AttendanceRepository;
use App\Repositories\Hrm\Department\DepartmentRepository;
use App\Repositories\Hrm\Designation\DesignationRepository;
use App\Repositories\Hrm\Employee\AppoinmentRepository;
use App\Repositories\Hrm\Leave\LeaveRequestRepository;
use App\Repositories\Hrm\Notice\NoticeRepository;
use App\Repositories\Hrm\Payroll\AdvanceRepository;
use App\Repositories\Hrm\Payroll\PayrollSetUpRepository;
use App\Repositories\Hrm\Payroll\SalaryRepository;
use App\Repositories\Hrm\Visit\VisitRepository;
use App\Repositories\ProfileRepository;
use App\Repositories\Report\AttendanceReportRepository;
use App\Repositories\Settings\ProfileUpdateSettingRepository;
use App\Repositories\Support\SupportTicketRepository;
use App\Repositories\UserRepository;
use App\Services\Award\AwardService;
use App\Services\Management\ProjectService;
use App\Services\Task\TaskService;
use App\Services\Travel\TravelService;
use Brian2694\Toastr\Facades\Toastr;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class EmployeeController extends Controller
{
    use FileHandler, RelationshipTrait;

    public function __construct(
        protected UserRepository $user,
        protected RoleRepository $role,
        protected ProfileRepository $profile,
        protected DesignationRepository $designation,
        protected DepartmentRepository $department,
        protected ProfileUpdateSettingRepository $profileSetting,
        protected SupportTicketRepository $supportTicketRepository,
        protected AttendanceReportRepository $attendanceReportRepository,
        protected NoticeRepository $noticeRepository,
        protected VisitRepository $visitRepository,
        protected AppoinmentRepository $appointmentRepository,
        protected SalaryRepository $salaryRepository,
        protected ProjectService $projectService,
        protected TaskService $taskService,
        protected AwardService $awardService,
        protected TravelService $travelService,
        protected AttendanceRepository $attendance_repo,
        protected LeaveRequestRepository $leaveRequest,
        protected PayrollSetUpRepository $payrollSetupRepository,
        protected AdvanceRepository $advanceRepository
    ) {}

    public function authProfile(Request $request, $slug)
    {
        $user = Auth::user();
        if (! \myCompanyData($user->company_id)) {
            Toastr::warning('You Can\'t access!', 'Access Denied');

            return \redirect()->back();
        }

        $data['title'] = _trans('common.Employee Details');
        $data['slug'] = $slug;
        $data['id'] = $user->id;
        $request['user_id'] = $user->id;
        $data['show'] = $this->profile->getProfile($request, $slug);

        if ($slug == 'personal') {
            $data['designations'] = $this->designation->getActiveAll();
            $data['departments'] = $this->department->getActiveAll();
        } elseif ($slug == 'attendance') {
            $data['class'] = 'attendance_table';
            $data['fields'] = $this->attendance_repo->fields();
            $data['table'] = \route('attendance.em_profile');
            $data['url_id'] = 'attendance_table_url';
        } elseif ($slug == 'notice') {
            $data['fields'] = $this->noticeRepository->fields();
            $data['url_id'] = 'notice_table_url';
            $data['class'] = 'table_class';
            $data['table'] = \route('notice.em_profile');
        } elseif ($slug == 'leave_request') {
            $data['class'] = 'leave_request_table';
            $data['fields'] = $this->leaveRequest->fields();
            $data['table'] = \route('leave_request.em_profile');
            $data['url_id'] = 'leave_request_table_url';
        } elseif ($slug == 'visit') {
            $data['fields'] = $this->visitRepository->fields();
            $data['table'] = \route('visit.em_profile');
            $data['url_id'] = 'visit_table_url';
            $data['class'] = 'table_class';
        } elseif ($slug == 'phonebook') {
            $data['fields'] = $this->user->phonebookFields();
            $data['table'] = \route('phonebookTable.em_profile');
            $data['url_id'] = 'phonebook_table_url';
            $data['class'] = 'table_class';
        } elseif ($slug == 'appointment') {
            $data['fields'] = $this->appointmentRepository->fields();
            $data['table'] = \route('appointment.em_profile');
            $data['url_id'] = 'appointment_table_url';
            $data['class'] = 'table_class';
        } elseif ($slug == 'ticket') {
            $data['fields'] = $this->supportTicketRepository->fields();
            $data['table'] = \route('supportTicket.em_profile');
            $data['url_id'] = 'support_table_url';
            $data['class'] = 'table_class';
        } elseif ($slug == 'advance') {
            $data['class'] = 'salary_advance_table';
            $data['fields'] = $this->advanceRepository->fields();
            $data['table'] = \route('advance.em_profile');
            $data['url_id'] = 'salary_advance_table_url';
        } elseif ($slug == 'commission') {
            $data['fields'] = $this->payrollSetupRepository->setCommissionFields();
            $data['table'] = \route('commission.em_profile');
            $data['url_id'] = 'payroll_item_set_up_table_url';
            $data['class'] = 'table_class';
        } elseif ($slug == 'salary') {
            $data['fields'] = $this->salaryRepository->fields();
            $data['table'] = \route('salary.em_profile');
            $data['url_id'] = 'salary_table_url';
            $data['class'] = 'salary_table';
        } elseif ($slug == 'project') {
            $data['fields'] = $this->projectService->fields();
            $data['table'] = \route('project.em_profile');
            $data['url_id'] = 'project_table_url';
            $data['class'] = 'table_class';
        } elseif ($slug == 'task') {
            $data['fields'] = $this->taskService->fields();
            $data['table'] = \route('task.em_profile');
            $data['url_id'] = 'task_table_url';
            $data['class'] = 'task_table';
        } elseif ($slug == 'award') {
            $data['table'] = \route('award.em_profile');
            $data['fields'] = $this->awardService->fields();
            $data['title'] = _trans('award.Award List');
            $data['url_id'] = 'award_table_url';
            $data['class'] = 'award_table_class';
        } elseif ($slug == 'travel') {
            $data['table'] = \route('travel.em_profile');
            $data['url_id'] = 'travel_table_url';
            $data['fields'] = $this->travelService->fields();
            $data['class'] = 'travel_table_class';
        }
        $data['name'] = @$user->name;
        $data['department'] = @$user->department->title;

        return \view('backend.profile.details', \compact('data'));
    }

    public function fileView($image)
    {
        try {
            $data['image'] = \uploaded_asset($image);

            return \view('backend.modal.image_view', \compact('data'));
        } catch (\Throwable $th) {
            return \response()->json('fail');
        }
    }
}
