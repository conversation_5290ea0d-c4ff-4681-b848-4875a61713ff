{"Shift store successfully": "Shift store successfully", "Company has been created successfully": "Company has been created successfully", "Company approval is currently in progress; kindly allow some time for the final approval to be processed": "Company approval is currently in progress; kindly allow some time for the final approval to be processed", "Designation store successfully": "Designation store successfully", "Department store successfully": "Department store successfully", "Employee successfully Created": "Employee successfully Created", "Weekend update successfully": "Weekend update successfully", "Shift update successfully": "Shift update successfully", "Ip Address store successfully": "Ip Address store successfully", "Ip Address updated successfully": "Ip Address updated successfully", "Location update successfully": "Location update successfully", "Success": "Success", "Branch Delete successfully": "Branch Delete successfully", "Status has been inactivated successfully": "Status has been inactivated successfully", "Upgrade Plan request sent successfully": "Upgrade Plan request sent successfully", "Leave type store successfully": "Leave type store successfully", "Assign leave store successfully": "Assign leave store successfully", "You cannot do it for demo": "You cannot do it for demo", "You cannot delete for demo": "You cannot delete for demo", "Category created successfully": "Category created successfully", "Travel type created successfully": "Travel type created successfully", "Project created successfully": "Project created successfully", "Advance type created successfully": "Advance type created successfully", "Advance pay created successfully": "Advance pay created successfully", "Advance pay approved successfully": "Advance pay approved successfully", "Expense created successfully": "Expense created successfully", "Complain Delete successfully": "<PERSON><PERSON><PERSON> successfully", "Verbal warning Delete successfully": "Verbal warning Delete successfully", "Subject is required": "Subject is required", "Description is required": "Description is required", "Discussion created successfully": "Discussion created successfully", "Competence type created successfully": "Competence type created successfully", "Appraisal created successfully": "Appraisal created successfully", "Goal type created successfully": "Goal type created successfully", "Indicator created successfully": "Indicator created successfully", "Task created successfully": "Task created successfully", "Note created successfully": "Note created successfully", "Note Updated successfully": "Note Updated successfully", "Users imported successfully": "Users imported successfully", "Select an department": "Select an department", "Please select a department": "Please select a department", "Please select a month": "Please select a month", "Salary generated successfully": "Salary generated successfully", "Salary already generated": "Salary already generated", "Payslip Delete successfully": "Payslip Delete successfully", "Account update successfully": "Account update successfully", "Award type created successfully": "Award type created successfully", "Award created successfully": "Award created successfully", "Total late check in count": "Total late check in count", "Total early check out count": "Total early check out count", "Total extra leave in count": "Total extra leave in count", "Total monthly leave count": "Total monthly leave count", "Please complete salary calculation to get the final salary": "Please complete salary calculation to get the final salary", "HR role updated successfully": "HR role updated successfully", "Store successfully": "Store successfully", "Assign leave update successfully": "Assign leave update successfully", "Commission created successfully": "Commission created successfully", "Please enter a comment": "Please enter a comment", "Role update successfully": "Role update successfully", "Department update successfully": "Department update successfully", "Designation update successfully": "Designation update successfully", "Role delete successfully": "Role delete successfully", "Role activate successfully": "Role activate successfully", "Mail not send": "Mail not send", "Update successfully": "Update successfully", "Updated successfully": "Updated successfully", "Location Delete successfully": "Location Delete successfully", "Leave type update successfully": "Leave type update successfully", "Data already exists": "Data already exists", "Commission Update successfully": "Commission Update successfully", "Commission Delete successfully": "Commission Delete successfully", "Item created successfully": "Item created successfully", "Commission update successfully": "Commission update successfully", "Advance type update successfully": "Advance type update successfully", "Advance type delete successfully": "Advance type delete successfully", "Advance pay update successfully": "Advance pay update successfully", "Account created successfully": "Account created successfully", "Payment method created successfully": "Payment method created successfully", "Paid successfully": "<PERSON><PERSON> successfully", "Advance pay delete successfully": "Advance pay delete successfully", "Salary pay successfully": "Salary pay successfully", "Meeting Delete successfully": "Meeting Delete successfully", "Project file created successfully": "Project file created successfully", "File Delete successfully": "File Delete successfully", "Comment created successfully": "Comment created successfully", "Discussion Delete successfully": "Discussion Delete successfully", "Note Delete successfully": "Note Delete successfully", "Task completed successfully": "Task completed successfully", "Task Updated successfully": "Task Updated successfully", "Project Completed successfully": "Project Completed successfully", "Award type update successfully": "Award type update successfully", "Award type Delete successfully": "Award type Delete successfully", "Award type inactivate successfully": "Award type inactivate successfully", "Award type activate successfully": "Award type activate successfully", "Award Updated successfully": "Award Updated successfully", "Award Delete successfully": "Award Delete successfully", "Account delete successfully": "Account delete successfully", "Category update successfully": "Category update successfully", "Category delete successfully": "Category delete successfully", "Payment method updated successfully": "Payment method updated successfully", "Payment method delete successfully": "Payment method delete successfully", "Deposit created successfully": "Deposit created successfully", "Deposit update successfully": "Deposit update successfully", "Deposit delete successfully": "Deposit delete successfully", "Expense Update successfully": "Expense Update successfully", "Expense approved successfully": "Expense approved successfully", "Branch Created successfully": "Branch Created successfully", "Branch Update successfully": "Branch Update successfully", "You are not allowed to perform the delete action in demo mode": "You are not allowed to perform the delete action in demo mode", "User activate successfully": "User activate successfully", "Attendance Deduction generated successfully": "Attendance Deduction generated successfully", "Appeal has been sent successfully": "Appeal has been sent successfully", "Deduction considered successfully": "Deduction considered successfully", "Bulletin created successfully": "Bulletin created successfully", "Bulletin updated successfully": "Bulletin updated successfully", "Location created successfully": "Location created successfully", "Location already exists": "Location already exists", "Notification deleted successfully": "Notification deleted successfully", "Location not found": "Location not found", "Reward type created successfully": "Reward type created successfully", "Reward type update successfully": "Reward type update successfully", "Reward type Delete successfully": "Reward type Delete successfully", "Language store successfully": "Language store successfully", "User inactivate successfully": "User inactivate successfully", "Department inactivate successfully": "Department inactivate successfully", "Department activate successfully": "Department activate successfully", "Update failed": "Update failed", "Leave request created successfully": "Leave request created successfully", "Leave request deleted successfully": "Leave request deleted successfully", "Leave request updated successfully": "Leave request updated successfully", "Mail not sent": "Mail not sent", "Department Delete successfully": "Department Delete successfully", "HR policy created successfully": "HR policy created successfully", "HR policy updated successfully": "HR policy updated successfully", "HR policy deleted successfully": "HR policy deleted successfully", "Asset created successfully": "Asset created successfully", "Asset updated successfully": "Asset updated successfully", "Asset assigned successfully": "Asset assigned successfully"}