<?php

namespace App\Models\Hrm\Attendance;

use App\Models\coreApp\BaseModel;
use App\Models\Traits\CompanyBranchTrait;
use App\Models\User;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\Cache;
use Modules\Break\Entities\UserBreak;
use Modules\SpecialAttendance\Entities\DutyCalendar;

class Attendance extends BaseModel
{
    use CompanyBranchTrait;

    protected $guarded = ['id'];

    protected $casts = [
        'check_in_info' => 'array',
        'check_out_info' => 'array',
        'attendance_log' => 'array',
    ];

    protected static function booted()
    {
        static::saved(function ($attendance) {
            static::clearUserAttendanceCache($attendance);
        });

        static::deleted(function ($attendance) {
            static::clearUserAttendanceCache($attendance);
        });
    }

    protected static function clearUserAttendanceCache($attendance)
    {
        $baseKey = 'user_attendance_'.$attendance->user_id;
        $specificKey = $baseKey.'_'.$attendance->date;

        Cache::forget($baseKey);
        Cache::forget($specificKey);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function schedule(): BelongsTo
    {
        return $this->belongsTo(DutySchedule::class, 'duty_schedule_id');
    }

    public function breaks(): HasMany
    {
        return $this->hasMany(UserBreak::class, 'date', 'date');
    }

    public function dutyCalendar(): BelongsTo
    {
        return $this->belongsTo(DutyCalendar::class, 'duty_calendar_id', 'id');
    }

    public function scopeToday($query)
    {
        return $query->where('date', \date('Y-m-d'));
    }
}
