<?php

namespace App\Http\Controllers\Backend\Designation;

use App\Helpers\CoreApp\Traits\ApiReturnFormatTrait;
use App\Http\Controllers\Controller;
use App\Http\Requests\DesignationReqeust;
use App\Models\coreApp\Relationship\RelationshipTrait;
use App\Models\Hrm\Designation\Designation;
use App\Repositories\Hrm\Designation\DesignationRepository;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class DesignationController extends Controller
{
    use ApiReturnFormatTrait,
        RelationshipTrait;

    protected DesignationRepository $designation;

    protected $model;

    public function __construct(DesignationRepository $designation, Designation $model)
    {
        $this->designation = $designation;
        $this->model = $model;
    }

    public function index(Request $request)
    {
        $data['title'] = _trans('common.Designations');
        $data['formTitle'] = _trans('common.Add New Designation');
        $data['collection'] = $this->designation->getPaginateData($request);

        if ($request->filled('id')) {
            $data['formTitle'] = _trans('common.Edit Designation');
            $data['designation'] = $this->designation->show($request->id);
        }

        return \view('backend.designation.index')->with($data);
    }

    public function store(DesignationReqeust $request)
    {
        try {
            $this->designation->store($request->validated());

            return \redirect()->route('designation.index')->with('success', 'Designation created successfully');
        } catch (\Throwable $th) {
            Log::error($th->getMessage());

            return \redirect()->back()->with('error', _trans('alert.Something went wrong!'));
        }
    }

    public function update(DesignationReqeust $request, $id)
    {
        if (\config('app.style') === 'demo' || \env('APP_STYLE') === 'demo') {
            return \redirect()->back()->with('error', _trans('alert.You are not allowed to perform the delete action in demo mode'));
        }

        try {
            $this->designation->update($request, $id);

            return \redirect()->route('designation.index')->with('success', _trans('alert.Designation updated successfully'));
        } catch (\Throwable $th) {
            Log::error($th->getMessage());

            return \redirect()->back()->with('error', _trans('alert.Something went wrong!'));
        }
    }

    public function deleteData(Request $request)
    {
        if (\demoCheck()) {
            return \redirect()->back()->with('error', _trans('alert.You cannot do it for demo'));
        }

        return $this->designation->destroyAll($request);
    }

    public function delete(Designation $designation)
    {
        if (\config('app.style') === 'demo' || \env('APP_STYLE') === 'demo') {
            return \redirect()->back()->with('error', _trans('alert.You are not allowed to perform the delete action in demo mode'));
        }

        return $this->designation->destroy($designation);
    }

    // status change
    public function statusUpdate(Request $request)
    {
        if (\demoCheck()) {
            return \redirect()->back()->with('error', _trans('alert.You cannot do it for demo'));
        }

        return $this->designation->statusUpdate($request);
    }
}
