<?php

namespace App\Http\Controllers\Backend\Leave;

use App\Helpers\CoreApp\Traits\ApiReturnFormatTrait;
use App\Helpers\CoreApp\Traits\DateHandler;
use App\Helpers\CoreApp\Traits\FirebaseNotification;
use App\Http\Controllers\Controller;
use App\Http\Requests\Leave\LeaveApplyRequest;
use App\Models\Hrm\Leave\LeaveRequest;
use App\Repositories\Admin\RoleRepository;
use App\Repositories\Hrm\Leave\LeaveRequestRepository;
use Illuminate\Http\Request;

class LeaveRequestController extends Controller
{
    use ApiReturnFormatTrait, DateHandler, FirebaseNotification;

    public function __construct(
        protected LeaveRequestRepository $repository,
        protected RoleRepository $role
    ) {}

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        try {
            $data['title'] = _trans('common.Leave Requests');
            $data['collection'] = $this->repository->getPaginateData($request);
            $data['status'] = $this->repository->getStatus();

            return \view('backend.leave.request.index')->with($data);
        } catch (\Exception $e) {
            return catchHandler($e);
        }
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     *
     * This function returns the create leave request form.
     * It retrieves the leave summary and assigns users from the repository.
     * The data is passed to the view and rendered.
     * In case of an exception, it logs the error and redirects back with an error message.
     */
    public function create()
    {
        try {
            $data['title'] = _trans('common.Leave Request');
            $data['leaveSummary'] = $this->repository->getLeaveSummary();
            $data['assign'] = $this->repository->getUsers(true);

            if (count($data['leaveSummary']) == 0) {
                return redirect()->route('leave.request.index')->with('error', _trans('alert.You are not assigned to any leave'));
            }

            return view('backend.leave.request.create')->with($data);
        } catch (\Exception $e) {
            return catchHandler($e);
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \App\Http\Requests\LeaveApplyRequest  $request
     * @return \Illuminate\Http\RedirectResponse
     *
     * This function attempts to create a leave request using the validated input.
     * Upon successful creation, it redirects to the leave log route with a success message.
     * In case of an exception, it logs the error and redirects back with an error message.
     */
    public function store(LeaveApplyRequest $request)
    {
        try {
            $this->repository->store($request);

            return redirect()->route('leave.request.leaveLog')->with('success', 'Leave request created successfully');
        } catch (\Exception $e) {
            return catchHandler($e);
        }
    }

    /**
     * This function returns the leave request action page.
     *
     *
     * @return \Illuminate\Http\Response
     */
    public function action(LeaveRequest $leaveRequest)
    {
        try {
            $data['title'] = _trans('common.Leave Request');
            $data['leaveSummary'] = $this->repository->getLeaveSummary($leaveRequest->user_id);
            $data['substitute'] = $this->repository->getUsers(false, true, $leaveRequest->user_id);
            $data['referTo'] = $this->repository->getUsers(true);
            $data['model'] = $leaveRequest;
            $data['status'] = $this->repository->getStatus();
            $data['otherApplications'] = $this->repository->getOtherApplications($leaveRequest);

            return \view('backend.leave.request.action')->with($data);
        } catch (\Exception $e) {
            return catchHandler($e);
        }
    }

    /**
     * Update the specified leave request in storage.
     *
     * @return \Illuminate\Http\RedirectResponse
     *
     * This function attempts to update a leave request using the provided request data.
     * Upon successful update, it redirects to the leave request index route with a success message.
     * In case of an exception, it logs the error and redirects back with an error message.
     */
    public function update(LeaveApplyRequest $request, LeaveRequest $leaveRequest)
    {
        if (config('app.style') === 'demo' || env('APP_STYLE') === 'demo') {
            return redirect()->back()->with('error', _trans('alert.You are not allowed to perform the delete action in demo mode'));
        }

        try {
            $this->repository->update($request, $leaveRequest);

            return redirect()->route('leave.request.index')->with('success', _trans('alert.Action Applied Successfully'));
        } catch (\Exception $e) {
            return catchHandler($e);
        }
    }

    /**
     * This function returns the leave request view page.
     *
     *
     * @return \Illuminate\Http\Response
     */
    public function view(LeaveRequest $leaveRequest)
    {
        try {
            $data['title'] = _trans('common.Leave Request');
            $data['leaveSummary'] = $this->repository->getLeaveSummary($leaveRequest->user_id);
            $data['model'] = $leaveRequest;
            $data['status'] = $this->repository->getStatus();

            return view('backend.leave.request.view')->with($data);
        } catch (\Exception $e) {
            return catchHandler($e);
        }
    }

    /**
     * This function returns the leave request log list.
     *
     *
     * @return \Illuminate\Http\Response
     */
    public function leaveLog(Request $request)
    {
        try {
            $data['title'] = _trans('common.Leave Requests');
            $data['collection'] = $this->repository->getLogData($request);
            $data['status'] = $this->repository->getStatus();
            $data['leaveSummary'] = $this->repository->getLeaveSummary();

            return view('backend.leave.request.request-log')->with($data);
        } catch (\Exception $e) {
            return catchHandler($e);
        }
    }

    /**
     * Remove the specified leave request from storage.
     *
     * @return \Illuminate\Http\RedirectResponse
     *
     * This function attempts to delete a leave request using the provided leave request model.
     * Upon successful deletion, it redirects to the leave request index route with a success message.
     * In case of an exception, it logs the error and redirects back with an error message.
     */
    public function destroy(LeaveRequest $leaveRequest)
    {
        if (config('app.style') === 'demo' || env('APP_STYLE') === 'demo') {
            return redirect()->back()->with('error', _trans('alert.You are not allowed to perform the delete action in demo mode'));
        }

        try {
            $this->repository->delete($leaveRequest);

            return redirect()->back()->with('success', _trans('alert.Deleted Successfully'));
        } catch (\Exception $e) {
            return catchHandler($e);
        }
    }
}
