<?php

namespace UniversalChatbot\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class QueryExecutor
{
    /**
     * Execute SQL query against the database
     *
     * @param string $query SQL query to execute
     * @return array Query results
     */
    public function executeQuery(string $query): array
    {
        try {
            // Validate query for security
            $validatedQuery = $this->validateQuery($query);

            if (!$validatedQuery) {
                return [
                    'success' => false,
                    'message' => 'Query failed validation',
                    'data' => [],
                ];
            }

            // Execute the query
            $results = DB::select($validatedQuery);

            // Convert to array for consistency
            $data = json_decode(json_encode($results), true);

            return [
                'success' => true,
                'query' => $validatedQuery,
                'data' => $data,
            ];
        } catch (\Exception $e) {
            Log::error('Error executing query: ' . $e->getMessage());

            return [
                'success' => false,
                'message' => 'Error executing query: ' . $e->getMessage(),
                'data' => [],
            ];
        }
    }

    /**
     * Validate and sanitize SQL query for security
     *
     * @param string $query SQL query to validate
     * @return string|false Validated query or false if invalid
     */
    protected function validateQuery(string $query)
    {
        // Basic validation
        if (empty($query)) {
            return false;
        }

        // Check for dangerous operations
        $dangerousOperations = [
            'DROP', 'TRUNCATE', 'DELETE', 'UPDATE', 'INSERT', 'ALTER', 'CREATE',
            'GRANT', 'REVOKE', 'COMMIT', 'ROLLBACK', 'SAVEPOINT', 'EXECUTE',
        ];

        foreach ($dangerousOperations as $operation) {
            if (preg_match('/\b' . $operation . '\b/i', $query)) {
                Log::warning('Dangerous SQL operation detected: ' . $operation);
                return false;
            }
        }

        // Ensure it's a SELECT query
        if (!preg_match('/^\s*SELECT\b/i', $query)) {
            Log::warning('Non-SELECT query detected');
            return false;
        }

        return $query;
    }

    /**
     * Check if query is safe to execute
     *
     * @param string $query SQL query to check
     * @return bool True if safe, false otherwise
     */
    public function isQuerySafe(string $query): bool
    {
        return $this->validateQuery($query) !== false;
    }

    /**
     * Get query execution statistics
     *
     * @param string $query SQL query
     * @return array Statistics about the query
     */
    public function getQueryStats(string $query): array
    {
        $stats = [
            'is_valid' => $this->isQuerySafe($query),
            'query_length' => strlen($query),
            'estimated_complexity' => $this->estimateQueryComplexity($query),
        ];

        return $stats;
    }

    /**
     * Estimate query complexity based on various factors
     *
     * @param string $query SQL query
     * @return string Complexity level (low, medium, high)
     */
    protected function estimateQueryComplexity(string $query): string
    {
        $complexity = 0;

        // Count JOINs
        $joinCount = preg_match_all('/\bJOIN\b/i', $query);
        $complexity += $joinCount * 2;

        // Count subqueries
        $subqueryCount = preg_match_all('/\(\s*SELECT\b/i', $query);
        $complexity += $subqueryCount * 3;

        // Count aggregate functions
        $aggregateCount = preg_match_all('/\b(COUNT|SUM|AVG|MIN|MAX|GROUP_CONCAT)\s*\(/i', $query);
        $complexity += $aggregateCount;

        // Count WHERE conditions
        $whereCount = preg_match_all('/\bWHERE\b/i', $query);
        $complexity += $whereCount;

        // Count ORDER BY
        $orderCount = preg_match_all('/\bORDER\s+BY\b/i', $query);
        $complexity += $orderCount;

        if ($complexity <= 2) {
            return 'low';
        } elseif ($complexity <= 6) {
            return 'medium';
        } else {
            return 'high';
        }
    }

    /**
     * Execute query with timeout and memory limits
     *
     * @param string $query SQL query to execute
     * @param int $timeoutSeconds Maximum execution time
     * @return array Query results
     */
    public function executeQueryWithLimits(string $query, int $timeoutSeconds = 30): array
    {
        try {
            // Set query timeout if supported
            if (method_exists(DB::connection(), 'statement')) {
                DB::statement("SET SESSION max_execution_time = {$timeoutSeconds}000");
            }

            return $this->executeQuery($query);
        } catch (\Exception $e) {
            Log::error('Query execution with limits failed: ' . $e->getMessage());

            return [
                'success' => false,
                'message' => 'Query execution failed: ' . $e->getMessage(),
                'data' => [],
            ];
        }
    }
}
