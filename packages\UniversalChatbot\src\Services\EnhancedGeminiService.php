<?php

namespace UniversalChatbot\Services;

use UniversalChatbot\Models\ChatbotQuery;

class EnhancedGeminiService
{
    protected GeminiApiClient $geminiClient;
    protected DatabaseSchemaService $schemaService;
    protected QueryAnalyzer $queryAnalyzer;
    protected QueryExecutor $queryExecutor;
    protected ResponseFormatter $responseFormatter;
    protected PerformanceTracker $performanceTracker;
    protected FollowUpDetector $followUpDetector;

    public function __construct(
        GeminiApiClient $geminiClient,
        DatabaseSchemaService $schemaService,
        QueryAnalyzer $queryAnalyzer,
        QueryExecutor $queryExecutor,
        ResponseFormatter $responseFormatter,
        PerformanceTracker $performanceTracker,
        FollowUpDetector $followUpDetector
    ) {
        $this->geminiClient       = $geminiClient;
        $this->schemaService      = $schemaService;
        $this->queryAnalyzer      = $queryAnalyzer;
        $this->queryExecutor      = $queryExecutor;
        $this->responseFormatter  = $responseFormatter;
        $this->performanceTracker = $performanceTracker;
        $this->followUpDetector   = $followUpDetector;
    }

    /**
     * Generate a response using Gemini API with database query capability.
     *
     * @param string $prompt User's question
     * @param array $context Optional context information
     * @param array $options Optional configuration
     * @return string The final response
     */
    public function generateResponse(string $prompt, array $context = [], array $options = []): string
    {
        // Initialize performance tracking
        $this->performanceTracker->startMetric('total_execution');

        $temperature         = $options['temperature'] ?? config('universal_chatbot.chat.temperature', 0.3);
        $maxTokens           = $options['max_tokens'] ?? config('universal_chatbot.chat.max_tokens', 1024);
        $conversationHistory = $options['conversation_history'] ?? [];
        $userId              = $options['user_id'] ?? null;
        $conversationId      = $options['conversation_id'] ?? null;

        // Create a new query record
        $queryRecord = new ChatbotQuery([
            'user_question'   => $prompt,
            'user_id'         => $userId,
            'conversation_id' => $conversationId,
            'metadata'        => [
                'context_count'              => count($context),
                'conversation_history_count' => count($conversationHistory),
            ],
        ]);

        // Check for follow-up questions
        $this->performanceTracker->startMetric('follow_up_detection');
        $followUpContext = $this->followUpDetector->checkForFollowUpQuestion($prompt, $conversationHistory);
        $this->performanceTracker->endMetric('follow_up_detection');

        if ($followUpContext) {
            $queryRecord->metadata = array_merge($queryRecord->metadata, [
                'is_follow_up'      => true,
                'previous_question' => $followUpContext['previous_query'],
            ]);
        }

        // Step 1: Ask AI to analyze the question and determine if database query is needed
        $this->performanceTracker->startMetric('analysis');
        $analysisResponse                  = $this->queryAnalyzer->analyzeQuestion($prompt, $conversationHistory, $followUpContext);
        $analysisResponse['analysis_time'] = $this->performanceTracker->endMetric('analysis');

        // Step 2: Process the analysis response
        $finalResponse = '';
        if ($analysisResponse['needs_database_query']) {
            // Step 3: Execute the database query
            $this->performanceTracker->startMetric('query_execution');
            $queryResults                   = $this->queryExecutor->executeQuery($analysisResponse['sql_query']);
            $queryResults['execution_time'] = $this->performanceTracker->endMetric('query_execution');

            // Update query record
            $queryRecord->sql_query           = $analysisResponse['sql_query'];
            $queryRecord->query_results       = $queryResults['data'];
            $queryRecord->used_database_query = true;

            // Step 4: Format the results using AI
            $this->performanceTracker->startMetric('formatting');
            $finalResponse = $this->responseFormatter->formatQueryResults(
                $prompt,
                $queryResults,
                $analysisResponse['sql_query'],
                $conversationHistory
            );
            $this->performanceTracker->endMetric('formatting');
        } else {
            // If no database query is needed, use context-based approach
            $this->performanceTracker->startMetric('context_response');
            $finalResponse = $this->responseFormatter->generateContextBasedResponse($prompt, $context, $conversationHistory);
            $this->performanceTracker->endMetric('context_response');
        }

        // Save the final response
        $queryRecord->final_response = $finalResponse;

        // Track performance metrics
        $this->performanceTracker->endMetric('total_execution');
        $this->performanceTracker->trackQueryMetrics($queryRecord, $analysisResponse, $queryResults ?? null);

        return $finalResponse;
    }

    /**
     * Get database schema information (delegated to DatabaseSchemaService)
     *
     * @return string Database schema description
     */
    public function getDatabaseSchemaInfo(): string
    {
        return $this->schemaService->getDatabaseSchemaInfo();
    }

    /**
     * Get cross-database compatible SQL function (delegated to DatabaseSchemaService)
     *
     * @param string $function Function name
     * @param array $arguments Function arguments
     * @return string SQL function call
     */
    public function dbFunction(string $function, array $arguments = []): string
    {
        return $this->schemaService->dbFunction($function, $arguments);
    }

}