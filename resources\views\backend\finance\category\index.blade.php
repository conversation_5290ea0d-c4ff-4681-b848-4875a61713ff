@extends('backend.layouts.app')
@section('title', @$data['title'])
@section('content')
<input type="text" hidden id="is_income" value="{{ @$data['is_income'] }}">
<div class="ot-card">
    <!-- toolbar table start -->
    <div
        class="table-toolbar d-flex flex-wrap gap-2 flex-xl-row justify-content-center justify-content-xxl-between align-content-center pb-3">
        <div class="align-self-center">
            <div class="d-flex flex-wrap gap-2  flex-lg-row justify-content-center align-content-center">
                <!-- show per page -->
                <div class="align-self-center">
                    <label>
                        <span class="mr-8">{{ _trans('common.Show') }}</span>
                        <select class="form-select d-inline-block" id="entries" onchange="depositCatDatatable()">
                            @include('backend.partials.tableLimit')
                        </select>
                        <span class="ml-8">{{ _trans('common.Entries') }}</span>
                    </label>
                </div>

                <div class="align-self-center d-flex flex-wrap gap-2">
                    <!-- add btn -->
                    <div class="align-self-center">
                        @if (hasPermission('deposit_category_create'))
                        <div class="align-self-center">
                            <a href="javascript:;" role="button" class="btn-add"
                                onclick="mainModalOpen(`{{ @$data['create'] }}`)"
                                data-bs-title="{{ _trans('common.Add') }}">
                                <span><i class="fa-solid fa-plus"></i> </span>
                                <span class="d-none d-xl-inline">{{ _trans('common.Create') }}</span>
                            </a>
                        </div>
                        @endif
                    </div>
                </div>

                <!-- search -->
                <div class="align-self-center">
                    <div class="search-box d-flex">
                        <input class="form-control" placeholder="{{ _trans('common.Search') }}" name="search"
                            onkeyup="depositCatDatatable()" autocomplete="off">
                        <span class="icon"><i class="fa-solid fa-magnifying-glass"></i></span>
                    </div>
                </div>

                <!-- dropdown action -->
                <div class="align-self-center">
                    <div class="dropdown dropdown-action">
                        <button type="button" class="btn-dropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fa-solid fa-ellipsis"></i>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end">
                            @if (hasPermission('deposit_category_update'))
                            <li>
                                <a class="dropdown-item" href="#"
                                    onclick="tableAction('active', `{{ $data['status_url'] }}`)"><span
                                        class="icon mr-10"><i class="fa-solid fa-eye"></i></span>
                                    {{ _trans('common.Activate') }} <span class="count">(0)</span>
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="#" aria-current="true"
                                    onclick="tableAction('inactive',`{{ $data['status_url'] }}`)">
                                    <span class="icon mr-8"><i class="fa-solid fa-eye-slash"></i></span>
                                    {{ _trans('common.Inactive') }} <span class="count">(0)</span>
                                </a>
                            </li>
                            @endif

                            @if (hasPermission('deposit_category_delete'))
                            <li>
                                <a class="dropdown-item" href="#"
                                    onclick="tableAction('delete', `{{ $data['delete_url'] }}`)">
                                    <span class="icon mr-16"><i class="fa-solid fa-trash-can"></i></span>
                                    {{ _trans('common.Delete') }} <span class="count">(0)</span>
                                </a>
                            </li>
                            @endif
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <!-- export -->
        @include('backend.partials.buttons')
    </div>
    <!-- toolbar table end -->
    <!--  table start -->
    <div class="table-responsive min-height-300">

        @include('backend.partials.table')

    </div>
    <!--  table end -->
</div>

@endsection
@section('script')
@include('backend.partials.table_js')
@endsection