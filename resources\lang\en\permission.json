{"employee_menu": "employee_menu", "department": "department", "designation": "designation", "role": "role", "employee": "employee", "performance": "performance", "official_document_menu": "official_document_menu", "official_document_type": "official_document_type", "official_document_request": "official_document_request", "write_up_menu": "write_up_menu", "complain": "complain", "verbal_warning": "verbal_warning", "attendance_menu": "attendance_menu", "attendance": "attendance", "tardy_menu": "tardy_menu", "tardy_group": "tardy_group", "tardy_rule": "tardy_rule", "tardy_rule_assign": "tardy_rule_assign", "tardy_record": "tardy_record", "tardy_request": "tardy_request", "attendance_setting_menu": "attendance_setting_menu", "weekend": "weekend", "holiday": "holiday", "shift": "shift", "schedule": "schedule", "duty_calendar": "duty_calendar", "ip_wishlist": "ip_wishlist", "location": "location", "break_menu": "break_menu", "break_type": "break_type", "break": "break", "leave_menu": "leave_menu", "leave_type": "leave_type", "leave_assign": "leave_assign", "leave_request": "leave_request", "leave_balance": "leave_balance", "payroll_menu": "payroll_menu", "commission": "commission", "setup": "setup", "advance_type": "advance_type", "advance": "advance", "deduction_generate": "deduction_generate", "salary_generate": "salary_generate", "communication_menu": "communication_menu", "conference": "conference", "meeting": "meeting", "appointment": "appointment", "notice": "notice", "bulletin": "bulletin", "office_work_menu": "office_work_menu", "client": "client", "project": "project", "project_file": "project_file", "project_discussion": "project_discussion", "project_note": "project_note", "task": "task", "task_discussion": "task_discussion", "task_file": "task_file", "task_note": "task_note", "visit": "visit", "contact": "contact", "support_ticket": "support_ticket", "travel_menu": "travel_menu", "travel_plan": "travel_plan", "travel_meeting": "travel_meeting", "travel_expense": "travel_expense", "travel_workflow": "travel_workflow", "awards_menu": "awards_menu", "award_type": "award_type", "award": "award", "report_menu": "report_menu", "report": "report", "account_menu": "account_menu", "account": "account", "deposit": "deposit", "expense": "expense", "transaction": "transaction", "account_setting_menu": "account_setting_menu", "deposit_category": "deposit_category", "expense_category": "expense_category", "payment_method": "payment_method", "setting_menu": "setting_menu", "general_setting": "general_setting", "currency": "currency", "language": "language", "branch": "branch", "configuration": "configuration", "activation": "activation", "branding": "branding", "profile_menus": "profile_menus", "time_complience_menu": "time_complience_menu", "time_complience_group": "time_complience_group", "time_complience_rule": "time_complience_rule", "time_complience_rule_assign": "time_complience_rule_assign", "time_complience_record": "time_complience_record", "time_complience_request": "time_complience_request", "content": "content"}