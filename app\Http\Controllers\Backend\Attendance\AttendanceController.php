<?php

namespace App\Http\Controllers\Backend\Attendance;

use App\Helpers\CoreApp\Traits\ApiReturnFormatTrait;
use App\Http\Controllers\Controller;
use App\Models\coreApp\Relationship\RelationshipTrait;
use App\Models\Hrm\Attendance\Attendance;
use App\Repositories\Hrm\Attendance\AttendanceRepository;
use App\Repositories\Hrm\Department\DepartmentRepository;
use Brian2694\Toastr\Facades\Toastr;
use Illuminate\Http\Request;

class AttendanceController extends Controller
{
    use ApiReturnFormatTrait, RelationshipTrait;

    protected $attendanceRepo;

    protected $departmentRepository;

    public function __construct(
        AttendanceRepository $attendanceRepo,
        DepartmentRepository $departmentRepository,
    ) {
        $this->attendanceRepo = $attendanceRepo;
        $this->departmentRepository = $departmentRepository;
    }

    public function index(Request $request)
    {
        if ($request->ajax()) {
            return $this->attendanceRepo->table($request);
        }

        $data['class'] = 'attendance_table';
        $data['fields'] = $this->attendanceRepo->fields();
        $data['table'] = \route('attendance.index');
        $data['url_id'] = 'attendance_table_url';
        $data['checkbox'] = false;
        $data['title'] = _trans('attendance.Attendance list');
        $data['departments'] = $this->departmentRepository->getActiveAll();

        return \view('backend.attendance.attendance.index', \compact('data'));
    }

    public function edit(Attendance $attendance)
    {
        $data['title'] = _trans('attendance.Edit Attendance');
        $data['show'] = $attendance->load('lateInReason');
        $data['users'] = $this->attendanceRepo->checkInUsers();

        return \view('backend.attendance.attendance.check_out', \compact('data'));
    }

    public function update(Request $request, Attendance $attendance_id)
    {
        try {
            $request['remote_mode_out'] = 0;

            $checkout = $this->attendanceRepo->update($request, $attendance_id->id);

            if ($checkout->original['result']) {
                Toastr::success(_trans('attendance.Attendance has been updated'), 'Success');

                return \redirect()->route('attendance.index');
            } else {
                Toastr::error(_trans('attendance.Attendance did not checkout'), 'Error');

                return \redirect()->back();
            }
        } catch (\Throwable $th) {
            \info($th->getMessage());
            Toastr::error(_trans('response.Something went wrong'), 'Error');

            return \redirect()->back();
        }
    }

    public function showImageInModal($attendance_id, $type)
    {
        try {
            $data['title'] = $type == 'check_in' ? _trans('common.Check in Image') : _trans('common.Check out Image');
            $attendance = Attendance::find($attendance_id);
            if ($type == 'check_in') {
                $data['image'] = \uploaded_asset(@$attendance->check_in_image);
            } else {
                $data['image'] = \uploaded_asset(@$attendance->check_out_image);
            }

            return \view('backend.attendance.attendance.show-image-modal', \compact('data'));
        } catch (\Throwable $th) {
            return \response()->json('fail');
        }
    }

    public function absents(Request $request)
    {
        if ($request->ajax()) {
            return $this->attendanceRepo->absentTable($request);
        }

        $data['class'] = 'absent_table';
        $data['fields'] = $this->attendanceRepo->absentFields();
        $data['table'] = \route('attendance.absents');
        $data['url_id'] = 'absent_table_url';
        $data['title'] = _trans('attendance.Absent List');
        $data['departments'] = $this->departmentRepository->getAll();

        return \view('backend.attendance.attendance.absent', \compact('data'));
    }
}
