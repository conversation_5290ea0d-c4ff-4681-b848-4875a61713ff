<?php

namespace App\Http\Controllers\Api\V11;

use App\Helpers\CoreApp\Traits\ApiReturnFormatTrait;
use App\Http\Controllers\Controller;
use App\Http\Requests\RemarkApiRequest;
use App\Models\Hrm\Attendance\Attendance;
use Illuminate\Support\Facades\Schema;
use Modules\Break\Entities\UserBreak;

class RemarkApiController extends Controller
{
    use ApiReturnFormatTrait;

    public function __invoke(RemarkApiRequest $request)
    {
        try {
            if ($request->type == 'break') {
                if (Schema::hasTable('user_breaks')) {
                    $break = UserBreak::find($request->id);

                    if (! $break) {
                        return $this->responseWithError(_trans('Break not found!'), [], 400);
                    }

                    $break->update([
                        'remark' => $request->remark,
                    ]);

                    return $this->responseWithSuccess(_trans('Remark has been store successfully!'), [], 200);
                } else {
                    return $this->responseWithError(_trans('User Breaks table not found!'), [], 400);
                }
            } elseif ($request->type == 'attendance') {
                if (Schema::hasTable('attendances')) {
                    $attendance = Attendance::find($request->id);

                    if (! $attendance) {
                        return $this->responseWithError(_trans('Attendance not found!'), [], 400);
                    }

                    $attendance->update([
                        'late_reason' => $request->remark,
                    ]);

                    return $this->responseWithSuccess(_trans('Remark has been store successfully!'), [], 200);
                } else {
                    return $this->responseWithError(_trans('Attendances table not found!'), [], 400);
                }
            } else {
                return $this->responseWithError(_trans('Not found!'), [], 400);
            }
        } catch (\Throwable $th) {
            return $this->responseWithError(_trans('Something went wrong.'), [$th->getMessage()], 400);
        }
    }
}
