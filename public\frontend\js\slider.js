// feedback slick carousel
$('.slick-carousel-feedback').slick({
    infinite: true,
    autoplay: false,
    slidesToShow: 3, // Shows a three slides at a time
    slidesToScroll: 1, // When you click an arrow, it scrolls 1 slide at a time
    arrows: true, // Adds arrows to sides of slider
    dots: false,
    // prevArrow: '<button type="button" class="slick-prev"><i class="icofont-thin-left"></i></button>',
    // nextArrow: '<button type="button" class="slick-next"><i class="icofont-thin-right"></i></button>',
    responsive: [
        {
            breakpoint: 1400,
            settings: {
              slidesToShow: 3,
              slidesToScroll: 1,
              // centerMode: true,
            }
          },

          {
            breakpoint: 1150,
            settings: {
              slidesToShow: 2,
              slidesToScroll: 1,
            }
          },

        {
      breakpoint: 991,
      settings: {
        slidesToShow: 2,
        slidesToScroll: 1,
        arrows: false,
        // centerMode: true,
      }
    },  {
      breakpoint: 767,
      settings: {
        slidesToShow: 1,
        slidesToScroll: 1,
        arrows: false,
      }
    }] // Adds the dots on the bottom
  });


  // brand slick carousel
  $('.slick-carousel-brand').slick({
    infinite: true,
    autoplay: true,
    slidesToShow: 5, // Shows a three slides at a time
    slidesToScroll: 1, // When you click an arrow, it scrolls 1 slide at a time
    arrows: false, // Adds arrows to sides of slider
    dots: false,
    responsive: [
        {
            breakpoint: 1200,
            settings: {
              slidesToShow: 5,
              slidesToScroll: 1,
              // centerMode: true,

            }

          },
        {
      breakpoint: 991,
      settings: {
        slidesToShow: 3,
        slidesToScroll: 1,
        // centerMode: true,

      }

    },
    {
            breakpoint: 575,
            settings: {
              slidesToShow: 2,
              slidesToScroll: 1,
              dots: false,
              infinite: true,
              autoplay: false,
              autoplaySpeed: 2000,
            }
    } , {
      breakpoint: 420,
      settings: {
        slidesToShow: 1,
        slidesToScroll: 1,
        dots: false,
        infinite: true,
        autoplay: false,
        autoplaySpeed: 2000,
      }
    }]

  });

// advertise slider active 575px

$('.slick-carousel-ad').slick({

    responsive: [
        {
            breakpoint: 767,
            settings: {
              slidesToShow: 2,
              slidesToScroll: 1,
              dots: false,
              infinite: true,
              autoplay: true,
              autoplaySpeed: 2000,

            }

          },

    {
            breakpoint: 575,
            settings: {
              slidesToShow: 1,
              slidesToScroll: 1,
              dots: false,
              infinite: true,
              autoplay: true,
              autoplaySpeed: 2000,
            }
    }]

  });


  $('.slick-client').slick({
    infinite: true,
    autoplay: false,
    slidesToShow: 3,
    slidesToScroll: 1,
    arrows: true,
    dots: false,
    responsive: [
      {
        breakpoint: 1024,
        settings: {
          slidesToShow: 2,
          slidesToScroll: 2,
          infinite: true,
          arrows: true
        }
      },
      {
        breakpoint: 600,
        settings: {
          slidesToShow: 2,
          slidesToScroll: 2,
          infinite: true,
          arrows: true
        }
      },
      {
        breakpoint: 480,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
          infinite: true,
        }
      }
    ]
});



$('.trusted-company-slider').slick({
  infinite: true,
  autoplay: true,
  slidesToShow: 4,
  slidesToScroll: 1,
  arrows: false,
  dots: false,
  responsive: [
    {
      breakpoint: 1024,
      settings: {
        slidesToShow: 4,
        slidesToScroll: 4,
      }
    },
    {
      breakpoint: 600,
      settings: {
        slidesToShow: 3,
        slidesToScroll: 3,
      }
    },
    {
      breakpoint: 480,
      settings: {
        slidesToShow: 2,
        slidesToScroll: 2,
      }
    }
  ]
});


$('.slick-backend-design').slick({
    infinite: true,
    autoplay: false,
    slidesToShow: 1, // Shows a three slides at a time
    slidesToScroll: 1, // When you click an arrow, it scrolls 1 slide at a time
    arrows: true, // Adds arrows to sides of slider
    dots: false,
    // prevArrow: '<button type="button" class="slick-prev"><i class="icofont-thin-left"></i></button>',
    // nextArrow: '<button type="button" class="slick-next"><i class="icofont-thin-right"></i></button>',
    responsive: [
        {
            breakpoint: 1400,
            settings: {
              slidesToShow: 1,
              slidesToScroll: 1,
              // centerMode: true,
            }
          },

          {
            breakpoint: 1150,
            settings: {
              slidesToShow: 1,
              slidesToScroll: 1,
            }
          },

        {
      breakpoint: 991,
      settings: {
        slidesToShow: 2,
        slidesToScroll: 1,
        arrows: false,
        // centerMode: true,
      }
    },  {
      breakpoint: 767,
      settings: {
        slidesToShow: 1,
        slidesToScroll: 1,
        arrows: false,
      }
    }] // Adds the dots on the bottom
  });
