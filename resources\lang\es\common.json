{"Pro": "Pro", "Dashboard": "Dashboard", "Select": "Select", "Save": "Save", "Yes": "Yes", "Cancel": "Cancel", "Nothing to show here": "Nothing to show here", "Thank you": "Thank you", "Check In": "Check In", "Expire Notification": "Expire Notification", "Notifications": "Notifications", "No Notification Found": "No Notification Found", "See All Notifications": "See All Notifications", "Expire Notifications": "Expire Notifications", "No Expire Notification Found": "No Expire Notification Found", "See All Expire Notifications": "See All Expire Notifications", "My Profile": "My Profile", "Notification": "Notification", "Settings": "Settings", "Logout": "Logout", "Add-ons": "Add-ons", "Employee Documents": "Employee Documents", "Branches": "Branches", "HR": "HR", "Designations": "Designations", "Departments": "Departments", "Roles": "Roles", "Employees": "Employees", "Device Management": "Device Management", "Leaves": "Leaves", "Type": "Type", "Leave Balance": "Leave Balance", "Client Lists": "Client Lists", "List": "List", "Visit": "Visit", "Manage visit": "Manage visit", "Support": "Support", "Tickets": "Tickets", "Announcement": "Announcement", "Notice": "Notice", "Send E-mail": "Send E-mail", "Send Notification": "Send Notification", "Contacts": "Contacts", "Report": "Report", "Live Tracking": "Live Tracking", "Location History": "Location History", "Location Timeline": "Location Timeline", "Break report": "Break report", "Payment Report": "Payment Report", "Visit Reports": "Visit Reports", "Configurations": "Configurations", "IP Whiltelist": "IP Whiltelist", "Locations": "Locations", "Activation": "Activation", "General Settings": "General Settings", "App Setting": "App Setting", "Currency": "<PERSON><PERSON><PERSON><PERSON>", "ID": "ID", "Title": "Title", "Status": "Status", "Action": "Action", "Show": "Show", "Entries": "Entries", "Add": "Add", "Create": "Create", "Search": "Search", "Activate": "Activate", "Inactive": "Inactive", "Delete": "Delete", "Export": "Export", "Copy": "Copy", "Json File": "Json File", "Excel File": "Excel File", "Csv File": "Csv File", "Pdf File": "Pdf File", "Choose Employee": "<PERSON><PERSON>loyee", "Bulletin": "Bulletin", "Employee": "Employee", "Bulk Import": "Bulk Import", "Official Documents": "Official Documents", "Tardy": "<PERSON><PERSON>", "Records": "Records", "Appeals": "Appeals", "Rules": "Rules", "Break": "Break", "QR Code": "QR Code", "Types": "Types", "Breaks": "Breaks", "Duty Calendar": "Duty Calendar", "Leave": "Leave", "Leave Type": "Leave Type", "Appointments": "Appointments", "Clients": "Clients", "Travel": "Travel", "Travel Plans": "Travel Plans", "Travel Workflows": "Travel Workflows", "Support Ticket": "Support Ticket", "Reports": "Reports", "Addons": "Addons", "Branding": "Branding", "Edit": "Edit", "Name": "Name", "Web Login": "Web Login", "App Login": "<PERSON>pp <PERSON>gin", "Employee List": "Employee List", "Image": "Image", "Registered Face": "Registered Face", "Email": "Email", "Phone": "Phone", "Designation": "Designation", "Department": "Department", "Role": "Role", "Shift": "Shift", "Trash": "Trash", "Select Designation": "Select Designation", "Select Status": "Select Status", "Employee Import": "Employee Import", "Instructions for Bulk User Upload": "Instructions for Bulk User Upload", "Make sure the file is in CSV format": "Make sure the file is in CSV format", "Required fields:": "Required fields:", "Phone Number": "Phone Number", "Ensure there are no duplicate email or phone number": "Ensure there are no duplicate email or phone number", "Download Sample File": "Download Sample File", "Upload CSV File": "Upload CSV File", "Browse": "Browse", "Submit": "Submit", "Document Types": "Document Types", "Date Range": "Date Range", "SL": "SL", "Actions": "Actions", "Description": "Description", "Date": "Date", "Response Deadline": "Response Deadline", "File": "File", "Break Duration": "Break Duration", "Face": "Face", "Checkin Location": "Checkin Location", "Check Out": "Check Out", "Checkout Location": "Checkout Location", "Hours": "Hours", "Check In Image": "Check In Image", "Check Out Image": "Check Out Image", "Select department": "Select department", "Absent": "Absent", "Tardy Records": "Tardy Records", "Tardy Rule": "Tardy Rule", "Time": "Time", "Appeal": "Appeal", "Appeal Approved": "Appeal Approved", "Appeal Rejected": "Appeal Rejected", "Tardy Appeals": "Tardy Appeals", "Reason": "Reason", "Attachment": "Attachment", "Approval": "Approval", "Reject": "Reject", "Days": "Days", "Available Days": "Available Days", "Substitute": "Substitute", "Manager Approved": "Manager Approved", "HR Approved": "HR Approved", "Leave Request": "Leave Request", "Select Department": "Select Department", "Select Employee": "Select Employee", "Select Type": "Select Type", "Approve": "Approve", "Pending": "Pending", "Leave Balance List": "Leave Balance List", "Contact": "Contact", "Employee Details": "Employee Details", "Leave Summary": "Leave Summary", "Available Leave": "Available Leave", "Acting as HR": "Acting as H<PERSON>", "Amount": "Amount", "Payroll": "Payroll", "Salary": "Salary", "Set Contract": "Set Contract", "Profile": "Profile", "Notices": "Notices", "Phonebook": "Phonebook", "E-mail": "E-mail", "Old Password": "Old Password", "Enter Old Password": "Enter Old Password", "Update": "Update", "Welcome to": "Welcome to", "Company Dashboard": "Company Dashboard", "My Dashboard": "My Dashboard", "Groups": "Groups", "Assign": "Assign", "Revenue": "Revenue", "With": "With", "Location": "Location", "Date Time": "Date Time", "Present": "Present", "Unbanned": "Unbanned", "Banned": "Banned", "Register Face": "Register Face", "Permission": "Permission", "Password Reset Mail": "Password Reset Mail", "Live Location Tracking": "Live Location Tracking", "Start Time": "Start Time", "End Time": "End Time", "Members": "Members", "Short Description": "Short Description", "Version": "Version", "Release Date": "Release Date", "Employee ID": "Employee ID", "Create +": "Create +", "Create Menu": "Create Menu", "Notice Create": "Notice Create", "Task Create": "Task Create", "Award Create": "Award Create", "Leave Create": "Leave Create", "Account Create": "Account Create", "Notes": "Notes", "Addons List": "Addons List", "Live Chat": "Live Chat", "Evaluation": "Evaluation", "Time Compliance": "Time Compliance", "View Details": "View Details", "Activity Feed": "Activity Feed", "Loading more": "Loading more", "Schedule Start": "Schedule Start", "Checkin": "Checkin", "Late Reason": "Late Reason", "Schedule End": "Schedule End", "Checkout": "Checkout", "Stay": "Stay", "Native": "Native", "Code": "Code", "RTL": "RTL", "Languages": "Languages", "Active": "Active", "Add Employee": "Add Employee", "Personal Info": "Personal Info", "Official Setup": "Official Setup", "Weekend & Holiday": "Weekend & Holiday", "Employee Name": "Employee Name", "Email Address": "Email Address", "Profile Picture": "Profile Picture", "Gender": "Gender", "Choose One": "Choose One", "Male": "Male", "Female": "Female", "Unisex": "Unisex", "Country": "Country", "Address": "Address", "Religion": "Religion", "Islam": "Islam", "Hinduism": "Hinduism", "Christianity": "Christianity", "Buddhism": "Buddhism", "Others": "Others", "Marital Status": "Marital Status", "Married": "Married", "Unmarried": "Unmarried", "Blood Group": "Blood Group", "A+": "A+", "A-": "A-", "B+": "B+", "B-": "B-", "O+": "O+", "O-": "O-", "AB+": "AB+", "AB-": "AB-", "Joining Date": "Joining Date", "Duty Schedule": "Duty Schedule", "Time Zone": "Time Zone", "Attendance Method": "Attendance Method", "Normal Attendance": "Normal Attendance", "Password": "Password", "If no password is provided, set the default password to 12345678 when creating a user": "If no password is provided, set the default password to 12345678 when creating a user", "Is Free IP": "Is Free IP", "No": "No", "IP Addresses": "IP Addresses", "Is Free Location": "Is Free Location", "Allow Location": "Allow Location", "Add Location": "Add Location", "Weekend": "Weekend", "Holiday": "Holiday", "Previous": "Previous", "Next Steps": "Next Steps", "Chat": "Cha<PERSON>", "Employee Dashboard": "Employee Dashboard", "Promotion": "Promotion", "Resignation": "Resignation", "Termination": "Termination", "Daily Report": "Daily Report", "Expense Report": "Expense Report", "Employee Report": "Employee Report", "Payslip Report": "Payslip Report", "Geo Tracker": "Geo Tracker", "Duty schedule assigned": "Duty schedule assigned", "Enter keyword": "Enter keyword", "Select One": "Select One", "Free Location": "Free Location", "Edit Employee": "Edit Employee", "Next": "Next", "Update Now": "Update Now", "Enter Phone": "Enter Phone", "Enter Address": "Enter Address", "Add Document Type": "Add Document Type", "Document Type": "Document Type", "Request Template": "Request Template", "Response Template": "Response Template", "Submit Now": "Submit Now", "Employee Setup": "Employee Setup", "Calendar view": "Calendar view", "Generate": "Generate", "Select Dates": "Select Dates", "Duty schedule": "Duty schedule", "When choose duty schedule its removed holiday": "When choose duty schedule its removed holiday", "Selecting a Duty Schedule will remove the weekend assignment": "Selecting a Duty Schedule will remove the weekend assignment", "If a weekend is assigned, the Duty Schedule will not be available for that day": "If a weekend is assigned, the Duty Schedule will not be available for that day", "You do not have permission to edit!": "You do not have permission to edit!", "In-active": "In-active", "User": "User", "Duty Calendar 2": "Duty Calendar 2", "Personal Document": "Personal Document", "Start Date": "Start Date", "Threshold Minutes": "<PERSON><PERSON><PERSON><PERSON>", "Threshold Days": "Threshold Days", "Deduction Days": "Deduction Days", "Contract Date": "Contract Date", "Contract End": "Contract End", "Basic Salary": "Basic Salary", "Employee name": "Employee name", "Subject": "Subject", "Priority": "Priority", "Add New Leave Type": "Add New Leave Type", "Is Paid": "<PERSON>", "Primary Assign": "Primary Assign", "Primary Assign Days": "Primary Assign Days", "Assign Type": "Assign Type", "Add New Assign Type": "Add New Assign Type", "Not Started": "Not Started", "On Hold": "On Hold", "In Progress": "In Progress", "Completed": "Completed", "Cancelled": "Cancelled", "Select Priority": "Select Priority", "Payment": "Payment", "Payment Status": "Payment Status", "Unpaid": "Unpaid", "Paid": "Paid", "Complete": "Complete", "Credentials": "Credentials", "Employee Credentials": "Employee Credentials", " Company Credentials": " Company Credentials", "Check": "Check", "working Schedule": "working Schedule", "Speak Language": "Speak Language", "Date of Birth": "Date of Birth", "Next Step": "Next Step", "Add New Role": "Add New Role", "Upper Roles": "Upper Roles", "Default Password: 12345678": "Default Password: 12345678", "Document Request Create": "Document Request Create", "Request Date": "Request Date", "Informed To": "Informed To", "file": "file", "Request Application": "Request Application", "Component": "Component", "HRM Setup": "HRM Setup", "My Leave Log": "My Leave Log", "Invoice": "Invoice", "Document request created successfully": "Document request created successfully", "Document Request": "Document Request", "Approved": "Approved", "Rejected": "Rejected", "Request Type": "Request Type", "File Expire Date": "File Expire Date", "Informed": "Informed", "Final Approved": "Final Approved", "No File": "No File", "N/A": "N/A", "View": "View", "Request for": "Request for", "Applicant": "Applicant", "joined on": "joined on", "Duration": "Duration", "years": "years", "months": "months", "Request File": "Request File", "Manage Reply Letter": "Manage Reply Letter", "Authorized Person": "Authorized Person", "Select Authorized Person": "Select Authorized Person", "Official Seal": "Official Seal", "Signature": "Signature", "Document request deleted successfully": "Document request deleted successfully", "Time Complience Rule Assign": "Time Complience Rule Assign", "Tardy Group": "Tardy Group", "Select Group": "Select Group", "Assign Rules": "Assign Rules", "": "", "Select Role": "Select Role", "Note": "Note", "Group": "Group", "Type :": "Type :", "Title :": "Title :", "Maximum Allowance (Minutes) :": "Maximum Allowance (Minutes) :", "Occurance Limit :": "Occurance Limit :", "Deduction Days :": "Deduction Days :", "Clear filters": "Clear filters", "Complience Groups": "Complience Groups", "Add New Complience Groups": "Add New Complience Groups", "Complience Rules": "Complience Rules", "Maximum Allowance (Minutes)": "Maximum Allowance (Minutes)", "Occurance Limit": "Occurance Limit", "Edit Tardy Rule": "<PERSON>", "Instructions": "Instructions", "If an employee is exempted from Complience rule occurrences, salary deductions\n            will be applied according to the specified criteria": "If an employee is exempted from Complience rule occurrences, salary deductions\n            will be applied according to the specified criteria", "Check IN": "Check IN", "Salary Deduction (Days)": "Salary Deduction (Days)", "Employee Complience Records": "Employee Complience Records", "Create Complience Record": "Create Complience Record", "Choose A Employee": "<PERSON><PERSON> A Employee", "Select Tardy Type": "Select Tardy Type", "Tardy For": "<PERSON><PERSON>", "Tardy Minutes": "Tardy Minutes", "Emergency": "Emergency", "Notified": "Notified", "Total Break Time": "Total Break Time", "Edit Tardy Record": "<PERSON> <PERSON>rdy Record"}