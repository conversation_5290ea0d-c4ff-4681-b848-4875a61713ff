<?php

namespace App\Helpers\CoreApp\Traits;

use Carbon\Carbon;
use DateTime;
use DateTimeZone;
use Illuminate\Support\Facades\Auth;
use Jen<PERSON><PERSON>\Agent\Facades\Agent;

trait GeoLocationTrait
{
    public function getLocation()
    {
        $agent = new Agent;
        if (! empty($_SERVER['HTTP_CLIENT_IP'])) {
            $ip = $_SERVER['HTTP_CLIENT_IP'];
        } elseif (! empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
        } else {
            $ip = $_SERVER['REMOTE_ADDR'];
        }
        if ($ip == '127.0.0.1') {
            $ip = '**************';
        }
        $dataArray = \json_decode(\file_get_contents('http://www.geoplugin.net/json.gp?ip='.$ip));

        return $dataArray;
    }

    public function converToTimeZone($time = '', $toTimeZone = '', $fromTimeZone = '')
    {
        $date = new DateTime($time, new DateTimeZone($fromTimeZone));
        $date->setTimezone(new DateTimeZone($toTimeZone));
        $time = $date->createFromFormat('Y-m-d H:i:s', \date('Y-m-d').' '.$time.':00');

        return $time;
    }

    public function getDateTime($time)
    {
        $defaultTimezone = \config('app.timezone', 'Asia/Dhaka');

        $date = \request()->get('date').' '.$time.':00';

        $datetime = Carbon::parse($date, Auth::user()?->time_zone ?? $defaultTimezone);

        return $datetime->format('Y-m-d H:i:s');
    }
}
