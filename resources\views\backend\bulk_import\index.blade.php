@extends('backend.layouts.app')
@section('title', @$data['title'])
@section('content')

<div class="ot-card">
    <h3 class="card-title d-flex align-items-center gap-10 mb-25">
        Bulk Import
    </h3>
    <form method="POST" action="{{ route('bulk_import.upload') }}" enctype="multipart/form-data">
        @csrf

        <div class="row g-y-24">

            {{-- Model Selection --}}
            <div class="col-lg-6">
                <label class="form-label">{{ _trans('common.Select Model') }}</label>
                <select name="model"
                    class="form-select demo-select2-placeholder {{ $errors->has('model') ? 'is-invalid' : '' }} select2"
                    data-placeholder="{{ _trans('common.Choose Model') }}">
                    <option value="" selected></option>
                    @foreach ($models as $model)
                    <option value="{{ $model }}">{{ ucwords(str_replace('_', ' ', $model)) }}
                    </option>
                    @endforeach
                </select>
                @if ($errors->has('model'))
                <span class="error text-danger">{{ $errors->first('model') }}</span>
                @endif
            </div>

            {{-- File Upload --}}
            <div class="col-lg-6">
                <label class="form-label">
                    {{ _trans('common.Upload CSV File') }}
                </label>
                <div class="ot_fileUploader left-side">
                    <input class="form-control" type="text" placeholder="{{ _trans('common.Upload CSV File') }}"
                        readonly id="placeholder">
                    <div class="primary-btn-small-input">
                        <label class="btn-primary-fill file-up-btn" for="fileBrowse">{{ _trans('common.Browse')
                            }}</label>
                        <input type="file" class="d-none form-control" name="csv_file" id="fileBrowse" accept=".csv">
                    </div>
                </div>
                @if ($errors->has('csv_file'))
                <span class="text-danger">{{ $errors->first('csv_file') }}</span>
                @endif
            </div>

            {{-- Buttons --}}
            <div class="col-md-12 text-right mt-3 mb-3 mr-5">
                <div class="form-group d-flex justify-content-end gap-10">
                    <button type="submit" class="btn-primary-fill">Import Now</button>
                </div>
            </div>
        </div>


    </form>
</div>

@endsection

@pushonce('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
            const fileInput = document.getElementById('fileBrowse');
            const placeholder = document.getElementById('placeholder');

            fileInput.addEventListener('change', function() {
                placeholder.value = this.files[0]?.name || '';
            });
        });
</script>
@endpushonce