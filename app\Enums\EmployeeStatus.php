<?php

namespace App\Enums;

enum EmployeeStatus: string
{
    case PERMANENT = 'permanent';
    case PROBATION = 'probition'; // Assuming typo in source, should be 'probation'
    case CONTRACT = 'contract';
    case SUSPENDED = 'suspended';
    case TERMINATED = 'terminated';
    case RETIRED = 'retired';
    case LAYOFF = 'layoff';
    case RESIGNED = 'resigned';

    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    public static function labels(): array
    {
        return [
            self::PERMANENT->value => 'Permanent',
            self::PROBATION->value => 'Probation',
            self::CONTRACT->value => 'Contract',
            self::SUSPENDED->value => 'Suspended',
            self::TERMINATED->value => 'Terminated',
            self::RETIRED->value => 'Retired',
            self::LAYOFF->value => 'Layoff',
            self::RESIGNED->value => 'Resigned',
        ];
    }
}
