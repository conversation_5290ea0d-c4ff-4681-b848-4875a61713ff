{"Name is required": "Name is required", "Email is required": "Email is required", "Email must be a valid email address": "Email must be a valid email address", "The email  is already associated with an existing or previously deleted account": "The email  is already associated with an existing or previously deleted account", "Phone is required": "Phone is required", "Phone must be unique": "Phone must be unique", "Gender is required": "Gender is required", "Address must not exceed 255 characters": "Address must not exceed 255 characters", "Birth date must be a valid date": "Birth date must be a valid date", "Joining date is required": "Joining date is required", "Department is required": "Department is required", "Department must exist": "Department must exist", "Designation is required": "Designation is required", "Designation must exist": "Designation must exist", "Duty schedule is required": "Duty schedule is required", "Duty schedule must exist": "Duty schedule must exist", "Role is required": "Role is required", "Role must exist": "Role must exist", "Attendance method is required": "Attendance method is required", "Invalid attendance method selected": "Invalid attendance method selected", "Password type is required": "Password type is required", "Invalid password type selected": "Invalid password type selected", "Password must be at least 8 characters": "Password must be at least 8 characters", "Weekends are required": "Weekends are required", "Invalid weekend selected": "Invalid weekend selected", "Holidays are required": "Holidays are required", "Invalid holiday selected": "Invalid holiday selected", "Avatar must be an image": "Avatar must be an image", "Avatar must be of type jpg, jpeg, png, webp": "Avatar must be of type jpg, jpeg, png, webp", "Avatar size must not exceed 2048 KB": "Avatar size must not exceed 2048 KB", "Title is required": "Title is required", "Status is required": "Status is required", "The title is required": "The title is required", "The title may not be greater than 255 characters": "The title may not be greater than 255 characters", "The title has already been taken": "The title has already been taken", "The group field is required": "The group field is required", "The group and type must be unique": "The group and type must be unique", "The type field is required": "The type field is required", "The threshold minutes are required": "The threshold minutes are required", "The threshold minutes must be an integer": "The threshold minutes must be an integer", "The threshold days must be an integer": "The threshold days must be an integer", "The deduction days are required": "The deduction days are required", "The deduction days must be a number": "The deduction days must be a number", "The deduction days must be a valid decimal value": "The deduction days must be a valid decimal value", "The description must be a string": "The description must be a string", "The description may not be greater than 255 characters": "The description may not be greater than 255 characters", "The selected status is invalid": "The selected status is invalid", "The date is required": "The date is required", "The date must be a valid date": "The date must be a valid date", "The user is required": "The user is required", "The user ID must be an integer": "The user ID must be an integer", "The selected user does not exist": "The selected user does not exist", "The tardy rule is required": "The tardy rule is required", "The tardy rule ID must be an integer": "The tardy rule ID must be an integer", "The selected tardy rule does not exist": "The selected tardy rule does not exist", "The tardy type is required": "The tardy type is required", "The tardy type must be a string": "The tardy type must be a string", "The tardy type must be either Check In or Check Out": "The tardy type must be either Check In or Check Out", "The tardy minutes are required": "The tardy minutes are required", "The tardy minutes must be an integer": "The tardy minutes must be an integer", "The tardy minutes must be at least 1": "The tardy minutes must be at least 1", "The emergency status must be 0 or 1": "The emergency status must be 0 or 1", "The notified status must be 0 or 1": "The notified status must be 0 or 1", "The reason must be a string": "The reason must be a string", "The reason must not exceed 255 characters": "The reason must not exceed 255 characters", "The tardy reason must be a string": "The tardy reason must be a string", "The tardy reason must not exceed 255 characters": "The tardy reason must not exceed 255 characters"}