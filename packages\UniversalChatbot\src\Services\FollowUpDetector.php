<?php

namespace UniversalChatbot\Services;

use UniversalChatbot\Models\ChatbotQuery;

class FollowUpDetector
{
    /**
     * Follow-up question indicators
     *
     * @var array
     */
    protected array $followUpIndicators = [
        'how many', 'which ones', 'who else', 'what about', 'can you list',
        'show me', 'tell me more', 'what are their', 'and what about',
        'what were', 'how about', 'could you', 'please list', 'also show',
        'additionally', 'furthermore', 'moreover', 'besides that',
        'what else', 'any other', 'more details', 'expand on',
    ];

    /**
     * Context continuation indicators
     *
     * @var array
     */
    protected array $contextContinuationIndicators = [
        'and', 'also', 'too', 'as well', 'in addition', 'plus',
        'furthermore', 'moreover', 'besides', 'additionally',
    ];

    /**
     * Check if this is a follow-up question to a previous query
     *
     * @param string $prompt Current user prompt
     * @param array $conversationHistory Conversation history
     * @return array|null Follow-up context or null if not a follow-up
     */
    public function checkForFollowUpQuestion(string $prompt, array $conversationHistory = []): ?array
    {
        // Skip if no conversation history
        if (empty($conversationHistory)) {
            return null;
        }

        // Get the last query from this conversation
        $lastQuery = $this->getLastDatabaseQuery($conversationHistory);

        if (!$lastQuery) {
            return null;
        }

        // Check if this is likely a follow-up question
        $isLikelyFollowUp = $this->isLikelyFollowUp($prompt);

        // If it looks like a follow-up, provide the previous query context
        if ($isLikelyFollowUp) {
            return [
                'previous_query' => $lastQuery->user_question,
                'previous_sql' => $lastQuery->sql_query,
                'previous_results' => $lastQuery->query_results,
                'is_follow_up' => true,
                'confidence' => $this->calculateFollowUpConfidence($prompt),
            ];
        }

        return null;
    }

    /**
     * Get the last database query from conversation history
     *
     * @param array $conversationHistory Conversation history
     * @return ChatbotQuery|null Last query that used database or null
     */
    protected function getLastDatabaseQuery(array $conversationHistory): ?ChatbotQuery
    {
        // Get conversation ID from history
        $conversationId = $conversationHistory[0]['conversation_id'] ?? null;

        if (!$conversationId) {
            return null;
        }

        // Get the last query from this conversation that used database
        return ChatbotQuery::where('conversation_id', $conversationId)
            ->where('used_database_query', true)
            ->whereNotNull('sql_query')
            ->latest()
            ->first();
    }

    /**
     * Check if the prompt is likely a follow-up question
     *
     * @param string $prompt User prompt
     * @return bool True if likely a follow-up
     */
    protected function isLikelyFollowUp(string $prompt): bool
    {
        $lowercasePrompt = strtolower($prompt);

        // Check for direct follow-up indicators
        foreach ($this->followUpIndicators as $indicator) {
            if (strpos($lowercasePrompt, $indicator) !== false) {
                return true;
            }
        }

        // Check for context continuation at the beginning
        $words = explode(' ', trim($lowercasePrompt));
        if (!empty($words)) {
            $firstWord = $words[0];
            if (in_array($firstWord, $this->contextContinuationIndicators)) {
                return true;
            }
        }

        // Check for question patterns that suggest follow-up
        $followUpPatterns = [
            '/^(and|also|what about|how about)\s+/i',
            '/\b(more|additional|other|else)\b.*\?/i',
            '/^(show|list|tell)\s+me\s+(more|all|other)/i',
        ];

        foreach ($followUpPatterns as $pattern) {
            if (preg_match($pattern, $prompt)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Calculate confidence score for follow-up detection
     *
     * @param string $prompt User prompt
     * @return float Confidence score between 0 and 1
     */
    protected function calculateFollowUpConfidence(string $prompt): float
    {
        $confidence = 0.0;
        $lowercasePrompt = strtolower($prompt);

        // Strong indicators (high confidence)
        $strongIndicators = ['show me more', 'tell me more', 'what else', 'any other'];
        foreach ($strongIndicators as $indicator) {
            if (strpos($lowercasePrompt, $indicator) !== false) {
                $confidence += 0.3;
            }
        }

        // Medium indicators
        foreach ($this->followUpIndicators as $indicator) {
            if (strpos($lowercasePrompt, $indicator) !== false) {
                $confidence += 0.2;
            }
        }

        // Context continuation indicators
        $words = explode(' ', trim($lowercasePrompt));
        if (!empty($words) && in_array($words[0], $this->contextContinuationIndicators)) {
            $confidence += 0.25;
        }

        // Question mark increases confidence slightly
        if (strpos($prompt, '?') !== false) {
            $confidence += 0.1;
        }

        // Short prompts with follow-up words are more likely to be follow-ups
        if (str_word_count($prompt) <= 5) {
            $confidence += 0.15;
        }

        return min(1.0, $confidence);
    }

    /**
     * Analyze follow-up relationship between current and previous query
     *
     * @param string $currentPrompt Current user prompt
     * @param string $previousPrompt Previous user prompt
     * @return array Analysis of the relationship
     */
    public function analyzeFollowUpRelationship(string $currentPrompt, string $previousPrompt): array
    {
        $relationship = [
            'is_related' => false,
            'relationship_type' => 'none',
            'similarity_score' => 0.0,
            'shared_keywords' => [],
        ];

        // Extract keywords from both prompts
        $currentKeywords = $this->extractKeywords($currentPrompt);
        $previousKeywords = $this->extractKeywords($previousPrompt);

        // Find shared keywords
        $sharedKeywords = array_intersect($currentKeywords, $previousKeywords);
        $relationship['shared_keywords'] = array_values($sharedKeywords);

        // Calculate similarity score
        if (!empty($currentKeywords) && !empty($previousKeywords)) {
            $relationship['similarity_score'] = count($sharedKeywords) / 
                max(count($currentKeywords), count($previousKeywords));
        }

        // Determine relationship type
        if ($relationship['similarity_score'] > 0.3) {
            $relationship['is_related'] = true;
            
            if ($this->isExpansionQuery($currentPrompt)) {
                $relationship['relationship_type'] = 'expansion';
            } elseif ($this->isFilteringQuery($currentPrompt)) {
                $relationship['relationship_type'] = 'filtering';
            } elseif ($this->isDetailQuery($currentPrompt)) {
                $relationship['relationship_type'] = 'detail';
            } else {
                $relationship['relationship_type'] = 'related';
            }
        }

        return $relationship;
    }

    /**
     * Extract keywords from a prompt
     *
     * @param string $prompt User prompt
     * @return array Keywords
     */
    protected function extractKeywords(string $prompt): array
    {
        // Remove common stop words
        $stopWords = [
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for',
            'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have',
            'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should',
            'can', 'may', 'might', 'must', 'shall', 'this', 'that', 'these', 'those',
        ];

        // Extract words and filter
        $words = preg_split('/\s+/', strtolower($prompt));
        $keywords = array_filter($words, function ($word) use ($stopWords) {
            return strlen($word) > 2 && !in_array($word, $stopWords) && ctype_alpha($word);
        });

        return array_values($keywords);
    }

    /**
     * Check if current query is asking for expansion of previous results
     *
     * @param string $prompt Current prompt
     * @return bool True if expansion query
     */
    protected function isExpansionQuery(string $prompt): bool
    {
        $expansionIndicators = ['more', 'all', 'complete', 'full', 'entire', 'additional'];
        $lowercasePrompt = strtolower($prompt);

        foreach ($expansionIndicators as $indicator) {
            if (strpos($lowercasePrompt, $indicator) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if current query is asking for filtered results
     *
     * @param string $prompt Current prompt
     * @return bool True if filtering query
     */
    protected function isFilteringQuery(string $prompt): bool
    {
        $filteringIndicators = ['only', 'just', 'specific', 'particular', 'certain', 'where'];
        $lowercasePrompt = strtolower($prompt);

        foreach ($filteringIndicators as $indicator) {
            if (strpos($lowercasePrompt, $indicator) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if current query is asking for more details
     *
     * @param string $prompt Current prompt
     * @return bool True if detail query
     */
    protected function isDetailQuery(string $prompt): bool
    {
        $detailIndicators = ['details', 'information', 'about', 'explain', 'describe'];
        $lowercasePrompt = strtolower($prompt);

        foreach ($detailIndicators as $indicator) {
            if (strpos($lowercasePrompt, $indicator) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * Get follow-up suggestions based on previous query
     *
     * @param ChatbotQuery $previousQuery Previous query
     * @return array Suggested follow-up questions
     */
    public function getFollowUpSuggestions(ChatbotQuery $previousQuery): array
    {
        $suggestions = [];

        if (!$previousQuery->query_results || empty($previousQuery->query_results)) {
            return $suggestions;
        }

        // Analyze the previous query results to suggest follow-ups
        $resultCount = count($previousQuery->query_results);

        if ($resultCount > 0) {
            $suggestions[] = "Can you show me more details about these results?";
            $suggestions[] = "What else can you tell me about this data?";
        }

        if ($resultCount > 5) {
            $suggestions[] = "Can you filter these results?";
            $suggestions[] = "Show me only the most recent ones.";
        }

        // Add context-specific suggestions based on query content
        $queryLower = strtolower($previousQuery->user_question);
        
        if (strpos($queryLower, 'leave') !== false) {
            $suggestions[] = "What about pending leave requests?";
            $suggestions[] = "Show me leave requests for this month.";
        }

        if (strpos($queryLower, 'employee') !== false) {
            $suggestions[] = "What departments do these employees belong to?";
            $suggestions[] = "Show me their attendance records.";
        }

        return array_slice($suggestions, 0, 3); // Limit to 3 suggestions
    }
}
