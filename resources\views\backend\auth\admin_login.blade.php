@extends('frontend.auth.app')
@section('title', _trans('auth.Sign In'))
@section('content')
    <input type="hidden" id="login_success_fully" value="{{ _trans('frontend.Login successfully') }}">

    <form action="{{ route('admin.login') }}" method="post" id="login"
        class="auth-form d-flex justify-content-center align-items-start flex-column __login_form w-100 gap-15">
        @csrf

        <input type="hidden" hidden name="is_email" value="1">
        {{-- Email --}}
        <div class="input-field-group">
            <label for="username">{{ _trans('auth.Email') }}</label>
            <div class="custom-input-field login__field">
                <input type="email" name="email" class="login__input " value="{{ old('email') }}" id="username"
                    placeholder="{{ _trans('auth.Enter email') }}">
            </div>
            @error('email')
                <p class="text-danger cus-error __email text-12">{{ $message }}</p>
            @enderror
        </div>

        <!-- Password  -->
        <div class="input-field-group ">
            <div class="d-flex gap-2 justify-content-between">
                <label for="passwordLoginInput">{{ _trans('auth.Password') }} </label>
                <a href="{{ route('password.forget') }}" class="fogotPassword">
                    {{ _trans('auth.Forgot password?') }}</a>
            </div>
            <div class="custom-input-field password-input login__field">
                <input type="hidden" class="device_uuid" name="device_uuid" value="">
                <input type="password" name="password" class="login__input " id="passwordLoginInput"
                    placeholder="{{ _trans('auth.Enter password') }}">
                <i class="lar la-eye"></i>
            </div>
            @error('password')
                <p class="text-danger cus-error __password text-12">{{ $message }}</p>
            @enderror
        </div>


        @error('login_form_error')
            <div class="text-danger text-12">
                {{ $message }}
            </div>
        @enderror

        <button type="submit"
            class="btn-primary-fill  w-100 __login_btn  mt-6 d-flex align-items-center justify-content-center gap-8">
            <i class="las la-check-circle"></i>
            <span> {{ _trans('auth.Sign In') }}</span>
        </button>
    </form>

    {{-- Demo Link --}}
    <div class="row w-100">
        <div class="col-lg-12">
            <h6 class="text-center my-4">Role wise Demo login</h6>
        </div>
        @foreach ($users as $user)
            <div class="col-md-6">
                <form action="{{ route('admin.login') }}"
                    class="form justify-content-center align-items-start flex-column mb-3" method="post">
                    @csrf
                    <input type="hidden" name="email" value="{{ $user->email }}">
                    <input type="hidden" class="device_uuid" name="device_uuid" value="">
                    <input type="hidden" name="password" value="12345678">
                    <button type="submit" class="btn-primary-outline w-100 __demo_login_btn ">
                        {{ $user->name }}
                    </button>
                </form>
            </div>
        @endforeach
    </div>
    {{-- /Demo Link --}}

@endsection

@section('script')
    <script src="{{ global_asset('/') }}frontend/assets/jquery.min.js"></script>
    <script src="{{ global_asset('frontend/js/device-uuid.min.js') }}"></script>
    <script>
        var uuid = new DeviceUUID().get();
        $('.device_uuid').val(uuid);
        console.log('device_uuid ' + uuid);
    </script>
    <script src="{{ global_asset('/') }}frontend/assets/bootstrap/bootstrap.min.js"></script>
    <script src="{{ global_asset('/') }}backend/js/select2.min.js"></script>
    @include('backend.partials.message')
    <script src="{{ global_asset('js/toastr.js') }}"></script>
    {!! Toastr::message() !!}
    <script src="{{ global_asset('frontend/js/show-hide-password.js') }}"></script>
@endsection
