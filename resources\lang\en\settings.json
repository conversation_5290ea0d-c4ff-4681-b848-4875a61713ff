{"Language": "Language", "Distance (Meters)": "Distance (Meters)", "Enter Distance": "Enter Distance", "Distance measure in meters": "Distance measure in meters", "Location": "Location", "Enter Location": "Enter Location", "App Screen Setup": "App Screen Setup", "Settings": "Settings", "General": "General", "Email setup": "Email setup", "Firebase setup": "Firebase setup", "About System": "About System", "App Theme Setup": "App Theme Setup", "Type your company name": "Type your company name", "Background Image": "Background Image", "Recommended size:\n                                                1920 x 1080 px)": "Recommended size:\n                                                1920 x 1080 px)", "Logo": "Logo", "(Recommended\n                                                size: 210 x 50 px)": "(Recommended\n                                                size: 210 x 50 px)", "Company Logo Frontend": "Company Logo Frontend", "Company icon": "Company icon", "(Recommended size:\n                                                50 x 50 px)": "(Recommended size:\n                                                50 x 50 px)", "Company Icon": "Company Icon", "Website Footer": "Website Footer", "Type your company short description": "Type your company short description", "Android url": "Android url", "Android icon": "Android icon", "(Recommended size:\n                                                150 x 50 px)": "(Recommended size:\n                                                150 x 50 px)", "Ios url": "Ios url", "IOS icon": "IOS icon", "Email setup [SMTP]": "Email setup [SMTP]", "MAIL HOST": "MAIL HOST", "MAIL PORT": "MAIL PORT", "MAIL USERNAME": "MAIL USERNAME", "MAIL FROM ADDRESS": "MAIL FROM ADDRESS", "MAIL PASSWORD": "MAIL PASSWORD", "MAIL ENCRYPTION": "MAIL ENCRYPTION", "MAIL FROM NAME": "MAIL FROM NAME", "Geocoding setup": "Geocoding setup", "GEOCODING API KEY": "GEOCODING API KEY", "GEOCODING BASE URL": "GEOCODING BASE URL", "Pusher setup": "Pusher setup", "PUSHER APP ID": "PUSHER APP ID", "PUSHER APP KEY": "PUSHER APP KEY", "PUSHER APP SECRET": "PUSHER APP SECRET", "PUSHER APP CLUSTER": "PUSHER APP CLUSTER", "Payment Gateway": "Payment Gateway", "Stripe": "Stripe", "Stripe Key": "Stripe Key", "Stripe Secret": "Stripe Secret", "Enable Credit Card Payment": "Enable Credit Card Payment", "Enable Offline Payment": "Enable Offline Payment", "Enable Demo Checkout": "Enable Demo Checkout", "Enable Paystack Payment": "Enable Paystack Payment", "Paystack": "Paystack", "Paystack secret key": "Paystack secret key", "Paystack public key": "Paystack public key", "Paystack URL": "Paystack URL", "Paystack callback URL": "Paystack callback <PERSON><PERSON>", "Offline Payment Type": "Offline Payment Type", "Cash": "Cash", "Cheque": "Cheque", "Bank Transfer": "Bank Transfer", "Storage setup": "Storage setup", "Default storage": "Default storage", "Local": "Local", "S3": "S3", "AWS ACCESS KEY ID": "AWS ACCESS KEY ID", "AWS SECRET ACCESS KEY": "AWS SECRET ACCESS KEY", "AWS DEFAULT REGION": "AWS DEFAULT REGION", "AWS BUCKET": "AWS BUCKET", "AWS USE PATH STYLE ENDPOINT": "AWS USE PATH STYLE ENDPOINT", "Yes": "Yes", "No": "No", "IP Whitelist": "IP Whitelist", "Latitude": "Latitude", "Longitude": "Longitude", "Distance": "Distance", "User": "User", "Location Binding": "Location Binding", "Configuration": "Configuration", "Recommended size:\r\n                                                1920 x 1080 px)": "Recommended size:\r\n                                                1920 x 1080 px)", "(Recommended\r\n                                                size: 210 x 50 px)": "(Recommended\r\n                                                size: 210 x 50 px)", "(Recommended size:\r\n                                                50 x 50 px)": "(Recommended size:\r\n                                                50 x 50 px)", "(Recommended size:\r\n                                                150 x 50 px)": "(Recommended size:\r\n                                                150 x 50 px)", "FIREBASE API KEY": "FIREBASE API KEY", "FIREBASE AUTH DOMAIN": "FIREBASE AUTH DOMAIN", "FIREBASE AUTH DATABASE URL": "FIREBASE AUTH DATABASE URL", "FIREBASE AUTH PROJECT ID": "FIREBASE AUTH PROJECT ID", "FIREBASE AUTH STORAGE BUCKET": "FIREBASE AUTH STORAGE BUCKET", "FIREBASE AUTH SENDER ID": "FIREBASE AUTH SENDER ID", "FIREBASE AUTH APP ID": "FIREBASE AUTH APP ID", "FIREBASE AUTH MEASUREMENT ID": "FIREBASE AUTH MEASUREMENT ID", "FIREBASE AUTH COLLECTION NAME": "FIREBASE AUTH COLLECTION NAME", "FIREBASE CREDENTIALS JSON": "FIREBASE CREDENTIALS JSON", "Setup": "Setup", "Change Status": "Change Status", "Make Default": "Make Default", "Create Language": "Create Language", "Native": "Native", "Code": "Code", "RTL": "RTL", "Edit Language": "Edit Language", "Addon Activation": "Addon Activation", "Break": "Break", "Restricted": "Restricted", "Notify": "Notify", "PushNotification": "PushNotification", "Saas": "Saas", "SpecialAttendance": "SpecialAttendance", "Travel": "Travel", "AreaBasedAttendance": "AreaBasedAttendance", "Enable": "Enable", "Disable": "Disable", "AutoAttendance": "AutoAttendance", "BulkImport": "BulkImport", "Bulletin": "Bulletin", "Credential": "Credential", "EmployeeDocuments": "EmployeeDocuments", "EmployeePerformance": "EmployeePerformance", "HRPolicies": "HRPolicies", "Invoice": "Invoice", "IpBasedAttendance": "IpBasedAttendance", "LiveTracking": "LiveTracking", "Meeting": "Meeting", "MultiBranch": "MultiBranch", "MultiShift": "MultiShift", "MultiTheme": "MultiTheme", "QrBasedAttendance": "QrBasedAttendance", "SelfieBasedAttendance": "SelfieBasedAttendance", "Services": "Services", "SingleDeviceLogin": "SingleDeviceLogin", "Tardy": "<PERSON><PERSON>", "VideoConference": "VideoConference", "WriteUp": "WriteUp", "Brandings": "Brandings", "Logo & Font Family": "Logo & Font Family", "App Name": "App Name", "Font Family": "Font Family", "Primary Color": "Primary Color", "Primary Light Color": "Primary Light Color", "Primary Dark Color": "Primary Dark Color", "Card Background Default": "Card Background Default", "Card Background Subdued": "Card Background Subdued", "Text Primary": "Text Primary", "Text Secondary": "Text Secondary", "Text Tertiary": "Text Tertiary", "Text Hint": "Text Hint", "Text Disabled": "Text Disabled", "Text Inverse Primary": "Text Inverse Primary", "Text Inverse Secondary": "Text Inverse Secondary", "Text Inverse Tertiary": "Text Inverse Tertiary", "Brand Text Link": "Brand Text Link", "Icon Primary": "Icon Primary", "Icon Secondary": "Icon Secondary", "Icon Disabled": "Icon Disabled", "Icon Inverse": "Icon Inverse", "Icon Accent": "Icon Accent", "Icon Navigation Bar": "Icon Navigation Bar", "Icon Accent Inactive": "Icon Accent Inactive", "Divider Primary": "Divider Primary", "Divider Inverse": "Divider Inverse", "Button Default": "<PERSON><PERSON>", "Button Secondary": "Button Secondary", "Button Inverse": "Button Inverse", "Button Disabled": "<PERSON><PERSON> Disabled", "Container": "Container", "Container Secondary": "Container Secondary", "Activation": "Activation", "Edit Currency": "<PERSON>", "Currency\n                                            Name": "Currency\n                                            Name", "Currency\n                                            Code": "Currency\n                                            Code", "Currency\n                                            Icon": "<PERSON><PERSON><PERSON><PERSON>", "Add Currency": "Add <PERSON>cy", "Currency Name": "Currency Name", "Currency Code": "Currency Code", "Currency Icon": "<PERSON><PERSON><PERSON><PERSON>", "Choose one theme": "Choose one theme", "Settings updated successfully": "Settings updated successfully", "Email settings updated successfully": "Email settings updated successfully", "Storage settings updated successfully": "Storage settings updated successfully", "Date and time setting": "Date and time setting", "TimeZone": "TimeZone", "Date format": "Date format", "Language setting": "Language setting", "Select Language": "Select Language", "Attendance setting": "Attendance setting", "Attendance Method": "Attendance Method", "Max Work Hours": "Max Work Hours", "Live tracking setting": "Live tracking setting", "App sync time": "App sync time", "Live data store after": "Live data store after", "Live data store time": "Live data store time", "Api Key": "Api Key", "Google Map Key": "Google Map Key", "Google Font app key": "Google Font app key", "Google Firebase Key": "Google Firebase Key", "Google Meet Credentials": "Google Meet Credentials", "Google Admin Email": "Google Admin Email", "Currency setting": "Currency setting", "Currency": "<PERSON><PERSON><PERSON><PERSON>", "Fiscal Year": "Fiscal Year", "Time format": "Time format", "12 HOURS": "12 HOURS", "24 HOURS": "24 HOURS", "Tardy Applicable": "Tardy Applicable", "IP Address Bind": "IP Address Bind", "Location Bind": "Location Bind", "Multiple Check In & Check Out": "Multiple Check In & Check Out", "Live Tracking": "Live Tracking", "Leave Assign": "Leave Assign", "User Wise": "User Wise", "Department Wise": "Department Wise", "NB: If You Change Leave Assign, Your\n                                                            Existing Leave Assign Will Be Deleted": "NB: If You Change Leave Assign, Your\n                                                            Existing Leave Assign Will Be Deleted", "NB: If You Change Leave Assign, Your\n                                                                Existing Leave Assign Will Be Deleted": "NB: If You Change Leave Assign, Your\n                                                                Existing Leave Assign Will Be Deleted", "NB: If You Change Leave Assign, Your\n                                    Existing Leave Assign Will Be Deleted": "NB: If You Change Leave Assign, Your\n                                    Existing Leave Assign Will Be Deleted", "Activation updated successfully": "Activation updated successfully", "AssetManagement": "AssetManagement", "SystemGuidelines": "SystemGuidelines"}