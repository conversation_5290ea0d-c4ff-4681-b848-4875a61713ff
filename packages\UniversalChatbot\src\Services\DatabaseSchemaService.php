<?php

namespace UniversalChatbot\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class DatabaseSchemaService
{
    /**
     * Get database schema information
     *
     * @return string Database schema description
     */
    public function getDatabaseSchemaInfo(): string
    {
        $useCompactSchema = config('universal_chatbot.ai.use_compact_schema', true);
        $schemaTableLimit = config('universal_chatbot.ai.schema_table_limit', 25);

        // Try to get schema dynamically
        if ($useCompactSchema) {
            $schema = $this->getCompactDatabaseSchema($schemaTableLimit);
        } else {
            $schema = $this->getDynamicDatabaseSchema($schemaTableLimit);
        }

        if (!empty($schema)) {
            return $schema;
        }

        // Fall back to hardcoded schema if dynamic generation fails
        return $this->getFallbackSchema();
    }

    /**
     * Generate compact database schema information
     *
     * @param int|null $limit Limit the number of tables
     * @return string
     */
    public function getCompactDatabaseSchema(?int $limit = 30): string
    {
        // Check cache first - cached for 24 hours
        $cacheKey = 'chatbot_db_schema_compact';
        if (cache()->has($cacheKey)) {
            return cache()->get($cacheKey);
        }

        try {
            $connection = DB::connection();
            $driver     = $connection->getDriverName();
            $tables     = $this->getDatabaseTables($connection, $driver);

            // Skip Laravel system tables
            $skipTables = ['migrations', 'password_resets', 'failed_jobs', 'personal_access_tokens'];

            // Sort tables by importance
            $importantTables = [
                'users', 'departments', 'designations', 'companies', 'branches',
                'leave_requests', 'leave_types', 'holidays', 'attendance',
            ];

            usort($tables, function ($a, $b) use ($importantTables) {
                $indexA = array_search($a, $importantTables);
                $indexB = array_search($b, $importantTables);

                if ($indexA !== false && $indexB !== false) {
                    return $indexA - $indexB;
                }

                if ($indexA !== false) {
                    return -1;
                }

                if ($indexB !== false) {
                    return 1;
                }

                return strcmp($a, $b);
            });

            $results     = [];
            $tableCount  = 0;
            $foreignKeys = $this->getDatabaseForeignKeys($connection, $driver);

            foreach ($tables as $tableName) {
                // Skip system tables
                if (in_array($tableName, $skipTables)) {
                    continue;
                }

                $tableInfo = $this->getTableInfo($connection, $driver, $tableName, $foreignKeys);
                if (!empty($tableInfo)) {
                    $results[] = $tableInfo;
                    $tableCount++;

                    if ($tableCount >= $limit) {
                        break;
                    }
                }
            }

            $output = implode("\n\n", $results);

            // Cache the result for 24 hours
            cache()->put($cacheKey, $output, now()->addHours(24));

            return $output;
        } catch (\Exception $e) {
            Log::error('Failed to generate compact schema: ' . $e->getMessage());
            return '';
        }
    }

    /**
     * Get list of database tables
     *
     * @param \Illuminate\Database\Connection $connection
     * @param string $driver
     * @return array
     */
    public function getDatabaseTables($connection, string $driver): array
    {
        if ($driver === 'pgsql') {
            $tables = $connection->select("
                SELECT table_name
                FROM information_schema.tables
                WHERE table_schema = 'public'
                ORDER BY table_name
            ");

            return array_map(function ($row) {
                return $row->table_name;
            }, $tables);
        } else {
            // MySQL
            $tables     = $connection->select('SHOW TABLES');
            $columnName = 'Tables_in_' . config("database.connections.{$driver}.database");

            return array_map(function ($row) use ($columnName) {
                return $row->$columnName;
            }, $tables);
        }
    }

    /**
     * Get foreign keys for all tables
     *
     * @param \Illuminate\Database\Connection $connection
     * @param string $driver
     * @return array
     */
    public function getDatabaseForeignKeys($connection, string $driver): array
    {
        $foreignKeys = [];

        try {
            if ($driver === 'pgsql') {
                $fkQuery = "
                    SELECT
                        tc.table_name,
                        kcu.column_name,
                        ccu.table_name AS foreign_table_name,
                        ccu.column_name AS foreign_column_name
                    FROM information_schema.table_constraints AS tc
                    JOIN information_schema.key_column_usage AS kcu
                        ON tc.constraint_name = kcu.constraint_name
                        AND tc.table_schema = kcu.table_schema
                    JOIN information_schema.constraint_column_usage AS ccu
                        ON ccu.constraint_name = tc.constraint_name
                        AND ccu.table_schema = tc.table_schema
                    WHERE tc.constraint_type = 'FOREIGN KEY'
                ";
                $fkResults = $connection->select($fkQuery);
            } else {
                // MySQL
                $dbName  = config("database.connections.{$driver}.database");
                $fkQuery = '
                    SELECT
                        TABLE_NAME as table_name,
                        COLUMN_NAME as column_name,
                        REFERENCED_TABLE_NAME as foreign_table_name,
                        REFERENCED_COLUMN_NAME as foreign_column_name
                    FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
                    WHERE REFERENCED_TABLE_SCHEMA = ?
                    AND REFERENCED_TABLE_NAME IS NOT NULL
                ';
                $fkResults = $connection->select($fkQuery, [$dbName]);
            }

            foreach ($fkResults as $fk) {
                if (!isset($foreignKeys[$fk->table_name])) {
                    $foreignKeys[$fk->table_name] = [];
                }
                $foreignKeys[$fk->table_name][$fk->column_name] = [
                    'table'  => $fk->foreign_table_name,
                    'column' => $fk->foreign_column_name,
                ];
            }
        } catch (\Exception $e) {
            Log::warning('Failed to get foreign keys: ' . $e->getMessage());
        }

        return $foreignKeys;
    }

    /**
     * Get cross-database compatible SQL function
     * Helps when working with both MySQL and PostgreSQL
     *
     * @param string $function Function name (e.g., 'DATE', 'CONCAT')
     * @param array $arguments Function arguments
     * @return string SQL function call
     */
    public function dbFunction(string $function, array $arguments = []): string
    {
        $connection = DB::connection();
        $driver     = $connection->getDriverName();

        switch (strtoupper($function)) {
            case 'DATE':
                return $driver === 'pgsql'
                ? "DATE({$arguments[0]})"
                : "DATE({$arguments[0]})";

            case 'NOW':
                return $driver === 'pgsql' ? 'NOW()' : 'NOW()';

            case 'CONCAT':
                if ($driver === 'pgsql') {
                    return implode(' || ', $arguments);
                } else {
                    return 'CONCAT(' . implode(', ', $arguments) . ')';
                }

            case 'TIME_TO_SEC':
                if ($driver === 'pgsql') {
                    // PostgreSQL doesn't have TIME_TO_SEC, convert using extract
                    return "EXTRACT(EPOCH FROM {$arguments[0]}::time)";
                } else {
                    return "TIME_TO_SEC({$arguments[0]})";
                }

            case 'DATE_FORMAT':
                if ($driver === 'pgsql') {
                    // Convert MySQL date format to PostgreSQL
                    $format = $arguments[1];
                    $format = str_replace('%Y', 'YYYY', $format);
                    $format = str_replace('%m', 'MM', $format);
                    $format = str_replace('%d', 'DD', $format);
                    $format = str_replace('%H', 'HH24', $format);
                    $format = str_replace('%i', 'MI', $format);
                    $format = str_replace('%s', 'SS', $format);

                    return "TO_CHAR({$arguments[0]}::timestamp, '{$format}')";
                } else {
                    return "DATE_FORMAT({$arguments[0]}, {$arguments[1]})";
                }

            case 'DAYNAME':
                if ($driver === 'pgsql') {
                    return "TO_CHAR({$arguments[0]}::timestamp, 'Day')";
                } else {
                    return "DAYNAME({$arguments[0]})";
                }

            default:
                // For unsupported functions, return as-is (might not work across databases)
                $args = implode(', ', $arguments);
                return "{$function}({$args})";
        }
    }

    /**
     * Get detailed info for a specific table
     *
     * @param \Illuminate\Database\Connection $connection
     * @param string $driver
     * @param string $tableName
     * @param array $foreignKeys
     * @return string
     */
    protected function getTableInfo($connection, string $driver, string $tableName, array $foreignKeys): string
    {
        try {
            // Get columns
            if ($driver === 'pgsql') {
                $columns = $connection->select("
                    SELECT column_name, data_type, character_maximum_length,
                           is_nullable, column_default,
                           (SELECT COUNT(*) FROM information_schema.table_constraints tc
                            JOIN information_schema.constraint_column_usage ccu
                              ON tc.constraint_name = ccu.constraint_name
                            WHERE tc.constraint_type = 'PRIMARY KEY'
                              AND tc.table_name = c.table_name
                              AND ccu.column_name = c.column_name) as is_primary_key
                    FROM information_schema.columns c
                    WHERE table_name = ?
                    ORDER BY ordinal_position
                ", [$tableName]);
            } else {
                // MySQL
                $columns = $connection->select("SHOW COLUMNS FROM `{$tableName}`");
            }

            $output = ["Table: {$tableName}", 'Columns:'];

            foreach ($columns as $column) {
                $columnName     = $driver === 'pgsql' ? $column->column_name : $column->Field;
                $foreignKeyInfo = isset($foreignKeys[$tableName][$columnName])
                ? ", -> {$foreignKeys[$tableName][$columnName]['table']}.{$foreignKeys[$tableName][$columnName]['column']}"
                : '';

                if ($driver === 'pgsql') {
                    $type       = $column->data_type;
                    $primaryKey = $column->is_primary_key > 0 ? ' (PK)' : '';

                    // Handle enums for PostgreSQL
                    if ($type === 'USER-DEFINED') {
                        try {
                            $enumQuery = '
                                SELECT pg_enum.enumlabel
                                FROM pg_type
                                JOIN pg_enum ON pg_enum.enumtypid = pg_type.oid
                                JOIN pg_catalog.pg_namespace ON pg_namespace.oid = pg_type.typnamespace
                                JOIN information_schema.columns ON columns.udt_name = pg_type.typname
                                WHERE columns.table_name = ? AND columns.column_name = ?
                                ORDER BY pg_enum.enumsortorder
                            ';
                            $enumValues = $connection->select($enumQuery, [$tableName, $columnName]);

                            if (count($enumValues) > 0) {
                                $values = array_map(function ($val) {
                                    return "'{$val->enumlabel}'";
                                }, $enumValues);

                                // Limit to first few values if too many
                                if (count($values) > 4) {
                                    $values   = array_slice($values, 0, 3);
                                    $values[] = '...';
                                }

                                $type = 'enum: ' . implode(', ', $values);
                            }
                        } catch (\Exception $e) {
                            // Keep original type if error
                        }
                    }
                } else {
                    // MySQL format
                    $type       = $column->Type;
                    $primaryKey = $column->Key === 'PRI' ? ' (PK)' : '';

                    // Format MySQL enums for better readability
                    if (strpos($type, 'enum') === 0) {
                        $type = preg_replace('/enum\((.*)\)/', 'enum: $1', $type);

                        // Limit enum values if too many
                        $enumValues = explode(',', preg_replace('/enum: /', '', $type));
                        if (count($enumValues) > 4) {
                            $limitedValues = array_slice($enumValues, 0, 3);
                            $type          = 'enum: ' . implode(',', $limitedValues) . ',...';
                        }
                    }
                }

                $output[] = "  - {$columnName}{$primaryKey}: {$type}{$foreignKeyInfo}";
            }

            return implode("\n", $output);
        } catch (\Exception $e) {
            Log::warning("Failed to get info for table {$tableName}: " . $e->getMessage());
            return '';
        }
    }

    /**
     * Generate dynamic database schema information from the database
     *
     * @param int|null $limit Limit the number of tables
     * @return string
     */
    protected function getDynamicDatabaseSchema(?int $limit = 20): string
    {
        try {
            $connection = DB::connection();
            $driver     = $connection->getDriverName();

            // Get tables based on database driver
            $tables = $this->getDatabaseTables($connection, $driver);

            // Sort tables by importance
            $importantTables = [
                'users', 'departments', 'designations', 'companies', 'branches',
                'leave_requests', 'leave_types', 'holidays', 'holiday_applied_departments',
                'attendance', 'user_breaks',
            ];

            usort($tables, function ($a, $b) use ($importantTables) {
                $indexA = array_search($a, $importantTables);
                $indexB = array_search($b, $importantTables);

                if ($indexA !== false && $indexB !== false) {
                    return $indexA - $indexB;
                }

                if ($indexA !== false) {
                    return -1;
                }

                if ($indexB !== false) {
                    return 1;
                }

                return strcmp($a, $b);
            });

            // Skip Laravel system tables
            $skipTables = ['migrations', 'password_resets', 'failed_jobs', 'personal_access_tokens'];

            $output      = [];
            $tableCount  = 0;
            $foreignKeys = $this->getDatabaseForeignKeys($connection, $driver);

            // Process tables and columns
            foreach ($tables as $tableName) {
                // Skip system tables
                if (in_array($tableName, $skipTables)) {
                    continue;
                }

                try {
                    $tableInfo = $this->getTableInfo($connection, $driver, $tableName, $foreignKeys);
                    if (!empty($tableInfo)) {
                        $output[] = $tableInfo;
                        $output[] = ''; // Add blank line between tables

                        $tableCount++;
                        if ($tableCount >= $limit) {
                            break;
                        }
                    }
                } catch (\Exception $e) {
                    // If we can't get the columns, log the error and skip this table
                    Log::warning("Failed to get columns for table {$tableName}: " . $e->getMessage());
                    continue;
                }
            }

            return implode("\n", $output);
        } catch (\Exception $e) {
            // If anything goes wrong, just return an empty string and we'll fall back to the hardcoded schema
            Log::error('Failed to generate dynamic schema: ' . $e->getMessage());
            return '';
        }
    }

    /**
     * Get fallback hardcoded schema
     *
     * @return string
     */
    protected function getFallbackSchema(): string
    {
        return <<<'EOT'
Table: leave_requests
Columns:
  - id (PK): integer
  - leave_assign_id: integer, -> leave_assigns.id
  - user_id: integer, -> users.id
  - apply_date: date
  - leave_from: date
  - leave_to: date
  - days: integer
  - reason: text
  - status: enum: 'approved', 'pending', 'rejected', 'cancelled', 'referred'

Table: leave_assigns
Columns:
  - id (PK): integer
  - type_id: integer, -> leave_types.id
  - days: integer
  - status: enum: 'active', 'inactive'
  - user_id: integer, -> users.id

Table: leave_types
Columns:
  - id (PK): integer
  - name: string
  - status: enum: 'active', 'inactive'
  - is_paid: boolean
  - gender: enum: 'Male', 'Female', 'Unisex', 'All'

Table: users
Columns:
  - id (PK): integer
  - name: string
  - email: string
  - employee_id: string
  - department_id: integer, -> departments.id
  - designation_id: integer, -> designations.id

Table: departments
Columns:
  - id (PK): integer
  - title: string
  - status_id: integer
EOT;
    }
}