<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAttendancesTable extends Migration
{
    public function up()
    {
        Schema::disableForeignKeyConstraints();

        Schema::create('attendances', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->cascadeOnDelete();
            $table->date('date')->nullable();
            $table->timestamp('check_in')->nullable();
            $table->timestamp('check_out')->nullable();
            $table->time('stay_time')->nullable();
            $table->time('worked_time')->nullable();
            $table->time('break_time')->nullable();
            $table->time('over_time')->nullable();
            $table->time('late_duration')->nullable();
            $table->time('early_exit_duration')->nullable();
            $table->enum('checkin_status', ['on_time', 'late', 'absent'])->nullable();
            $table->enum('checkout_status', ['left_timely', 'left_early', 'left_later'])->nullable();
            $table->string('late_reason')->nullable();
            $table->string('early_exit_reason')->nullable();
            $table->boolean('exempt_from_penalties')->default(false);
            $table->json('check_in_info')->nullable();
            $table->json('check_out_info')->nullable();
            $table->json('attendance_log')->nullable();
            $table->foreignId('duty_schedule_id')->nullable()->constrained('duty_schedules');
            $table->enum('status', ['check_in', 'break', 'check_out'])->default('check_in');
            $table->foreignId('company_id')->nullable()->constrained('companies')->default(1);
            $table->foreignId('branch_id')->nullable()->constrained('branches')->default(1);
            $table->timestamps();
        });

        Schema::enableForeignKeyConstraints();
    }

    public function down()
    {
        Schema::dropIfExists('attendances');
    }
}
