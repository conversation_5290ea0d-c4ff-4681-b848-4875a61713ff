<?php

namespace UniversalChatbot\Services;

use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class ResponseFormatter
{
    protected GeminiApiClient $geminiClient;

    public function __construct(GeminiApiClient $geminiClient)
    {
        $this->geminiClient = $geminiClient;
    }

    /**
     * Format query results using AI
     *
     * @param string $prompt Original user question
     * @param array $queryResults Database query results
     * @param string $sqlQuery SQL query that was executed
     * @param array $conversationHistory Previous conversation
     * @return string Formatted response
     */
    public function formatQueryResults(string $prompt, array $queryResults, string $sqlQuery, array $conversationHistory = []): string
    {
        $currentDate = Carbon::now()->format('Y-m-d');

        // Convert query results to JSON string
        $resultsJson = json_encode($queryResults['data'], JSON_PRETTY_PRINT);

        // Create formatting prompt
        $formattingPrompt = $this->createFormattingPrompt($prompt, $sqlQuery, $resultsJson, $currentDate);

        // Call Gemini API
        $response = $this->geminiClient->formatResponse($formattingPrompt);

        if (!$response['success']) {
            Log::error('Failed to format query results: ' . ($response['error'] ?? 'Unknown error'));
            // Fallback to simple formatting
            return $this->fallbackFormatResults($queryResults, $prompt);
        }

        // Extract text from response
        $text = $this->geminiClient->extractTextFromResponse($response['response']);
        if ($text) {
            return $text;
        }

        // Fallback if response format is unexpected
        return $this->fallbackFormatResults($queryResults, $prompt);
    }

    /**
     * Generate response based on context when database query is not needed
     *
     * @param string $prompt User's question
     * @param array $context Context information
     * @param array $conversationHistory Previous conversation
     * @return string Response
     */
    public function generateContextBasedResponse(string $prompt, array $context, array $conversationHistory = []): string
    {
        $currentDate = Carbon::now()->format('Y-m-d');

        // Format context
        $contextText = '';
        if (!empty($context)) {
            $contextText = "Context information:\n\n";
            foreach ($context as $index => $chunk) {
                $contextText .= "Context #{$index}:\n{$chunk['text']}\n";
                if (isset($chunk['source'])) {
                    $contextText .= "Source: {$chunk['source']}\n";
                }
                $contextText .= "\n";
            }
        }

        // Format conversation history
        $conversationText = '';
        if (!empty($conversationHistory)) {
            $conversationText = "Previous conversation:\n";
            foreach (array_slice($conversationHistory, -3) as $exchange) {
                $conversationText .= "User: {$exchange['message_text']}\n";
                $conversationText .= "Assistant: {$exchange['response_text']}\n";
            }
            $conversationText .= "\n";
        }

        // Create prompt
        $fullPrompt = <<<EOT
You are a helpful assistant for the Onest HRM system. Today's date is {$currentDate}.

{$conversationText}
{$contextText}

User question: {$prompt}

Please provide a helpful, concise response based on the available information.
EOT;

        // Call Gemini API
        $response = $this->geminiClient->generateContextResponse($fullPrompt);

        if (!$response['success']) {
            Log::error('Failed to generate context-based response: ' . ($response['error'] ?? 'Unknown error'));
            return "I'm sorry, I couldn't process your request at the moment. Please try again later.";
        }

        // Extract text from response
        $text = $this->geminiClient->extractTextFromResponse($response['response']);
        if ($text) {
            return $text;
        }

        return "I'm sorry, I couldn't generate a response at the moment. Please try again later.";
    }

    /**
     * Create formatting prompt for query results
     *
     * @param string $prompt Original user question
     * @param string $sqlQuery SQL query that was executed
     * @param string $resultsJson Query results as JSON
     * @param string $currentDate Current date
     * @return string Formatting prompt
     */
    protected function createFormattingPrompt(string $prompt, string $sqlQuery, string $resultsJson, string $currentDate): string
    {
        return <<<EOT
You are an AI assistant for an HRM system. The user asked the following question:

USER QUESTION: {$prompt}

I executed the following SQL query to answer this question:
```sql
{$sqlQuery}
```

The query returned the following results:
```json
{$resultsJson}
```

Your task is to format these results into a clear, concise, and helpful response for the user.
IMPORTANT: Today's date is {$currentDate}.

Guidelines:
1. If the query was successful, explain the results in natural language
2. If there are no results, explain that no data was found for the query
3. Format lists, counts, or statistics in a readable way
4. Refer to the current date when relevant (e.g., for "today" queries)
5. Keep your response professional but conversational
6. Be concise and direct - focus only on answering the question with the data provided

Please provide ONLY the final response to the user without any explanations of your process.
EOT;
    }

    /**
     * Fallback method to format results if AI formatting fails
     *
     * @param array $queryResults Database query results
     * @param string $prompt Original user question
     * @return string Formatted response
     */
    protected function fallbackFormatResults(array $queryResults, string $prompt): string
    {
        if (!$queryResults['success']) {
            return "I couldn't retrieve the data needed to answer your question. There was an issue with the database query.";
        }

        if (empty($queryResults['data'])) {
            return "I didn't find any data matching your question about \"" . $prompt . '".';
        }

        $response = "Here's what I found:\n\n";

        foreach ($queryResults['data'] as $index => $row) {
            $response .= ($index + 1) . '. ';
            $rowData = [];

            foreach ($row as $key => $value) {
                if ($value !== null) {
                    $rowData[] = "{$key}: {$value}";
                }
            }

            $response .= implode(', ', $rowData) . "\n";
        }

        return $response;
    }

    /**
     * Format response for different output types
     *
     * @param array $data Data to format
     * @param string $format Output format (table, list, summary)
     * @return string Formatted response
     */
    public function formatDataByType(array $data, string $format = 'list'): string
    {
        if (empty($data)) {
            return "No data available.";
        }

        switch ($format) {
            case 'table':
                return $this->formatAsTable($data);
            case 'summary':
                return $this->formatAsSummary($data);
            case 'list':
            default:
                return $this->formatAsList($data);
        }
    }

    /**
     * Format data as a table
     *
     * @param array $data Data to format
     * @return string Table formatted response
     */
    protected function formatAsTable(array $data): string
    {
        if (empty($data)) {
            return "No data to display.";
        }

        $headers = array_keys($data[0]);
        $response = "| " . implode(" | ", $headers) . " |\n";
        $response .= "|" . str_repeat("---|", count($headers)) . "\n";

        foreach ($data as $row) {
            $values = array_map(function ($value) {
                return $value ?? 'N/A';
            }, array_values($row));
            $response .= "| " . implode(" | ", $values) . " |\n";
        }

        return $response;
    }

    /**
     * Format data as a list
     *
     * @param array $data Data to format
     * @return string List formatted response
     */
    protected function formatAsList(array $data): string
    {
        $response = "";
        foreach ($data as $index => $row) {
            $response .= ($index + 1) . ". ";
            $rowData = [];
            foreach ($row as $key => $value) {
                if ($value !== null) {
                    $rowData[] = "{$key}: {$value}";
                }
            }
            $response .= implode(', ', $rowData) . "\n";
        }
        return $response;
    }

    /**
     * Format data as a summary
     *
     * @param array $data Data to format
     * @return string Summary formatted response
     */
    protected function formatAsSummary(array $data): string
    {
        $count = count($data);
        return "Found {$count} record(s) matching your query.";
    }
}
