<?php

namespace App\Http\Controllers\Backend\Attendance;

use App\Helpers\CoreApp\Traits\ApiReturnFormatTrait;
use App\Http\Controllers\Controller;
use App\Models\coreApp\Relationship\RelationshipTrait;
use App\Models\Hrm\Attendance\Attendance;
use App\Repositories\Hrm\Attendance\AttendanceRepository;
use App\Repositories\Hrm\Department\DepartmentRepository;
use App\Repositories\UserRepository;
use App\Services\Hrm\EmployeeBreakService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CheckInController extends Controller
{
    use ApiReturnFormatTrait, RelationshipTrait;

    protected $attendanceRepo;

    protected $departmentRepository;

    protected $userRepository;

    protected $breakBackService;

    public function __construct(AttendanceRepository $attendanceRepo, DepartmentRepository $departmentRepository, UserRepository $userRepository, EmployeeBreakService $breakBackService)
    {
        $this->attendanceRepo = $attendanceRepo;
        $this->departmentRepository = $departmentRepository;
        $this->userRepository = $userRepository;
        $this->breakBackService = $breakBackService;
    }

    public function dashboardAjaxCheckinModal(Request $request)
    {
        try {
            $data['title'] = _trans('common.Check In');
            $data['url'] = \route('admin.ajaxDashboardCheckin');
            $data['button'] = _trans('common.Check In');
            $data['type'] = 'checkin';

            // $dutySchedule = Auth::user()->attendanceConfig->dutySchedules->first();

            // $data['reason'] = $this->attendance_repo->checkInStatus($dutySchedule);

            return \view('backend.attendance.attendance.check_in_modal', \compact('data'));
        } catch (\Throwable $th) {
            \info($th->getMessage());

            return \response()->json('fail');
        }
    }

    public function dashboardAjaxCheckin(Request $request)
    {
        try {
            $this->attendanceRepo->checkIn($request);

            return $this->responseWithSuccess('Check in successfully', \route('admin.dashboard'), 200);
        } catch (\Exception $exception) {
            return catchHandler($exception);
        }
    }

    public function ajaxDashboardCheckOutModal(Request $request)
    {
        try {
            $data['title'] = _trans('common.Check Out');
            $data['url'] = route('admin.ajaxDashboardCheckOut');
            $data['button'] = _trans('common.Check Out');
            $data['type'] = 'checkin';

            $attendance = Attendance::with(['dutyCalendar'])
                ->where('user_id', Auth::id())
                ->where('date', '>=', date('Y-m-d', strtotime('-1 days')))
                ->where('check_in', '!=', null)
                ->where('check_out', '=', null)
                ->first();

            if (! $attendance) {
                return \response()->json('fail');
            }

            $data['break_time'] = $attendance->break_time;

            // $data['reason'] = $this->attendanceRepo->checkOutStatus(@$attendance->dutyCalendar);

            return view('backend.attendance.attendance.check_in_modal', compact('data'));
        } catch (\Throwable $th) {
            \info($th->getMessage());

            return \response()->json('fail');
        }
    }

    public function ajaxDashboardCheckOut(Request $request)
    {
        try {
            $this->attendanceRepo->checkOut($request);

            return $this->responseWithSuccess('Check Out Successfully', \route('admin.dashboard'), 200);
        } catch (\Exception $exception) {
            return catchHandler($exception);
        }
    }

    public function dashboardAjaxBreakModal(Request $request)
    {
        try {
            $data['title'] = _trans('common.Take Break');
            $data['url'] = \route('admin.ajaxDashboardBreak');

            return \view('backend.attendance.attendance.break', \compact('data'));
        } catch (\Throwable $th) {
            return \response()->json('fail');
        }
    }

    public function dashboardAjaxBreak(Request $request)
    {
        try {
            $request['time'] = \date('H:i:s');
            $break = $this->breakBackService->breakStartEnd($request, 'start');
            if ($break->original['result']) {
                $route = [\route('admin.ajaxDashboardBreakModal_Back'), \route('admin.ajaxDashboardBreakModal')];

                return $this->responseWithSuccess($break->original['message'], $route, 200);
            } else {
                return $this->responseWithError($break->original['message'], false);
            }
        } catch (\Throwable $th) {
            return $this->responseWithError($th->getMessage());
        }
    }

    public function ajaxDashboardBreakModalBack(Request $request, $slug = 'back')
    {
        try {
            $request['time'] = \date('H:i:s');
            $data['title'] = _trans('common.Back Break time');
            $data = $this->breakBackService->breakStartEndWeb($request, $slug);
            if (! $data) {
                return \response()->json('fail');
            } else {
                return \view('backend.modal.break_back', \compact('data'));
            }
        } catch (\Throwable $th) {
            return \response()->json('fail');
        }
    }
}
