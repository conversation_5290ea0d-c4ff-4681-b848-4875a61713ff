/*
    Main scss for the application.

    Index
    -----

    1. Variables
    2. Mixins
    3. Functions
    4. Fonts
    5. Config
    6. Components
        1) Scrollbar
        2) Topbar
        3) Notification
        4) Profile Expand
        5) Sidebar
        6) Theme Switch

*/
/*
    --------------------------------------------------
    Mixins for the base stylesheet.
    Here will import all the mixins from the functions file.
    --------------------------------------------------    
*/
/*
    -------------------------------------------------- 
    Break points variables
    --------------------------------------------------
*/
/*
    -------------------------------------------------- 
    Colors variables
    --------------------------------------------------
*/
/* Primary Blue */
/* Secondary Cyan  */
/* Success */
/* Danger  */
/* Warning */
/* Accent */
/*
    Media Queries mixin for the theme

    --------------------------------------------------
    Break points variables
    --------------------------------------------------
    @break-xs: 576px; // Extra Small Devices, Phones
    @break-sm: 576px; // Small Devices, Tablets
    @break-md: 768px; // Medium Devices, Desktops
    @break-lg: 992px; // Large Devices, Laptops, Desktops
    @break-xl: 1200px; // Extra Large Devices, Wide Laptops, Desktops
    @break-xxl: 1400px; // Extra Extra Large Devices, Wide Desktops
    --------------------------------------------------

*/
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Lexend:wght@300;400;500;600;700&family=Poppins:ital,wght@0,300;0,400;0,500;0,600;0,700;1,300;1,400;1,500;1,600;1,700&display=swap");
.dropdown-text-light {
  color: #6f767e;
}

.dropdown-text-dark {
  color: #1a1d1f;
}

.dropdown-text-red {
  color: #ff0022;
}

.dropdown-text-disable {
  color: #b2bec3;
}

.dropdown-text-blue {
  color: #5669ff;
}

.dropdown-content {
  margin-top: 24px;
  margin-left: 32px;
}

.dropdown-section {
  padding: 80px;
  background: #fcfcfc;
  margin-top: 8px;
}
.dropdown-section h1 {
  font-weight: 700;
  font-size: 32px;
  text-transform: uppercase;
  color: #120d26;
}
.dropdown-section p {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #4b4b4b;
}

.button-title {
  font-weight: 600;
  font-size: 14px;
  text-transform: uppercase;
  color: #120d26;
}

.dropdown-with-down-arrow,
.dropdown2-with-down-arrow {
  margin-top: 12px;
  background-color: #fcfcfc;
  font-weight: 500;
  font-size: 16px;
  padding: 8px 16px;
  letter-spacing: 0.02em;
  color: #1a1d1f;
  border: 2px solid #f0eeee;
  border-radius: 5px;
}
.dropdown-with-down-arrow i,
.dropdown2-with-down-arrow i {
  margin-left: 12px;
  font-weight: bold;
}
.dropdown-with-down-arrow:hover,
.dropdown2-with-down-arrow:hover {
  border: 2px solid #5669ff;
  color: #5669ff;
}
.dropdown-with-down-arrow:hover i,
.dropdown2-with-down-arrow:hover i {
  color: #5669ff;
}
.dropdown-with-down-arrow:focus,
.dropdown2-with-down-arrow:focus {
  border: 2px solid #4d5ee5;
  color: #066ecf;
}
.dropdown-with-down-arrow:focus i,
.dropdown2-with-down-arrow:focus i {
  color: #066ecf;
}

.dropdwon-btn-section {
  margin-top: 40px;
}

.dropdown-with-dots {
  margin-top: 12px;
}

button {
  border: none;
  outline: none;
}

.dropdown-menu {
  margin-top: 10px !important;
}

#dropdown {
  background-color: #fcfcfc;
  border: 2px solid #f0eeee;
  border-radius: 5px 0px 0px 5px;
  font-weight: 500;
  font-size: 16px;
  padding: 8px 16px;
  letter-spacing: 0.02em;
  color: #1a1d1f;
}
#dropdown:hover {
  border: 2px solid #5669ff;
  color: #5669ff;
}
#dropdown:focus {
  border: 2px solid #4d5ee5;
  color: #066ecf;
}

#three-dots {
  background-color: #fcfcfc;
  border: 2px solid #f0eeee;
  border-radius: 0px 5px 5px 0px;
  padding: 8px;
  letter-spacing: 0.02em;
  color: #1a1d1f;
}
#three-dots:hover {
  border: 2px solid #5669ff;
  color: #5669ff;
}
#three-dots:focus {
  border: 2px solid #4d5ee5;
  color: #066ecf;
}

.dropdown-items,
.third-item,
.second-item {
  position: relative;
  width: 191px;
  background: #ffffff;
  border: 1px solid #f0eeee;
  border-radius: 7px;
}
.dropdown-items ul,
.third-item ul,
.second-item ul {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  padding: 16px;
  padding-right: 0px;
  gap: 16px;
}
.dropdown-items ul .text-secondary > i,
.third-item ul .text-secondary > i,
.second-item ul .text-secondary > i {
  padding-right: 16px;
}
.dropdown-items ul li,
.third-item ul li,
.second-item ul li {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  padding: 8px;
  padding-right: 24px;
}
.dropdown-items ul .dropdown-text-dark > i,
.third-item ul .dropdown-text-dark > i,
.second-item ul .dropdown-text-dark > i {
  padding-left: 16px;
}

.second-item {
  display: block;
  width: 180px;
  position: absolute;
  opacity: 0;
  visibility: hidden;
  left: 100%;
  margin-top: -40px;
  transition: all ease-in-out 0.4s;
}

.third-item {
  display: none;
  width: 180px;
  position: absolute;
  left: 112%;
  margin-top: -66px;
}

ul li.dropdown-text-dark:hover .second-item {
  visibility: visible;
  opacity: 1;
  transition-delay: 0s;
  margin-left: 10px;
}

.dropdown-search-checkbox {
  margin-top: 12px;
  background-color: #fcfcfc;
  border: 2px solid #f0eeee;
  border-radius: 50px;
  font-weight: 500;
  font-size: 16px;
  letter-spacing: 0.02em;
  padding: 8px 16px;
  color: #5669ff;
}

.search-container {
  margin-top: 12px;
  width: 272px;
  background: #ffffff;
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.05);
  border-radius: 7px;
}

.search-input-checkbox input {
  width: 20px !important;
  height: 20px;
  border: 1px solid #eaeaea;
  border-radius: 5px;
}
.search-input-checkbox label {
  color: #6f767e;
  margin-left: 12px;
}
.search-input-checkbox ul li {
  display: flex;
}

.search-input {
  position: relative;
  text-align: center;
  padding: 15px 16px 15px 16px;
  border-bottom: 2px solid #f0eeee;
}
.search-input input {
  padding: 12px;
  width: 100%;
  border: 2px solid #f0eeee;
  border-radius: 50px;
  background: #fafafa;
  outline: none;
}
.search-input ::-moz-placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}
.search-input :-ms-input-placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}
.search-input ::placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}
.search-input i {
  position: absolute;
  top: 29px;
  right: 27px;
  padding: 5px;
  background-color: #fafafa;
  color: #6f767e;
}

.search-items {
  padding: 17px 25px;
}
.search-items ul {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  gap: 16px;
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
}
.search-items ul li {
  color: #6f767e;
}

.search-input ul {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  padding: 8px 16px;
  gap: 16px;
}
.search-input ul li {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #6f767e;
}
.search-input ul li input {
  width: auto;
}
.search-input ul li ::-moz-placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}
.search-input ul li :-ms-input-placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}
.search-input ul li ::placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}

.search-container .btn-items {
  padding: 5px 17px 25px;
  text-align: end;
}
.search-container .btn-items .btn {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
}
.search-container .btn-items .btn.clear {
  color: #6f767e;
}

.input-default input,
.input-date input {
  padding: 16px;
  width: 336px;
  height: 48px;
  border: 2px solid #f0eeee;
  border-radius: 5px;
  outline: none;
}
.input-default ::-moz-placeholder,
.input-date ::-moz-placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}
.input-default :-ms-input-placeholder,
.input-date :-ms-input-placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}
.input-default ::placeholder,
.input-date ::placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}

.input-field-focus {
  width: 360px;
}
.input-field-focus input {
  width: 100%;
  height: 48px;
  padding: 16px;
  border: 2px solid #f0eeee;
  border-radius: 5px;
  outline: none;
}
.input-field-focus input:focus-visible {
  box-shadow: 0px 0px 10px rgba(10, 175, 255, 0.35);
  border: 2px solid #0010f7;
  outline: none;
}
.input-field-focus ::-moz-placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}
.input-field-focus :-ms-input-placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}
.input-field-focus ::placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}

.input-with-icon {
  position: relative;
  margin-top: 16px;
  width: 336px;
  height: 48px;
}
.input-with-icon input {
  width: 336px;
  height: 48px;
  padding: 16px 45px;
  border: 2px solid #f0eeee;
  border-radius: 5px;
  outline: none;
}
.input-with-icon input:focus-visible {
  box-shadow: 0px 0px 10px rgba(10, 175, 255, 0.35);
  border: 2px solid #0010f7;
  outline: none;
}
.input-with-icon ::-moz-placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}
.input-with-icon :-ms-input-placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}
.input-with-icon ::placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}
.input-with-icon i {
  position: absolute;
  top: 16px;
  color: #6f767e;
}
.input-with-icon i.fa-user-o {
  left: 20px;
}
.input-with-icon i.fa-search {
  right: 20px;
}

.input-pre-post {
  position: relative;
  margin-top: 16px;
  width: 296px;
  height: 48px;
}
.input-pre-post input {
  padding: 16px 45px;
  width: 296px;
  height: 48px;
  border: 2px solid #f0eeee;
  border-radius: 5px;
  outline: none;
}
.input-pre-post ::-moz-placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}
.input-pre-post :-ms-input-placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}
.input-pre-post ::placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}
.input-pre-post i {
  position: absolute;
  top: 16px;
  color: #6f767e;
}
.input-pre-post i.fa-user-o {
  left: 20px;
}
.input-pre-post i.fa-search {
  right: 60px;
}
.input-pre-post img {
  position: absolute;
  top: 2px;
  right: 1px;
  padding: 15px;
  width: 47px;
  background-color: #f7f7f7;
  border-left: 2px solid #f0eeee;
  border-radius: 0px 5px 5px 0px;
}

.input-https-post {
  position: relative;
  margin-top: 16px;
  width: 296px;
  height: 48px;
}
.input-https-post input {
  padding: 16px 43px 16px 110px;
  width: 296px;
  height: 48px;
  border: 2px solid #f0eeee;
  border-radius: 5px;
  outline: none;
}
.input-https-post ::-moz-placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}
.input-https-post :-ms-input-placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}
.input-https-post ::placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}
.input-https-post i {
  position: absolute;
  top: 16px;
  color: #6f767e;
}
.input-https-post i.fa-user-o {
  left: 88px;
}
.input-https-post img {
  position: absolute;
  top: 2px;
  right: 1px;
  padding: 15px;
  width: 47px;
  background-color: #f7f7f7;
  border-left: 2px solid #f0eeee;
  border-radius: 0px 5px 5px 0px;
}
.input-https-post .https {
  position: absolute;
  color: #1a1d1f;
  top: 2px;
  left: 1.5px;
  background-color: #f7f7f7;
  padding: 12px;
  border-right: 2px solid #f0eeee;
  border-radius: 5px 0px 0px 5px;
}

.input-https-post2 {
  position: relative;
  margin-top: 16px;
  width: 296px;
  height: 48px;
}
.input-https-post2 input {
  padding: 16px 43px 16px 86px;
  width: 296px;
  height: 48px;
  border: 2px solid #f0eeee;
  border-radius: 5px;
  outline: none;
}
.input-https-post2 ::-moz-placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}
.input-https-post2 :-ms-input-placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}
.input-https-post2 ::placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}
.input-https-post2 i {
  position: absolute;
  top: 16px;
  color: #6f767e;
}
.input-https-post2 i.fa-user-o {
  left: 88px;
}
.input-https-post2 i.fa-search {
  right: 20px;
}
.input-https-post2 .https {
  position: absolute;
  color: #1a1d1f;
  top: 2px;
  left: 1.5px;
  background-color: #f7f7f7;
  padding: 12px;
  border-right: 2px solid #f0eeee;
  border-radius: 5px 0px 0px 5px;
}

textarea {
  width: 280px;
  height: 79px;
  margin-top: 16px;
  padding: 12px 16px;
  background: #ffffff;
  border: 1px solid #eaeaea;
  border-radius: 7px;
  outline: none;
}

textarea::-moz-placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}

textarea:-ms-input-placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}

textarea::placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}

.text-area {
  width: 280px;
  height: 79px;
}
.text-area .text-count {
  font-family: "Lexend";
  font-size: 10px;
  font-weight: 500;
  text-align: end;
  color: #b2bec3;
}

.Input-search-tab {
  position: relative;
  margin-top: 16px;
  width: 273px;
  height: 48px;
}
.Input-search-tab input {
  padding: 16px 43px 16px 16px;
  width: 100%;
  height: 100%;
  border: 2px solid #f0eeee;
  border-radius: 5px;
  outline: none;
}
.Input-search-tab ::-moz-placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}
.Input-search-tab :-ms-input-placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}
.Input-search-tab ::placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}
.Input-search-tab i {
  position: absolute;
  top: 0;
  color: #6f767e;
  border-left: 2px solid #f0eeee;
  padding: 16px;
}
.Input-search-tab i.fa-search {
  right: 0px;
}
.Input-search-tab .search {
  position: absolute;
  color: #ffffff;
  top: 0px;
  right: 0px;
  background-color: #5669ff;
  padding: 13px;
  border-radius: 0px 5px 5px 0px;
}
.Input-search-tab i.fa-microphone {
  right: 70px;
  color: #5669ff;
  border: none;
}

.Input-search-tab.search-color > input {
  width: 289px;
}

.Input-search-tab.search-color > i {
  right: -15px;
  color: #fafafa;
  background-color: #5669ff;
  border-radius: 0px 5px 5px 0px;
}

.Input-search-tab .search-tab1 {
  width: 237px;
}

.Input-search-tab.microphone > input {
  padding: 16px 110px 16px 16px;
}

.input-date {
  margin-top: 16px;
}
.input-date input {
  width: 272px;
}

.Range-date {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  width: 272px;
  height: 48px;
  flex-direction: row;
  border: 2px solid #f0eeee;
  border-radius: 5px;
  margin-top: 16px;
  padding: 10px 16px;
  gap: 5px;
}
.Range-date input {
  border: none;
  outline: none;
}

.time-field {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  flex-direction: row;
  width: 386px;
  gap: 5px;
  margin-top: 16px;
}
.time-field .input-time {
  border: 2px solid #f0eeee;
  border-radius: 5px;
  width: 107px;
  height: 50px;
}
.time-field .input-time select {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  color: #1a1d1f;
  border: none;
  outline: none;
  padding: 11px 16px;
}
.time-field .select-time input {
  border: 2px solid #f0eeee;
  border-radius: 5px;
  width: 272px;
  height: 48px;
  outline: none;
  padding: 16px;
}

.input-group-start-end-time {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  justify-content: flex-end;
  flex-direction: row;
  gap: 10px;
}
.input-group-start-end-time .input-start-end-time {
  border: 2px solid #f0eeee;
  border-radius: 5px;
  width: 169px;
  height: 44px;
  padding: 12px 16px;
  color: #6f767e;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
}
.input-group-start-end-time .input-start-end-time span {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
}

option:hover {
  color: #5669ff;
}

.input-time-select {
  display: flex;
  justify-content: end;
  align-items: center;
  margin-top: 16px;
}
.input-time-select .input-start-end-time-select {
  border: 2px solid #f0eeee;
  border-radius: 5px;
  width: 178px;
  height: 60px;
  padding: 12px 16px;
  color: #6f767e;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
}
.input-time-select .input-start-end-time-select p {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 500;
  font-size: 12px;
  color: #0010f7;
}
.input-time-select .input-start-end-time-select h6 {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 700;
  font-size: 16px;
  color: #1a1d1f;
}

/*
    --------------------------------------------------
    Functions for the base stylesheet.
    Here will import all the functions from the functions file.
    --------------------------------------------------    
*/
.pa-0 {
  padding: 0px !important;
}

.pl-0 {
  padding-left: 0px !important;
}

.pt-0 {
  padding-top: 0px !important;
}

.pr-0 {
  padding-right: 0px !important;
}

.pb-0 {
  padding-bottom: 0px !important;
}

.pv-0 {
  padding-top: 0px !important;
  padding-bottom: 0px !important;
}

.ph-0 {
  padding-left: 0px !important;
  padding-right: 0px !important;
}

.pa-2 {
  padding: 2px !important;
}

.pl-2 {
  padding-left: 2px !important;
}

.pt-2 {
  padding-top: 2px !important;
}

.pr-2 {
  padding-right: 2px !important;
}

.pb-2 {
  padding-bottom: 2px !important;
}

.pv-2 {
  padding-top: 2px !important;
  padding-bottom: 2px !important;
}

.ph-2 {
  padding-left: 2px !important;
  padding-right: 2px !important;
}

.pa-4 {
  padding: 4px !important;
}

.pl-4 {
  padding-left: 4px !important;
}

.pt-4 {
  padding-top: 4px !important;
}

.pr-4 {
  padding-right: 4px !important;
}

.pb-4 {
  padding-bottom: 4px !important;
}

.pv-4 {
  padding-top: 4px !important;
  padding-bottom: 4px !important;
}

.ph-4 {
  padding-left: 4px !important;
  padding-right: 4px !important;
}

.pa-6 {
  padding: 6px !important;
}

.pl-6 {
  padding-left: 6px !important;
}

.pt-6 {
  padding-top: 6px !important;
}

.pr-6 {
  padding-right: 6px !important;
}

.pb-6 {
  padding-bottom: 6px !important;
}

.pv-6 {
  padding-top: 6px !important;
  padding-bottom: 6px !important;
}

.ph-6 {
  padding-left: 6px !important;
  padding-right: 6px !important;
}

.pa-8 {
  padding: 8px !important;
}

.pl-8 {
  padding-left: 8px !important;
}

.pt-8 {
  padding-top: 8px !important;
}

.pr-8 {
  padding-right: 8px !important;
}

.pb-8 {
  padding-bottom: 8px !important;
}

.pv-8 {
  padding-top: 8px !important;
  padding-bottom: 8px !important;
}

.ph-8 {
  padding-left: 8px !important;
  padding-right: 8px !important;
}

.pa-10 {
  padding: 10px !important;
}

.pl-10 {
  padding-left: 10px !important;
}

.pt-10 {
  padding-top: 10px !important;
}

.pr-10 {
  padding-right: 10px !important;
}

.pb-10 {
  padding-bottom: 10px !important;
}

.pv-10 {
  padding-top: 10px !important;
  padding-bottom: 10px !important;
}

.ph-10 {
  padding-left: 10px !important;
  padding-right: 10px !important;
}

.pa-12 {
  padding: 12px !important;
}

.pl-12 {
  padding-left: 12px !important;
}

.pt-12 {
  padding-top: 12px !important;
}

.pr-12 {
  padding-right: 12px !important;
}

.pb-12 {
  padding-bottom: 12px !important;
}

.pv-12 {
  padding-top: 12px !important;
  padding-bottom: 12px !important;
}

.ph-12 {
  padding-left: 12px !important;
  padding-right: 12px !important;
}

.pa-14 {
  padding: 14px !important;
}

.pl-14 {
  padding-left: 14px !important;
}

.pt-14 {
  padding-top: 14px !important;
}

.pr-14 {
  padding-right: 14px !important;
}

.pb-14 {
  padding-bottom: 14px !important;
}

.pv-14 {
  padding-top: 14px !important;
  padding-bottom: 14px !important;
}

.ph-14 {
  padding-left: 14px !important;
  padding-right: 14px !important;
}

.pa-16 {
  padding: 16px !important;
}

.pl-16 {
  padding-left: 16px !important;
}

.pt-16 {
  padding-top: 16px !important;
}

.pr-16 {
  padding-right: 16px !important;
}

.pb-16 {
  padding-bottom: 16px !important;
}

.pv-16 {
  padding-top: 16px !important;
  padding-bottom: 16px !important;
}

.ph-16 {
  padding-left: 16px !important;
  padding-right: 16px !important;
}

.pa-18 {
  padding: 18px !important;
}

.pl-18 {
  padding-left: 18px !important;
}

.pt-18 {
  padding-top: 18px !important;
}

.pr-18 {
  padding-right: 18px !important;
}

.pb-18 {
  padding-bottom: 18px !important;
}

.pv-18 {
  padding-top: 18px !important;
  padding-bottom: 18px !important;
}

.ph-18 {
  padding-left: 18px !important;
  padding-right: 18px !important;
}

.pa-20 {
  padding: 20px !important;
}

.pl-20 {
  padding-left: 20px !important;
}

.pt-20 {
  padding-top: 20px !important;
}

.pr-20 {
  padding-right: 20px !important;
}

.pb-20 {
  padding-bottom: 20px !important;
}

.pv-20 {
  padding-top: 20px !important;
  padding-bottom: 20px !important;
}

.ph-20 {
  padding-left: 20px !important;
  padding-right: 20px !important;
}

.pa-22 {
  padding: 22px !important;
}

.pl-22 {
  padding-left: 22px !important;
}

.pt-22 {
  padding-top: 22px !important;
}

.pr-22 {
  padding-right: 22px !important;
}

.pb-22 {
  padding-bottom: 22px !important;
}

.pv-22 {
  padding-top: 22px !important;
  padding-bottom: 22px !important;
}

.ph-22 {
  padding-left: 22px !important;
  padding-right: 22px !important;
}

.pa-24 {
  padding: 24px !important;
}

.pl-24 {
  padding-left: 24px !important;
}

.pt-24 {
  padding-top: 24px !important;
}

.pr-24 {
  padding-right: 24px !important;
}

.pb-24 {
  padding-bottom: 24px !important;
}

.pv-24 {
  padding-top: 24px !important;
  padding-bottom: 24px !important;
}

.ph-24 {
  padding-left: 24px !important;
  padding-right: 24px !important;
}

.pa-26 {
  padding: 26px !important;
}

.pl-26 {
  padding-left: 26px !important;
}

.pt-26 {
  padding-top: 26px !important;
}

.pr-26 {
  padding-right: 26px !important;
}

.pb-26 {
  padding-bottom: 26px !important;
}

.pv-26 {
  padding-top: 26px !important;
  padding-bottom: 26px !important;
}

.ph-26 {
  padding-left: 26px !important;
  padding-right: 26px !important;
}

.pa-28 {
  padding: 28px !important;
}

.pl-28 {
  padding-left: 28px !important;
}

.pt-28 {
  padding-top: 28px !important;
}

.pr-28 {
  padding-right: 28px !important;
}

.pb-28 {
  padding-bottom: 28px !important;
}

.pv-28 {
  padding-top: 28px !important;
  padding-bottom: 28px !important;
}

.ph-28 {
  padding-left: 28px !important;
  padding-right: 28px !important;
}

.pa-30 {
  padding: 30px !important;
}

.pl-30 {
  padding-left: 30px !important;
}

.pt-30 {
  padding-top: 30px !important;
}

.pr-30 {
  padding-right: 30px !important;
}

.pb-30 {
  padding-bottom: 30px !important;
}

.pv-30 {
  padding-top: 30px !important;
  padding-bottom: 30px !important;
}

.ph-30 {
  padding-left: 30px !important;
  padding-right: 30px !important;
}

.pa-32 {
  padding: 32px !important;
}

.pl-32 {
  padding-left: 32px !important;
}

.pt-32 {
  padding-top: 32px !important;
}

.pr-32 {
  padding-right: 32px !important;
}

.pb-32 {
  padding-bottom: 32px !important;
}

.pv-32 {
  padding-top: 32px !important;
  padding-bottom: 32px !important;
}

.ph-32 {
  padding-left: 32px !important;
  padding-right: 32px !important;
}

.pa-34 {
  padding: 34px !important;
}

.pl-34 {
  padding-left: 34px !important;
}

.pt-34 {
  padding-top: 34px !important;
}

.pr-34 {
  padding-right: 34px !important;
}

.pb-34 {
  padding-bottom: 34px !important;
}

.pv-34 {
  padding-top: 34px !important;
  padding-bottom: 34px !important;
}

.ph-34 {
  padding-left: 34px !important;
  padding-right: 34px !important;
}

.pa-36 {
  padding: 36px !important;
}

.pl-36 {
  padding-left: 36px !important;
}

.pt-36 {
  padding-top: 36px !important;
}

.pr-36 {
  padding-right: 36px !important;
}

.pb-36 {
  padding-bottom: 36px !important;
}

.pv-36 {
  padding-top: 36px !important;
  padding-bottom: 36px !important;
}

.ph-36 {
  padding-left: 36px !important;
  padding-right: 36px !important;
}

.pa-38 {
  padding: 38px !important;
}

.pl-38 {
  padding-left: 38px !important;
}

.pt-38 {
  padding-top: 38px !important;
}

.pr-38 {
  padding-right: 38px !important;
}

.pb-38 {
  padding-bottom: 38px !important;
}

.pv-38 {
  padding-top: 38px !important;
  padding-bottom: 38px !important;
}

.ph-38 {
  padding-left: 38px !important;
  padding-right: 38px !important;
}

.pa-40 {
  padding: 40px !important;
}

.pl-40 {
  padding-left: 40px !important;
}

.pt-40 {
  padding-top: 40px !important;
}

.pr-40 {
  padding-right: 40px !important;
}

.pb-40 {
  padding-bottom: 40px !important;
}

.pv-40 {
  padding-top: 40px !important;
  padding-bottom: 40px !important;
}

.ph-40 {
  padding-left: 40px !important;
  padding-right: 40px !important;
}

.pa-42 {
  padding: 42px !important;
}

.pl-42 {
  padding-left: 42px !important;
}

.pt-42 {
  padding-top: 42px !important;
}

.pr-42 {
  padding-right: 42px !important;
}

.pb-42 {
  padding-bottom: 42px !important;
}

.pv-42 {
  padding-top: 42px !important;
  padding-bottom: 42px !important;
}

.ph-42 {
  padding-left: 42px !important;
  padding-right: 42px !important;
}

.pa-44 {
  padding: 44px !important;
}

.pl-44 {
  padding-left: 44px !important;
}

.pt-44 {
  padding-top: 44px !important;
}

.pr-44 {
  padding-right: 44px !important;
}

.pb-44 {
  padding-bottom: 44px !important;
}

.pv-44 {
  padding-top: 44px !important;
  padding-bottom: 44px !important;
}

.ph-44 {
  padding-left: 44px !important;
  padding-right: 44px !important;
}

.pa-46 {
  padding: 46px !important;
}

.pl-46 {
  padding-left: 46px !important;
}

.pt-46 {
  padding-top: 46px !important;
}

.pr-46 {
  padding-right: 46px !important;
}

.pb-46 {
  padding-bottom: 46px !important;
}

.pv-46 {
  padding-top: 46px !important;
  padding-bottom: 46px !important;
}

.ph-46 {
  padding-left: 46px !important;
  padding-right: 46px !important;
}

.pa-48 {
  padding: 48px !important;
}

.pl-48 {
  padding-left: 48px !important;
}

.pt-48 {
  padding-top: 48px !important;
}

.pr-48 {
  padding-right: 48px !important;
}

.pb-48 {
  padding-bottom: 48px !important;
}

.pv-48 {
  padding-top: 48px !important;
  padding-bottom: 48px !important;
}

.ph-48 {
  padding-left: 48px !important;
  padding-right: 48px !important;
}

.pa-50 {
  padding: 50px !important;
}

.pl-50 {
  padding-left: 50px !important;
}

.pt-50 {
  padding-top: 50px !important;
}

.pr-50 {
  padding-right: 50px !important;
}

.pb-50 {
  padding-bottom: 50px !important;
}

.pv-50 {
  padding-top: 50px !important;
  padding-bottom: 50px !important;
}

.ph-50 {
  padding-left: 50px !important;
  padding-right: 50px !important;
}

.pa-52 {
  padding: 52px !important;
}

.pl-52 {
  padding-left: 52px !important;
}

.pt-52 {
  padding-top: 52px !important;
}

.pr-52 {
  padding-right: 52px !important;
}

.pb-52 {
  padding-bottom: 52px !important;
}

.pv-52 {
  padding-top: 52px !important;
  padding-bottom: 52px !important;
}

.ph-52 {
  padding-left: 52px !important;
  padding-right: 52px !important;
}

.pa-54 {
  padding: 54px !important;
}

.pl-54 {
  padding-left: 54px !important;
}

.pt-54 {
  padding-top: 54px !important;
}

.pr-54 {
  padding-right: 54px !important;
}

.pb-54 {
  padding-bottom: 54px !important;
}

.pv-54 {
  padding-top: 54px !important;
  padding-bottom: 54px !important;
}

.ph-54 {
  padding-left: 54px !important;
  padding-right: 54px !important;
}

.pa-56 {
  padding: 56px !important;
}

.pl-56 {
  padding-left: 56px !important;
}

.pt-56 {
  padding-top: 56px !important;
}

.pr-56 {
  padding-right: 56px !important;
}

.pb-56 {
  padding-bottom: 56px !important;
}

.pv-56 {
  padding-top: 56px !important;
  padding-bottom: 56px !important;
}

.ph-56 {
  padding-left: 56px !important;
  padding-right: 56px !important;
}

.pa-58 {
  padding: 58px !important;
}

.pl-58 {
  padding-left: 58px !important;
}

.pt-58 {
  padding-top: 58px !important;
}

.pr-58 {
  padding-right: 58px !important;
}

.pb-58 {
  padding-bottom: 58px !important;
}

.pv-58 {
  padding-top: 58px !important;
  padding-bottom: 58px !important;
}

.ph-58 {
  padding-left: 58px !important;
  padding-right: 58px !important;
}

.pa-60 {
  padding: 60px !important;
}

.pl-60 {
  padding-left: 60px !important;
}

.pt-60 {
  padding-top: 60px !important;
}

.pr-60 {
  padding-right: 60px !important;
}

.pb-60 {
  padding-bottom: 60px !important;
}

.pv-60 {
  padding-top: 60px !important;
  padding-bottom: 60px !important;
}

.ph-60 {
  padding-left: 60px !important;
  padding-right: 60px !important;
}

.pa-62 {
  padding: 62px !important;
}

.pl-62 {
  padding-left: 62px !important;
}

.pt-62 {
  padding-top: 62px !important;
}

.pr-62 {
  padding-right: 62px !important;
}

.pb-62 {
  padding-bottom: 62px !important;
}

.pv-62 {
  padding-top: 62px !important;
  padding-bottom: 62px !important;
}

.ph-62 {
  padding-left: 62px !important;
  padding-right: 62px !important;
}

.pa-64 {
  padding: 64px !important;
}

.pl-64 {
  padding-left: 64px !important;
}

.pt-64 {
  padding-top: 64px !important;
}

.pr-64 {
  padding-right: 64px !important;
}

.pb-64 {
  padding-bottom: 64px !important;
}

.pv-64 {
  padding-top: 64px !important;
  padding-bottom: 64px !important;
}

.ph-64 {
  padding-left: 64px !important;
  padding-right: 64px !important;
}

.pa-66 {
  padding: 66px !important;
}

.pl-66 {
  padding-left: 66px !important;
}

.pt-66 {
  padding-top: 66px !important;
}

.pr-66 {
  padding-right: 66px !important;
}

.pb-66 {
  padding-bottom: 66px !important;
}

.pv-66 {
  padding-top: 66px !important;
  padding-bottom: 66px !important;
}

.ph-66 {
  padding-left: 66px !important;
  padding-right: 66px !important;
}

.pa-68 {
  padding: 68px !important;
}

.pl-68 {
  padding-left: 68px !important;
}

.pt-68 {
  padding-top: 68px !important;
}

.pr-68 {
  padding-right: 68px !important;
}

.pb-68 {
  padding-bottom: 68px !important;
}

.pv-68 {
  padding-top: 68px !important;
  padding-bottom: 68px !important;
}

.ph-68 {
  padding-left: 68px !important;
  padding-right: 68px !important;
}

.pa-70 {
  padding: 70px !important;
}

.pl-70 {
  padding-left: 70px !important;
}

.pt-70 {
  padding-top: 70px !important;
}

.pr-70 {
  padding-right: 70px !important;
}

.pb-70 {
  padding-bottom: 70px !important;
}

.pv-70 {
  padding-top: 70px !important;
  padding-bottom: 70px !important;
}

.ph-70 {
  padding-left: 70px !important;
  padding-right: 70px !important;
}

.pa-72 {
  padding: 72px !important;
}

.pl-72 {
  padding-left: 72px !important;
}

.pt-72 {
  padding-top: 72px !important;
}

.pr-72 {
  padding-right: 72px !important;
}

.pb-72 {
  padding-bottom: 72px !important;
}

.pv-72 {
  padding-top: 72px !important;
  padding-bottom: 72px !important;
}

.ph-72 {
  padding-left: 72px !important;
  padding-right: 72px !important;
}

.pa-74 {
  padding: 74px !important;
}

.pl-74 {
  padding-left: 74px !important;
}

.pt-74 {
  padding-top: 74px !important;
}

.pr-74 {
  padding-right: 74px !important;
}

.pb-74 {
  padding-bottom: 74px !important;
}

.pv-74 {
  padding-top: 74px !important;
  padding-bottom: 74px !important;
}

.ph-74 {
  padding-left: 74px !important;
  padding-right: 74px !important;
}

.pa-76 {
  padding: 76px !important;
}

.pl-76 {
  padding-left: 76px !important;
}

.pt-76 {
  padding-top: 76px !important;
}

.pr-76 {
  padding-right: 76px !important;
}

.pb-76 {
  padding-bottom: 76px !important;
}

.pv-76 {
  padding-top: 76px !important;
  padding-bottom: 76px !important;
}

.ph-76 {
  padding-left: 76px !important;
  padding-right: 76px !important;
}

.pa-78 {
  padding: 78px !important;
}

.pl-78 {
  padding-left: 78px !important;
}

.pt-78 {
  padding-top: 78px !important;
}

.pr-78 {
  padding-right: 78px !important;
}

.pb-78 {
  padding-bottom: 78px !important;
}

.pv-78 {
  padding-top: 78px !important;
  padding-bottom: 78px !important;
}

.ph-78 {
  padding-left: 78px !important;
  padding-right: 78px !important;
}

.pa-80 {
  padding: 80px !important;
}

.pl-80 {
  padding-left: 80px !important;
}

.pt-80 {
  padding-top: 80px !important;
}

.pr-80 {
  padding-right: 80px !important;
}

.pb-80 {
  padding-bottom: 80px !important;
}

.pv-80 {
  padding-top: 80px !important;
  padding-bottom: 80px !important;
}

.ph-80 {
  padding-left: 80px !important;
  padding-right: 80px !important;
}

.pa-82 {
  padding: 82px !important;
}

.pl-82 {
  padding-left: 82px !important;
}

.pt-82 {
  padding-top: 82px !important;
}

.pr-82 {
  padding-right: 82px !important;
}

.pb-82 {
  padding-bottom: 82px !important;
}

.pv-82 {
  padding-top: 82px !important;
  padding-bottom: 82px !important;
}

.ph-82 {
  padding-left: 82px !important;
  padding-right: 82px !important;
}

.pa-84 {
  padding: 84px !important;
}

.pl-84 {
  padding-left: 84px !important;
}

.pt-84 {
  padding-top: 84px !important;
}

.pr-84 {
  padding-right: 84px !important;
}

.pb-84 {
  padding-bottom: 84px !important;
}

.pv-84 {
  padding-top: 84px !important;
  padding-bottom: 84px !important;
}

.ph-84 {
  padding-left: 84px !important;
  padding-right: 84px !important;
}

.pa-86 {
  padding: 86px !important;
}

.pl-86 {
  padding-left: 86px !important;
}

.pt-86 {
  padding-top: 86px !important;
}

.pr-86 {
  padding-right: 86px !important;
}

.pb-86 {
  padding-bottom: 86px !important;
}

.pv-86 {
  padding-top: 86px !important;
  padding-bottom: 86px !important;
}

.ph-86 {
  padding-left: 86px !important;
  padding-right: 86px !important;
}

.pa-88 {
  padding: 88px !important;
}

.pl-88 {
  padding-left: 88px !important;
}

.pt-88 {
  padding-top: 88px !important;
}

.pr-88 {
  padding-right: 88px !important;
}

.pb-88 {
  padding-bottom: 88px !important;
}

.pv-88 {
  padding-top: 88px !important;
  padding-bottom: 88px !important;
}

.ph-88 {
  padding-left: 88px !important;
  padding-right: 88px !important;
}

.pa-90 {
  padding: 90px !important;
}

.pl-90 {
  padding-left: 90px !important;
}

.pt-90 {
  padding-top: 90px !important;
}

.pr-90 {
  padding-right: 90px !important;
}

.pb-90 {
  padding-bottom: 90px !important;
}

.pv-90 {
  padding-top: 90px !important;
  padding-bottom: 90px !important;
}

.ph-90 {
  padding-left: 90px !important;
  padding-right: 90px !important;
}

.pa-92 {
  padding: 92px !important;
}

.pl-92 {
  padding-left: 92px !important;
}

.pt-92 {
  padding-top: 92px !important;
}

.pr-92 {
  padding-right: 92px !important;
}

.pb-92 {
  padding-bottom: 92px !important;
}

.pv-92 {
  padding-top: 92px !important;
  padding-bottom: 92px !important;
}

.ph-92 {
  padding-left: 92px !important;
  padding-right: 92px !important;
}

.pa-94 {
  padding: 94px !important;
}

.pl-94 {
  padding-left: 94px !important;
}

.pt-94 {
  padding-top: 94px !important;
}

.pr-94 {
  padding-right: 94px !important;
}

.pb-94 {
  padding-bottom: 94px !important;
}

.pv-94 {
  padding-top: 94px !important;
  padding-bottom: 94px !important;
}

.ph-94 {
  padding-left: 94px !important;
  padding-right: 94px !important;
}

.pa-96 {
  padding: 96px !important;
}

.pl-96 {
  padding-left: 96px !important;
}

.pt-96 {
  padding-top: 96px !important;
}

.pr-96 {
  padding-right: 96px !important;
}

.pb-96 {
  padding-bottom: 96px !important;
}

.pv-96 {
  padding-top: 96px !important;
  padding-bottom: 96px !important;
}

.ph-96 {
  padding-left: 96px !important;
  padding-right: 96px !important;
}

.pa-98 {
  padding: 98px !important;
}

.pl-98 {
  padding-left: 98px !important;
}

.pt-98 {
  padding-top: 98px !important;
}

.pr-98 {
  padding-right: 98px !important;
}

.pb-98 {
  padding-bottom: 98px !important;
}

.pv-98 {
  padding-top: 98px !important;
  padding-bottom: 98px !important;
}

.ph-98 {
  padding-left: 98px !important;
  padding-right: 98px !important;
}

.pa-100 {
  padding: 100px !important;
}

.pl-100 {
  padding-left: 100px !important;
}

.pt-100 {
  padding-top: 100px !important;
}

.pr-100 {
  padding-right: 100px !important;
}

.pb-100 {
  padding-bottom: 100px !important;
}

.pv-100 {
  padding-top: 100px !important;
  padding-bottom: 100px !important;
}

.ph-100 {
  padding-left: 100px !important;
  padding-right: 100px !important;
}

.ma-0 {
  margin: 0px !important;
}

.ml-0 {
  margin-left: 0px !important;
}

.mt-0 {
  margin-top: 0px !important;
}

.mr-0 {
  margin-right: 0px !important;
}

.mb-0 {
  margin-bottom: 0px !important;
}

.mv-0 {
  margin-top: 0px !important;
  margin-bottom: 0px !important;
}

.mh-0 {
  margin-left: 0px !important;
  margin-right: 0px !important;
}

.ma-2 {
  margin: 2px !important;
}

.ml-2 {
  margin-left: 2px !important;
}

.mt-2 {
  margin-top: 2px !important;
}

.mr-2 {
  margin-right: 2px !important;
}

.mb-2 {
  margin-bottom: 2px !important;
}

.mv-2 {
  margin-top: 2px !important;
  margin-bottom: 2px !important;
}

.mh-2 {
  margin-left: 2px !important;
  margin-right: 2px !important;
}

.ma-4 {
  margin: 4px !important;
}

.ml-4 {
  margin-left: 4px !important;
}

.mt-4 {
  margin-top: 4px !important;
}

.mr-4 {
  margin-right: 4px !important;
}

.mb-4 {
  margin-bottom: 4px !important;
}

.mv-4 {
  margin-top: 4px !important;
  margin-bottom: 4px !important;
}

.mh-4 {
  margin-left: 4px !important;
  margin-right: 4px !important;
}

.ma-6 {
  margin: 6px !important;
}

.ml-6 {
  margin-left: 6px !important;
}

.mt-6 {
  margin-top: 6px !important;
}

.mr-6 {
  margin-right: 6px !important;
}

.mb-6 {
  margin-bottom: 6px !important;
}

.mv-6 {
  margin-top: 6px !important;
  margin-bottom: 6px !important;
}

.mh-6 {
  margin-left: 6px !important;
  margin-right: 6px !important;
}

.ma-8 {
  margin: 8px !important;
}

.ml-8 {
  margin-left: 8px !important;
}

.mt-8 {
  margin-top: 8px !important;
}

.mr-8 {
  margin-right: 8px !important;
}

.mb-8 {
  margin-bottom: 8px !important;
}

.mv-8 {
  margin-top: 8px !important;
  margin-bottom: 8px !important;
}

.mh-8 {
  margin-left: 8px !important;
  margin-right: 8px !important;
}

.ma-10 {
  margin: 10px !important;
}

.ml-10 {
  margin-left: 10px !important;
}

.mt-10 {
  margin-top: 10px !important;
}

.mr-10 {
  margin-right: 10px !important;
}

.mb-10 {
  margin-bottom: 10px !important;
}

.mv-10 {
  margin-top: 10px !important;
  margin-bottom: 10px !important;
}

.mh-10 {
  margin-left: 10px !important;
  margin-right: 10px !important;
}

.ma-12 {
  margin: 12px !important;
}

.ml-12 {
  margin-left: 12px !important;
}

.mt-12 {
  margin-top: 12px !important;
}

.mr-12 {
  margin-right: 12px !important;
}

.mb-12 {
  margin-bottom: 12px !important;
}

.mv-12 {
  margin-top: 12px !important;
  margin-bottom: 12px !important;
}

.mh-12 {
  margin-left: 12px !important;
  margin-right: 12px !important;
}

.ma-14 {
  margin: 14px !important;
}

.ml-14 {
  margin-left: 14px !important;
}

.mt-14 {
  margin-top: 14px !important;
}

.mr-14 {
  margin-right: 14px !important;
}

.mb-14 {
  margin-bottom: 14px !important;
}

.mv-14 {
  margin-top: 14px !important;
  margin-bottom: 14px !important;
}

.mh-14 {
  margin-left: 14px !important;
  margin-right: 14px !important;
}

.ma-16 {
  margin: 16px !important;
}

.ml-16 {
  margin-left: 16px !important;
}

.mt-16 {
  margin-top: 16px !important;
}

.mr-16 {
  margin-right: 16px !important;
}

.mb-16 {
  margin-bottom: 16px !important;
}

.mv-16 {
  margin-top: 16px !important;
  margin-bottom: 16px !important;
}

.mh-16 {
  margin-left: 16px !important;
  margin-right: 16px !important;
}

.ma-18 {
  margin: 18px !important;
}

.ml-18 {
  margin-left: 18px !important;
}

.mt-18 {
  margin-top: 18px !important;
}

.mr-18 {
  margin-right: 18px !important;
}

.mb-18 {
  margin-bottom: 18px !important;
}

.mv-18 {
  margin-top: 18px !important;
  margin-bottom: 18px !important;
}

.mh-18 {
  margin-left: 18px !important;
  margin-right: 18px !important;
}

.ma-20 {
  margin: 20px !important;
}

.ml-20 {
  margin-left: 20px !important;
}

.mt-20 {
  margin-top: 20px !important;
}

.mr-20 {
  margin-right: 20px !important;
}

.mb-20 {
  margin-bottom: 20px !important;
}

.mv-20 {
  margin-top: 20px !important;
  margin-bottom: 20px !important;
}

.mh-20 {
  margin-left: 20px !important;
  margin-right: 20px !important;
}

.ma-22 {
  margin: 22px !important;
}

.ml-22 {
  margin-left: 22px !important;
}

.mt-22 {
  margin-top: 22px !important;
}

.mr-22 {
  margin-right: 22px !important;
}

.mb-22 {
  margin-bottom: 22px !important;
}

.mv-22 {
  margin-top: 22px !important;
  margin-bottom: 22px !important;
}

.mh-22 {
  margin-left: 22px !important;
  margin-right: 22px !important;
}

.ma-24 {
  margin: 24px !important;
}

.ml-24 {
  margin-left: 24px !important;
}

.mt-24 {
  margin-top: 24px !important;
}

.mr-24 {
  margin-right: 24px !important;
}

.mb-24 {
  margin-bottom: 24px !important;
}

.mv-24 {
  margin-top: 24px !important;
  margin-bottom: 24px !important;
}

.mh-24 {
  margin-left: 24px !important;
  margin-right: 24px !important;
}

.ma-26 {
  margin: 26px !important;
}

.ml-26 {
  margin-left: 26px !important;
}

.mt-26 {
  margin-top: 26px !important;
}

.mr-26 {
  margin-right: 26px !important;
}

.mb-26 {
  margin-bottom: 26px !important;
}

.mv-26 {
  margin-top: 26px !important;
  margin-bottom: 26px !important;
}

.mh-26 {
  margin-left: 26px !important;
  margin-right: 26px !important;
}

.ma-28 {
  margin: 28px !important;
}

.ml-28 {
  margin-left: 28px !important;
}

.mt-28 {
  margin-top: 28px !important;
}

.mr-28 {
  margin-right: 28px !important;
}

.mb-28 {
  margin-bottom: 28px !important;
}

.mv-28 {
  margin-top: 28px !important;
  margin-bottom: 28px !important;
}

.mh-28 {
  margin-left: 28px !important;
  margin-right: 28px !important;
}

.ma-30 {
  margin: 30px !important;
}

.ml-30 {
  margin-left: 30px !important;
}

.mt-30 {
  margin-top: 30px !important;
}

.mr-30 {
  margin-right: 30px !important;
}

.mb-30 {
  margin-bottom: 30px !important;
}

.mv-30 {
  margin-top: 30px !important;
  margin-bottom: 30px !important;
}

.mh-30 {
  margin-left: 30px !important;
  margin-right: 30px !important;
}

.ma-32 {
  margin: 32px !important;
}

.ml-32 {
  margin-left: 32px !important;
}

.mt-32 {
  margin-top: 32px !important;
}

.mr-32 {
  margin-right: 32px !important;
}

.mb-32 {
  margin-bottom: 32px !important;
}

.mv-32 {
  margin-top: 32px !important;
  margin-bottom: 32px !important;
}

.mh-32 {
  margin-left: 32px !important;
  margin-right: 32px !important;
}

.ma-34 {
  margin: 34px !important;
}

.ml-34 {
  margin-left: 34px !important;
}

.mt-34 {
  margin-top: 34px !important;
}

.mr-34 {
  margin-right: 34px !important;
}

.mb-34 {
  margin-bottom: 34px !important;
}

.mv-34 {
  margin-top: 34px !important;
  margin-bottom: 34px !important;
}

.mh-34 {
  margin-left: 34px !important;
  margin-right: 34px !important;
}

.ma-36 {
  margin: 36px !important;
}

.ml-36 {
  margin-left: 36px !important;
}

.mt-36 {
  margin-top: 36px !important;
}

.mr-36 {
  margin-right: 36px !important;
}

.mb-36 {
  margin-bottom: 36px !important;
}

.mv-36 {
  margin-top: 36px !important;
  margin-bottom: 36px !important;
}

.mh-36 {
  margin-left: 36px !important;
  margin-right: 36px !important;
}

.ma-38 {
  margin: 38px !important;
}

.ml-38 {
  margin-left: 38px !important;
}

.mt-38 {
  margin-top: 38px !important;
}

.mr-38 {
  margin-right: 38px !important;
}

.mb-38 {
  margin-bottom: 38px !important;
}

.mv-38 {
  margin-top: 38px !important;
  margin-bottom: 38px !important;
}

.mh-38 {
  margin-left: 38px !important;
  margin-right: 38px !important;
}

.ma-40 {
  margin: 40px !important;
}

.ml-40 {
  margin-left: 40px !important;
}

.mt-40 {
  margin-top: 40px !important;
}

.mr-40 {
  margin-right: 40px !important;
}

.mb-40 {
  margin-bottom: 40px !important;
}

.mv-40 {
  margin-top: 40px !important;
  margin-bottom: 40px !important;
}

.mh-40 {
  margin-left: 40px !important;
  margin-right: 40px !important;
}

.ma-42 {
  margin: 42px !important;
}

.ml-42 {
  margin-left: 42px !important;
}

.mt-42 {
  margin-top: 42px !important;
}

.mr-42 {
  margin-right: 42px !important;
}

.mb-42 {
  margin-bottom: 42px !important;
}

.mv-42 {
  margin-top: 42px !important;
  margin-bottom: 42px !important;
}

.mh-42 {
  margin-left: 42px !important;
  margin-right: 42px !important;
}

.ma-44 {
  margin: 44px !important;
}

.ml-44 {
  margin-left: 44px !important;
}

.mt-44 {
  margin-top: 44px !important;
}

.mr-44 {
  margin-right: 44px !important;
}

.mb-44 {
  margin-bottom: 44px !important;
}

.mv-44 {
  margin-top: 44px !important;
  margin-bottom: 44px !important;
}

.mh-44 {
  margin-left: 44px !important;
  margin-right: 44px !important;
}

.ma-46 {
  margin: 46px !important;
}

.ml-46 {
  margin-left: 46px !important;
}

.mt-46 {
  margin-top: 46px !important;
}

.mr-46 {
  margin-right: 46px !important;
}

.mb-46 {
  margin-bottom: 46px !important;
}

.mv-46 {
  margin-top: 46px !important;
  margin-bottom: 46px !important;
}

.mh-46 {
  margin-left: 46px !important;
  margin-right: 46px !important;
}

.ma-48 {
  margin: 48px !important;
}

.ml-48 {
  margin-left: 48px !important;
}

.mt-48 {
  margin-top: 48px !important;
}

.mr-48 {
  margin-right: 48px !important;
}

.mb-48 {
  margin-bottom: 48px !important;
}

.mv-48 {
  margin-top: 48px !important;
  margin-bottom: 48px !important;
}

.mh-48 {
  margin-left: 48px !important;
  margin-right: 48px !important;
}

.ma-50 {
  margin: 50px !important;
}

.ml-50 {
  margin-left: 50px !important;
}

.mt-50 {
  margin-top: 50px !important;
}

.mr-50 {
  margin-right: 50px !important;
}

.mb-50 {
  margin-bottom: 50px !important;
}

.mv-50 {
  margin-top: 50px !important;
  margin-bottom: 50px !important;
}

.mh-50 {
  margin-left: 50px !important;
  margin-right: 50px !important;
}

.ma-52 {
  margin: 52px !important;
}

.ml-52 {
  margin-left: 52px !important;
}

.mt-52 {
  margin-top: 52px !important;
}

.mr-52 {
  margin-right: 52px !important;
}

.mb-52 {
  margin-bottom: 52px !important;
}

.mv-52 {
  margin-top: 52px !important;
  margin-bottom: 52px !important;
}

.mh-52 {
  margin-left: 52px !important;
  margin-right: 52px !important;
}

.ma-54 {
  margin: 54px !important;
}

.ml-54 {
  margin-left: 54px !important;
}

.mt-54 {
  margin-top: 54px !important;
}

.mr-54 {
  margin-right: 54px !important;
}

.mb-54 {
  margin-bottom: 54px !important;
}

.mv-54 {
  margin-top: 54px !important;
  margin-bottom: 54px !important;
}

.mh-54 {
  margin-left: 54px !important;
  margin-right: 54px !important;
}

.ma-56 {
  margin: 56px !important;
}

.ml-56 {
  margin-left: 56px !important;
}

.mt-56 {
  margin-top: 56px !important;
}

.mr-56 {
  margin-right: 56px !important;
}

.mb-56 {
  margin-bottom: 56px !important;
}

.mv-56 {
  margin-top: 56px !important;
  margin-bottom: 56px !important;
}

.mh-56 {
  margin-left: 56px !important;
  margin-right: 56px !important;
}

.ma-58 {
  margin: 58px !important;
}

.ml-58 {
  margin-left: 58px !important;
}

.mt-58 {
  margin-top: 58px !important;
}

.mr-58 {
  margin-right: 58px !important;
}

.mb-58 {
  margin-bottom: 58px !important;
}

.mv-58 {
  margin-top: 58px !important;
  margin-bottom: 58px !important;
}

.mh-58 {
  margin-left: 58px !important;
  margin-right: 58px !important;
}

.ma-60 {
  margin: 60px !important;
}

.ml-60 {
  margin-left: 60px !important;
}

.mt-60 {
  margin-top: 60px !important;
}

.mr-60 {
  margin-right: 60px !important;
}

.mb-60 {
  margin-bottom: 60px !important;
}

.mv-60 {
  margin-top: 60px !important;
  margin-bottom: 60px !important;
}

.mh-60 {
  margin-left: 60px !important;
  margin-right: 60px !important;
}

.ma-62 {
  margin: 62px !important;
}

.ml-62 {
  margin-left: 62px !important;
}

.mt-62 {
  margin-top: 62px !important;
}

.mr-62 {
  margin-right: 62px !important;
}

.mb-62 {
  margin-bottom: 62px !important;
}

.mv-62 {
  margin-top: 62px !important;
  margin-bottom: 62px !important;
}

.mh-62 {
  margin-left: 62px !important;
  margin-right: 62px !important;
}

.ma-64 {
  margin: 64px !important;
}

.ml-64 {
  margin-left: 64px !important;
}

.mt-64 {
  margin-top: 64px !important;
}

.mr-64 {
  margin-right: 64px !important;
}

.mb-64 {
  margin-bottom: 64px !important;
}

.mv-64 {
  margin-top: 64px !important;
  margin-bottom: 64px !important;
}

.mh-64 {
  margin-left: 64px !important;
  margin-right: 64px !important;
}

.ma-66 {
  margin: 66px !important;
}

.ml-66 {
  margin-left: 66px !important;
}

.mt-66 {
  margin-top: 66px !important;
}

.mr-66 {
  margin-right: 66px !important;
}

.mb-66 {
  margin-bottom: 66px !important;
}

.mv-66 {
  margin-top: 66px !important;
  margin-bottom: 66px !important;
}

.mh-66 {
  margin-left: 66px !important;
  margin-right: 66px !important;
}

.ma-68 {
  margin: 68px !important;
}

.ml-68 {
  margin-left: 68px !important;
}

.mt-68 {
  margin-top: 68px !important;
}

.mr-68 {
  margin-right: 68px !important;
}

.mb-68 {
  margin-bottom: 68px !important;
}

.mv-68 {
  margin-top: 68px !important;
  margin-bottom: 68px !important;
}

.mh-68 {
  margin-left: 68px !important;
  margin-right: 68px !important;
}

.ma-70 {
  margin: 70px !important;
}

.ml-70 {
  margin-left: 70px !important;
}

.mt-70 {
  margin-top: 70px !important;
}

.mr-70 {
  margin-right: 70px !important;
}

.mb-70 {
  margin-bottom: 70px !important;
}

.mv-70 {
  margin-top: 70px !important;
  margin-bottom: 70px !important;
}

.mh-70 {
  margin-left: 70px !important;
  margin-right: 70px !important;
}

.ma-72 {
  margin: 72px !important;
}

.ml-72 {
  margin-left: 72px !important;
}

.mt-72 {
  margin-top: 72px !important;
}

.mr-72 {
  margin-right: 72px !important;
}

.mb-72 {
  margin-bottom: 72px !important;
}

.mv-72 {
  margin-top: 72px !important;
  margin-bottom: 72px !important;
}

.mh-72 {
  margin-left: 72px !important;
  margin-right: 72px !important;
}

.ma-74 {
  margin: 74px !important;
}

.ml-74 {
  margin-left: 74px !important;
}

.mt-74 {
  margin-top: 74px !important;
}

.mr-74 {
  margin-right: 74px !important;
}

.mb-74 {
  margin-bottom: 74px !important;
}

.mv-74 {
  margin-top: 74px !important;
  margin-bottom: 74px !important;
}

.mh-74 {
  margin-left: 74px !important;
  margin-right: 74px !important;
}

.ma-76 {
  margin: 76px !important;
}

.ml-76 {
  margin-left: 76px !important;
}

.mt-76 {
  margin-top: 76px !important;
}

.mr-76 {
  margin-right: 76px !important;
}

.mb-76 {
  margin-bottom: 76px !important;
}

.mv-76 {
  margin-top: 76px !important;
  margin-bottom: 76px !important;
}

.mh-76 {
  margin-left: 76px !important;
  margin-right: 76px !important;
}

.ma-78 {
  margin: 78px !important;
}

.ml-78 {
  margin-left: 78px !important;
}

.mt-78 {
  margin-top: 78px !important;
}

.mr-78 {
  margin-right: 78px !important;
}

.mb-78 {
  margin-bottom: 78px !important;
}

.mv-78 {
  margin-top: 78px !important;
  margin-bottom: 78px !important;
}

.mh-78 {
  margin-left: 78px !important;
  margin-right: 78px !important;
}

.ma-80 {
  margin: 80px !important;
}

.ml-80 {
  margin-left: 80px !important;
}

.mt-80 {
  margin-top: 80px !important;
}

.mr-80 {
  margin-right: 80px !important;
}

.mb-80 {
  margin-bottom: 80px !important;
}

.mv-80 {
  margin-top: 80px !important;
  margin-bottom: 80px !important;
}

.mh-80 {
  margin-left: 80px !important;
  margin-right: 80px !important;
}

.ma-82 {
  margin: 82px !important;
}

.ml-82 {
  margin-left: 82px !important;
}

.mt-82 {
  margin-top: 82px !important;
}

.mr-82 {
  margin-right: 82px !important;
}

.mb-82 {
  margin-bottom: 82px !important;
}

.mv-82 {
  margin-top: 82px !important;
  margin-bottom: 82px !important;
}

.mh-82 {
  margin-left: 82px !important;
  margin-right: 82px !important;
}

.ma-84 {
  margin: 84px !important;
}

.ml-84 {
  margin-left: 84px !important;
}

.mt-84 {
  margin-top: 84px !important;
}

.mr-84 {
  margin-right: 84px !important;
}

.mb-84 {
  margin-bottom: 84px !important;
}

.mv-84 {
  margin-top: 84px !important;
  margin-bottom: 84px !important;
}

.mh-84 {
  margin-left: 84px !important;
  margin-right: 84px !important;
}

.ma-86 {
  margin: 86px !important;
}

.ml-86 {
  margin-left: 86px !important;
}

.mt-86 {
  margin-top: 86px !important;
}

.mr-86 {
  margin-right: 86px !important;
}

.mb-86 {
  margin-bottom: 86px !important;
}

.mv-86 {
  margin-top: 86px !important;
  margin-bottom: 86px !important;
}

.mh-86 {
  margin-left: 86px !important;
  margin-right: 86px !important;
}

.ma-88 {
  margin: 88px !important;
}

.ml-88 {
  margin-left: 88px !important;
}

.mt-88 {
  margin-top: 88px !important;
}

.mr-88 {
  margin-right: 88px !important;
}

.mb-88 {
  margin-bottom: 88px !important;
}

.mv-88 {
  margin-top: 88px !important;
  margin-bottom: 88px !important;
}

.mh-88 {
  margin-left: 88px !important;
  margin-right: 88px !important;
}

.ma-90 {
  margin: 90px !important;
}

.ml-90 {
  margin-left: 90px !important;
}

.mt-90 {
  margin-top: 90px !important;
}

.mr-90 {
  margin-right: 90px !important;
}

.mb-90 {
  margin-bottom: 90px !important;
}

.mv-90 {
  margin-top: 90px !important;
  margin-bottom: 90px !important;
}

.mh-90 {
  margin-left: 90px !important;
  margin-right: 90px !important;
}

.ma-92 {
  margin: 92px !important;
}

.ml-92 {
  margin-left: 92px !important;
}

.mt-92 {
  margin-top: 92px !important;
}

.mr-92 {
  margin-right: 92px !important;
}

.mb-92 {
  margin-bottom: 92px !important;
}

.mv-92 {
  margin-top: 92px !important;
  margin-bottom: 92px !important;
}

.mh-92 {
  margin-left: 92px !important;
  margin-right: 92px !important;
}

.ma-94 {
  margin: 94px !important;
}

.ml-94 {
  margin-left: 94px !important;
}

.mt-94 {
  margin-top: 94px !important;
}

.mr-94 {
  margin-right: 94px !important;
}

.mb-94 {
  margin-bottom: 94px !important;
}

.mv-94 {
  margin-top: 94px !important;
  margin-bottom: 94px !important;
}

.mh-94 {
  margin-left: 94px !important;
  margin-right: 94px !important;
}

.ma-96 {
  margin: 96px !important;
}

.ml-96 {
  margin-left: 96px !important;
}

.mt-96 {
  margin-top: 96px !important;
}

.mr-96 {
  margin-right: 96px !important;
}

.mb-96 {
  margin-bottom: 96px !important;
}

.mv-96 {
  margin-top: 96px !important;
  margin-bottom: 96px !important;
}

.mh-96 {
  margin-left: 96px !important;
  margin-right: 96px !important;
}

.ma-98 {
  margin: 98px !important;
}

.ml-98 {
  margin-left: 98px !important;
}

.mt-98 {
  margin-top: 98px !important;
}

.mr-98 {
  margin-right: 98px !important;
}

.mb-98 {
  margin-bottom: 98px !important;
}

.mv-98 {
  margin-top: 98px !important;
  margin-bottom: 98px !important;
}

.mh-98 {
  margin-left: 98px !important;
  margin-right: 98px !important;
}

.ma-100 {
  margin: 100px !important;
}

.ml-100 {
  margin-left: 100px !important;
}

.mt-100 {
  margin-top: 100px !important;
}

.mr-100 {
  margin-right: 100px !important;
}

.mb-100 {
  margin-bottom: 100px !important;
}

.mv-100 {
  margin-top: 100px !important;
  margin-bottom: 100px !important;
}

.mh-100 {
  margin-left: 100px !important;
  margin-right: 100px !important;
}

/*
    1)
      Font Name: Lexend
      Font URI: https://fonts.google.com/specimen/Lexend

    2)
      Font Name: Inter
      Font URI: https://fonts.google.com/specimen/Inter

    3)
      Font Name: Lexend
      Font URI: https://fonts.google.com/specimen/Poppins
*/
.default-theme {
  /* text color */
  --ot-primary-text: #6f767e;
  --ot-primary-text: #1a1d1f;
  --ot-text-subtitle: #33383f;
  /* background color */
  --ot-bg-primary: #f0f3f5;
  --ot-bg-secondary: #ffffff;
  --ot-bg-secondary-opacity: rgba(255, 255, 255, 0.35);
  --ot-bg-tertiary: #ffffff;
  /* border color */
  --ot-primary-border: #eaeaea;
  /* badge background color */
  --ot-bg-badge-success: #29d697;
  --ot-bg-badge-danger: #ff6a54;
  --ot-bg-badge-warning: #fdc400;
  --ot-bg-badge-primary: #1890ff;
  /* badge text color */
  --ot-text-badge-success: #ffffff;
  --ot-text-badge-danger: #ffffff;
  --ot-text-badge-warning: #ffffff;
  --ot-text-badge-primary: #ffffff;
  /* badge light background color */
  --ot-bg-badge-light-success: #e9faf4;
  --ot-bg-badge-light-danger: #fff0ed;
  --ot-bg-badge-light-warning: #fef9e5;
  --ot-bg-badge-light-primary: #e6f2fd;
  /* badge light text color */
  --ot-text-badge-light-success: #29d697;
  --ot-text-badge-light-danger: #ff6a54;
  --ot-text-badge-light-warning: #fdc400;
  --ot-text-badge-light-primary: #1890ff;
  /* badge deep text color */
  --ot-text-badge-deep-success: #29d697;
  --ot-text-badge-deep-danger: #ff6a54;
  --ot-text-badge-deep-warning: #fdc400;
  --ot-text-badge-deep-primary: #1890ff;
  /* table color */
  --ot-bg-table-card: #ffffff;
  --ot-border-table-card: #ffffff;
  --ot-bg-table-card-header: #ffffff;
  --ot-border-table-card-header: #eaeaea;
  --ot-border-table: #eaeaea;
  --ot-bg-table-thead: #f7fafc;
  --ot-border-table-thead: #eaeaea;
  --ot-bg-table-tbody: #f7fafc;
  --ot-bg-table-checkbox: #ffffff;
  --ot-border-table-checkbox: #eaeaea;
  --ot-color-table-icon-sorting-asc-up: #1a1d1f;
  --ot-color-table-icon-sorting-asc-down: #bebfc0;
  --ot-color-table-icon-sorting-desc-up: #bebfc0;
  --ot-color-table-icon-sorting-desc-down: #1a1d1f;
  --ot-bg-table-toolbar-per-page: #ffffff;
  --ot-border-table-toolbar-per-page: #eaeaea;
  --ot-bg-table-toolbar-btn-outline-primary: #ffffff;
  --ot-bg-table-toolbar-search: #ffffff;
  --ot-border-table-toolbar-search: #eaeaea;
  --ot-bg-table-toolbar-btn-action: #fcfcfc;
  --ot-border-table-toolbar-btn-action: #fcfcfc;
  /* table pagination */
  --ot-bg-table-pagination: #ffffff;
  --ot-text-table-pagination: #1a1d1f;
  --ot-border-table-pagination: #eaeaea;
  /* profile */
  --ot-bg-profile-menu: #fcfcfc;
  --ot-bg-profile-body: #fcfcfc;
  --ot-bg-profile-mobile-menu: #fcfcfc;
  --ot-disable-date: #b2bec3;
  --ot-bg-hover-datepicker: #f7f7f7;
  --ot-text-hover-datepicker: #000;
  --ot-bg-slider: #ccc;
  --ot-bg-before: #fff;
}

.dark-theme {
  /* text color */
  --ot-primary-text: #6f767e;
  --ot-primary-text: #ffffff;
  --ot-text-subtitle: #6f767e;
  /* background color */
  --ot-bg-primary: #111415;
  --ot-bg-secondary: #1a1d20;
  --ot-bg-secondary-opacity: #1a1d20;
  --ot-bg-tertiary: #1f2124;
  /* border color */
  --ot-primary-border: #272b30;
  /* badge background color */
  --ot-bg-badge-success: rgba(41, 214, 151, 0.1);
  --ot-bg-badge-danger: rgba(255, 106, 84, 0.1);
  --ot-bg-badge-warning: rgba(253, 196, 0, 0.1);
  --ot-bg-badge-primary: rgba(24, 144, 255, 0.1);
  /* badge text color */
  --ot-text-badge-success: #29d697;
  --ot-text-badge-danger: #ff6a54;
  --ot-text-badge-warning: #fdc400;
  --ot-text-badge-primary: #1890ff;
  /* badge deep color */
  --ot-text-badge-deep-success: #29d697;
  --ot-text-badge-deep-danger: #ff6a54;
  --ot-text-badge-deep-warning: #fdc400;
  --ot-text-badge-deep-primary: #1890ff;
  /* badge light background color */
  --ot-bg-badge-light-success: rgba(233, 250, 244, 0.1);
  --ot-bg-badge-light-danger: rgba(255, 240, 237, 0.1);
  --ot-bg-badge-light-warning: rgba(254, 249, 229, 0.1);
  --ot-bg-badge-light-primary: rgba(230, 242, 253, 0.1);
  /* badge light text color */
  --ot-text-badge-light-success: #29d697;
  --ot-text-badge-light-danger: #ff6a54;
  --ot-text-badge-light-warning: #fdc400;
  --ot-text-badge-light-primary: #1890ff;
  /* table color */
  --ot-bg-table-card: #1a1d20;
  --ot-border-table-card: #ffffff;
  --ot-bg-table-card-header: #1a1d20;
  --ot-border-table-card-header: transparent;
  --ot-border-table: transparent;
  --ot-bg-table-thead: #1f2124;
  --ot-border-table-thead: #1f2124;
  --ot-bg-table-tbody: #1f2124;
  --ot-bg-table-checkbox: #1a1d20;
  --ot-border-table-checkbox: #272b30;
  --ot-color-table-icon-sorting-asc-up: rgba(190, 191, 192, 0.1);
  --ot-color-table-icon-sorting-asc-down: #bebfc0;
  --ot-color-table-icon-sorting-desc-up: #bebfc0;
  --ot-color-table-icon-sorting-desc-down: rgba(190, 191, 192, 0.1);
  --ot-bg-table-toolbar-per-page: #1f2124;
  --ot-border-table-toolbar-per-page: #272b30;
  --ot-bg-table-toolbar-btn-outline-primary: #1a1d20;
  --ot-bg-table-toolbar-search: #1f2124;
  --ot-border-table-toolbar-search: #272b30;
  --ot-bg-table-toolbar-btn-action: #1f2124;
  --ot-border-table-toolbar-btn-action: #272b30;
  /* table pagination */
  --ot-bg-table-pagination: #1a1d20;
  --ot-text-table-pagination: #ffffff;
  --ot-border-table-pagination: #272b30;
  /* profile */
  --ot-bg-profile-menu: var(--ot-bg-secondary);
  --ot-bg-profile-body: var(--ot-bg-secondary);
  --ot-bg-profile-mobile-menu: var(--ot-bg-secondary);
  --ot-disable-date: #424f5570;
  --ot-bg-hover-datepicker: #363636ae;
  --ot-text-hover-datepicker: rgb(255, 255, 255);
  --ot-bg-slider: #474747a8;
  --ot-bg-before: #000;
}

/* Global text color */
/* Global bg color */
/* border color */
/* Global badge background color */
/* Global badge text color */
/* Global badge light background color */
/* Global badge light text color */
/* Global table color */
/* Global table pagination */
/* profile */
*,
*::before,
*::after {
  box-sizing: border-box;
}

html {
  box-sizing: border-box;
  scroll-behavior: smooth;
}

body {
  margin: 0;
  font-family: "Lexend", sans-serif;
  background-size: cover;
  background-position: center center;
  background-attachment: fixed;
}

body.dark-theme {
  background: none;
  background-color: var(--ot-bg-primary);
}

/* Heading Text */
h6,
h5,
h4,
h3,
h2,
h1 {
  color: var(--ot-primary-text);
}

/* Heading Text */
p,
a,
label {
  color: var(--ot-text-subtitle);
}

/*
    Base components are imported from the base folder.

    Index
    -----

    1) Scrollbar
    2) Topbar
    3) Topbar Dropdown Menu
    4) Profile Expand
    5) Sidebar
    6) Theme Switch
    7) Main Content
    8) Common Card
    9) Summery Card
    10) Charts
    11) Common Buttons
    11) Tables
    12) Accordion
    13) Signup, Signin
    14) Error Page
    15) Card
    16) Modal
    17) Alert
    18) Color Template
    19) Dropdown
    20) Badges
    21) Global Components
    22) Paginations
    23) Inputs
    24) Breadcrumb
*/
/* Scrollbar */
/* width */
::-webkit-scrollbar {
  width: 8px;
  border-radius: 5px;
}

/* Track */
::-webkit-scrollbar-track {
  background: transparent;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: rgba(35, 180, 220, 0.1) !important;
  border-radius: 8px;
  background-clip: padding-box;
}

/* Topbar */
/*Changes while RTL Start*/
body.rtl .header {
  right: 256px;
  left: 0;
  transition: -webkit- right 0.4s;
  transition: right 0.4s;
  transition: right 0.4s, -webkit- right 0.4s;
}
@media (max-width: 992px) {
  body.rtl .header {
    padding: 10px;
    right: 0;
  }
}
body.rtl .half-expand .header {
  z-index: 22;
  right: 100px;
  transition: -webkit- right 0.4s;
  transition: right 0.4s;
  transition: right 0.4s, -webkit- right 0.4s;
}
body.rtl .profile-expand-list {
  float: right;
  align-items: baseline;
  width: 100%;
}
body.rtl .profile-expand-list .profile-expand-item {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 10px;
}
body.rtl .topbar-dropdown-menu h1 {
  float: right;
  padding-right: 20px;
  padding-left: 0;
}
body.rtl .language-list h5,
body.rtl .currency-list h5 {
  float: right;
}

/*Changes while RTL End*/
/* Dark Theme Style start*/
.dark-theme .header {
  border-bottom: none;
}

/* Dark Theme Style end*/
.header {
  position: fixed;
  top: 0;
  left: 256px;
  right: 0;
  z-index: 20;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 17px 32px;
  background: var(--ot-bg-secondary-opacity);
  border-bottom: 2px solid #fff;
  transition: -webkit- left 0.4s;
  transition: left 0.4s;
  transition: left 0.4s, -webkit- left 0.4s;
}
.header .search-field {
  background: var(--ot-bg-secondary);
}
.header.on-scroll {
  background: var(--ot-bg-secondary);
}
.default-theme .header.on-scroll {
  background: #edfbff;
}
@media (max-width: 992px) {
  .header {
    width: 100%;
    padding: 10px;
    left: 0;
    transition: transform 0.3s;
  }
}
.header button {
  border: 0;
  background: transparent;
  outline: none;
}
.header .dropdown .dropdown-menu {
  transition: all 0.3s;
  max-height: 0;
  display: block;
  overflow: hidden;
  opacity: 0;
}
.header .close-toggle {
  display: none;
  transform: rotate(180deg);
}
.header .header-search {
  position: relative;
  width: 310px;
}
.header .header-search .search-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
}
.header .header-search .search-field {
  width: 100%;
  padding: 12px 32px 12px 16px;
  border-radius: 5px;
  border: none;
  background-color: var(--ot-bg-primary);
}
.default-theme .header .header-search .search-field {
  background-color: var(--ot-bg-secondary);
}
.header .header-search .search-field:focus-visible {
  outline-color: #eef0ff;
}
.header .header-controls {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.header .header-controls .header-control-item {
  margin-right: 38px;
}
.header .header-controls .header-control-item:last-child {
  margin-right: 0;
}
.header .header-controls .header-control-item .item-content button h6 {
  font-size: 14px;
}
.header
  .header-controls
  .header-control-item
  .item-content
  button
  h6.language-change {
  display: flex;
  align-items: center;
  justify-content: center;
}
.header
  .header-controls
  .header-control-item
  .item-content
  button.profile-navigate {
  display: flex;
  align-items: center;
  gap: 12px;
  width: -webkit-max-content;
  width: -moz-max-content;
  width: max-content;
}
.header
  .header-controls
  .header-control-item
  .item-content
  button.profile-navigate.show {
  display: flex !important;
}
.header
  .header-controls
  .header-control-item
  .item-content
  button.profile-navigate
  .profile-photo {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}
.header
  .header-controls
  .header-control-item
  .item-content
  button.profile-navigate
  .profile-photo
  img {
  max-width: 100%;
  max-height: 100%;
  border-radius: 50%;
}
.header
  .header-controls
  .header-control-item
  .item-content
  button.profile-navigate
  .profile-info {
  display: flex;
  flex-direction: column;
  align-items: baseline;
}
.header
  .header-controls
  .header-control-item
  .item-content
  button.profile-navigate
  .profile-info
  h6 {
  font-weight: 600;
  font-size: 16px;
  line-height: 20px;
  margin: 0;
}
.header
  .header-controls
  .header-control-item
  .item-content
  button.profile-navigate
  .profile-info
  p {
  font-weight: 500;
  font-size: 12px;
  line-height: 15px;
  margin: 0;
}
.header .header-controls .header-control-item .item-content button.icon {
  width: 24px;
  height: 24px;
}
.header
  .header-controls
  .header-control-item
  .language-currceny-container
  .language-currency-btn {
  gap: 12px;
}
.header
  .header-controls
  .header-control-item
  .language-currceny-container
  .language-currency-btn
  h6,
.header
  .header-controls
  .header-control-item
  .language-currceny-container
  .language-currency-btn
  span {
  color: var(--ot-text-primary);
  font-size: 14px;
  margin-bottom: 0;
}
.header
  .header-controls
  .header-control-item
  .language-currceny-container
  .language-currency-btn
  h6
  i,
.header
  .header-controls
  .header-control-item
  .language-currceny-container
  .language-currency-btn
  span
  i {
  transition: transform 0.3s;
}
.header
  .header-controls
  .header-control-item
  .language-currceny-container
  .language-currency-btn.show
  span
  i {
  transform: rotate(180deg);
  transition: transform 0.3s;
}
.header
  .header-controls
  .header-control-item
  .language-currceny-container
  .language-currency-btn
  .vertical-devider {
  height: 10px;
  width: 2px;
  background-color: #d1f2ff;
}
.header
  .header-controls
  .header-control-item
  .language-currceny-container
  .language-currency-dropdown {
  width: 392px;
  padding: 28px;
  font-weight: 500;
  font-size: 14px;
  line-height: 18px;
  color: var(--ot-text-primary);
}
.header
  .header-controls
  .header-control-item
  .language-currceny-container
  .language-currency-dropdown
  h5 {
  color: var(--ot-text-primary);
  font-size: 16px;
  margin-bottom: 12px;
}
.header
  .header-controls
  .header-control-item
  .language-currceny-container
  .language-currency-dropdown
  .select-hidden {
  display: none;
  visibility: hidden;
  padding-right: 10px;
}
.header
  .header-controls
  .header-control-item
  .language-currceny-container
  .language-currency-dropdown
  .select {
  cursor: pointer;
  display: inline-block;
  position: relative;
  font-size: 16px;
  color: var(--ot-text-primary);
  width: 100%;
  height: 48px;
  border: 1px solid #eaeaea;
  border-radius: 5px;
}
.header
  .header-controls
  .header-control-item
  .language-currceny-container
  .language-currency-dropdown
  .select-styled {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: white;
  padding: 8px 15px;
  transition: all 0.2s ease-in;
  display: flex;
  align-items: center;
  gap: 15px;
}
.header
  .header-controls
  .header-control-item
  .language-currceny-container
  .language-currency-dropdown
  .select-styled:after {
  content: "";
  width: 0;
  height: 0;
  border: 7px solid transparent;
  border-color: #fff transparent transparent transparent;
  position: absolute;
  top: 16px;
  right: 10px;
}
.header
  .header-controls
  .header-control-item
  .language-currceny-container
  .language-currency-dropdown
  .select-styled:active,
.header
  .header-controls
  .header-control-item
  .language-currceny-container
  .language-currency-dropdown
  .select-styled.active {
  box-shadow: 0px 0px 10px rgba(86, 105, 255, 0.35);
  border-radius: 5px;
}
.header
  .header-controls
  .header-control-item
  .language-currceny-container
  .language-currency-dropdown
  .select-styled:active:after,
.header
  .header-controls
  .header-control-item
  .language-currceny-container
  .language-currency-dropdown
  .select-styled.active:after {
  top: 9px;
  border-color: transparent transparent #fff transparent;
}
.header
  .header-controls
  .header-control-item
  .language-currceny-container
  .language-currency-dropdown
  .select-options {
  display: none;
  z-index: 999;
  margin: 0;
  padding: 0;
  list-style: none;
  background-color: #fff;
  border: 1px solid #eaeaea;
  border-radius: 5px;
  margin-top: 55px;
  padding: 20px;
}
.header
  .header-controls
  .header-control-item
  .language-currceny-container
  .language-currency-dropdown
  .select-options
  li {
  margin: 0;
  padding-bottom: 16px;
  transition: all 0.15s ease-in;
  display: flex;
  align-items: center;
  gap: 15px;
}
.header
  .header-controls
  .header-control-item
  .language-currceny-container
  .language-currency-dropdown
  .select-options
  li:last-child {
  padding-bottom: 0;
}
.header
  .header-controls
  .header-control-item
  .language-currceny-container
  .language-currency-dropdown
  .select-options
  li
  img {
  width: 30px;
  height: 18px;
}
.header
  .header-controls
  .header-control-item
  .language-currceny-container
  .language-currency-dropdown
  .select-options
  li:hover,
.header
  .header-controls
  .header-control-item
  .language-currceny-container
  .language-currency-dropdown
  .select-options
  li.is-selected {
  color: #6f767e;
  background: #fff;
}
.header
  .header-controls
  .header-control-item
  .language-currceny-container
  .language-currency-dropdown
  .select-options
  li[rel="hide"] {
  display: none;
}

.half-expand .header {
  z-index: 22;
  left: 100px;
  transition: -webkit- left 0.4s;
  transition: left 0.4s;
  transition: left 0.4s, -webkit- left 0.4s;
}

@media (max-width: 992px) {
  .close-toggle {
    display: block !important;
  }
}

@media (max-width: 992px) {
  .md-none,
  .header-search {
    display: none !important;
  }
}

/* Topbar Dropdown Menu */
.topbar-dropdown-menu {
  max-width: 425px;
  /* componet global styling start */
  /* Componet global styling end */
}
.topbar-dropdown-menu h1 {
  font-size: 20px;
  line-height: 32px;
  padding-left: 20px;
  font-weight: 600;
  color: var(--ot-text-primary);
}
.topbar-dropdown-menu .topbar-dropdown-content :last-child::before {
  content: none !important;
}
.topbar-dropdown-menu .topbar-dropdown-content .topbar-dropdown-item {
  padding: 0px;
  padding: 20px;
  border-radius: 5px;
  position: relative;
  white-space: normal;
  gap: 1rem;
  /* dropdown item avater start */
  /* dropdown item avater end */
  /* dropdown item content start */
  /* dropdown item content end */
}
.topbar-dropdown-menu .topbar-dropdown-content .topbar-dropdown-item:hover {
  background-color: var(--ot-bg-primary);
}
.topbar-dropdown-menu .topbar-dropdown-content .topbar-dropdown-item::before {
  content: "";
  position: absolute;
  bottom: 0;
  height: 1px;
  background: #efefef;
  left: 12px;
  right: 12px;
}
.topbar-dropdown-menu .topbar-dropdown-content .topbar-dropdown-item:active {
  color: #1e2125;
  background-color: #e9ecef;
}
.topbar-dropdown-menu
  .topbar-dropdown-content
  .topbar-dropdown-item
  .item-avater {
  min-width: 50px;
  min-height: 50px;
  max-width: 50px;
  max-height: 50px;
  border-radius: 50%;
  position: relative;
}
.topbar-dropdown-menu
  .topbar-dropdown-content
  .topbar-dropdown-item
  .item-avater
  img {
  width: 100%;
  height: 100%;
}
.topbar-dropdown-menu
  .topbar-dropdown-content
  .topbar-dropdown-item
  .item-avater
  .item-badge {
  position: absolute;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #6f767e;
  width: 10px;
  height: 10px;
  max-width: 10px;
  max-height: 10px;
  border-radius: 50%;
}
.topbar-dropdown-menu
  .topbar-dropdown-content
  .topbar-dropdown-item
  .item-avater
  .item-badge.active {
  background-color: #24c087;
}
.topbar-dropdown-menu
  .topbar-dropdown-content
  .topbar-dropdown-item
  .item-avater
  .item-badge.item-icon-badge {
  width: 18px;
  height: 18px;
  min-width: 18px;
  min-height: 18px;
  padding: 10px;
  background-color: #5669ff;
}
.topbar-dropdown-menu
  .topbar-dropdown-content
  .topbar-dropdown-item
  .item-avater
  .online-status-badge {
  width: 12px;
  height: 12px;
  border: 1px solid #fff;
}
.topbar-dropdown-menu
  .topbar-dropdown-content
  .topbar-dropdown-item
  .item-content
  h6 {
  font-size: 14px;
  line-height: 18px;
  color: var(--ot-text-primary);
  font-weight: 600;
}
.topbar-dropdown-menu
  .topbar-dropdown-content
  .topbar-dropdown-item
  .item-content
  h6
  span {
  color: var(--ot-text-primary);
}
.topbar-dropdown-menu
  .topbar-dropdown-content
  .topbar-dropdown-item
  .item-content
  h6.message {
  display: flex;
  flex-direction: column;
  gap: 10px;
  font-weight: 700;
  line-height: 15px;
}
.topbar-dropdown-menu
  .topbar-dropdown-content
  .topbar-dropdown-item
  .item-content
  h6.message
  span {
  font-size: 12px;
  font-weight: 400;
}
.topbar-dropdown-menu
  .topbar-dropdown-content
  .topbar-dropdown-item
  .item-status {
  color: var(--ot-text-primary);
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 5px;
}
.topbar-dropdown-menu
  .topbar-dropdown-content
  .topbar-dropdown-item
  .item-status
  .status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  min-width: 6px;
  min-height: 6px;
  background-color: #6f767e;
}
.topbar-dropdown-menu
  .topbar-dropdown-content
  .topbar-dropdown-item
  .item-status
  .status-dot.active {
  background: #5669ff;
}
.topbar-dropdown-menu .topbar-dropdown-footer {
  background: linear-gradient(90deg, #0f6aff 0%, #21c6fb 100%);
  padding: 12px;
  border-radius: 5px;
  margin-top: 20px;
  text-align: center;
}
.topbar-dropdown-menu .topbar-dropdown-footer a {
  color: #fff;
}
.topbar-dropdown-menu .topbar-dropdown-footer:hover {
  background: linear-gradient(90deg, #21c6fb 0%, #0f6aff 100%);
}

/* Profile Expand */
.profile-expand-dropdown {
  width: 265px;
  max-width: 265px;
}
.profile-expand-dropdown .profile-expand-container .profile-expand-list {
  gap: 20px;
  padding: 20px;
}
.profile-expand-dropdown
  .profile-expand-container
  .profile-expand-list
  .profile-expand-item {
  position: relative;
  text-decoration: none;
}
.profile-expand-dropdown
  .profile-expand-container
  .profile-expand-list
  .profile-expand-item.divider {
  margin-bottom: 20px;
}
.profile-expand-dropdown
  .profile-expand-container
  .profile-expand-list
  .profile-expand-item.divider::after {
  content: "";
  position: absolute;
  bottom: 0;
  height: 1px;
  background: #efefef;
  left: 4px;
  right: 4px;
  height: 2px;
  top: 50px;
}
.profile-expand-dropdown
  .profile-expand-container
  .profile-expand-list
  .profile-expand-item
  span {
  font-weight: 500;
  font-size: 15px;
  line-height: 28px;
  color: var(--ot-text-primary);
}
.profile-expand-dropdown
  .profile-expand-container
  .profile-expand-list
  .profile-expand-item:hover
  span {
  color: var(--ot-primary-text);
}
.profile-expand-dropdown
  .profile-expand-container
  .profile-expand-list
  .profile-expand-item
  img {
  width: 22px;
  height: 22px;
  margin-right: 10px;
}

/* Sidebar */
/*
    sidebarn    
*/
/*Changes while RTL Start*/
body.rtl .sidebar {
  right: 0;
}
@media (max-width: 768px) {
  body.rtl .sidebar {
    right: -100%;
    transition: right 0.3s;
  }
}
body.rtl .sidebar .sidebar-menu {
  height: calc(100vh - 60px);
  overflow-y: scroll;
  overflow-x: none;
  /* Default State reboot*/
}
body.rtl .sidebar .sidebar-menu ol,
body.rtl .sidebar .sidebar-menu ul {
  padding-right: 0;
  margin-right: 0;
}
body.rtl .sidebar .sidebar-menu ol a,
body.rtl .sidebar .sidebar-menu ul a {
  text-decoration: none;
}
body.rtl
  .sidebar
  .sidebar-menu
  .sidebar-menu-section
  .sidebar-menu-section-heading {
  margin-right: 17px;
}
body.rtl
  .sidebar
  .sidebar-menu
  .sidebar-menu-section
  .sidebar-menu-item
  > ul
  > li
  > a {
  padding-right: 47px;
}
body.rtl
  .sidebar
  .sidebar-menu
  .sidebar-menu-section
  .sidebar-menu-item
  > ul
  > li
  > ul
  > li
  > a {
  padding-right: 64px;
}
body.rtl .sidebar .sidebar-menu .sidebar-menu-section .sidebar-menu-item a {
  padding: 15px 25px 15px 0px;
}
body.rtl .sidebar .sidebar-menu .sidebar-menu-section .mm-active > a {
  border-left: none;
  border-right: 2px solid #0f6aff;
}
body.rtl .sidebar .sidebar-menu .sidebar-menu-section ul > li > a {
  padding-right: 28px;
}
body.rtl .sidebar-expand .sidebar {
  right: 0;
  transition: right 0.3s;
}

/*Changes while RTL END*/
/* Dark Theme Style start*/
.dark-theme .sidebar {
  border-right: none;
}

/* Dark Theme Style end*/
/* sidebar default design */
.sidebar {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  z-index: 300;
  display: flex;
  flex-direction: column;
  width: 256px;
  background-color: var(--ot-bg-secondary-opacity);
  border-right: 2px solid #fff;
  transition: -webkit- width 0.4s;
  transition: width 0.4s;
  transition: width 0.4s, -webkit- width 0.4s;
  /* Responsive Sidebar */
  /* Sidebar Header */
  /* Sidebar Menu */
}
.sidebar .close-toggle {
  display: none;
}
@media (max-width: 992px) {
  .sidebar .close-toggle {
    display: block;
  }
}
.sidebar .half-expand-toggle {
  display: block;
}
@media (max-width: 992px) {
  .sidebar .half-expand-toggle {
    display: none;
  }
}
@media (max-width: 992px) {
  .sidebar {
    z-index: 30;
    background-color: var(--ot-bg-secondary);
    z-index: 30;
    left: -100%;
    transition: -webkit-left 0.3s;
    transition: left 0.3s;
    transition: left 0.3s, -webkit-left 0.3s;
  }
}
.sidebar .sidebar-header {
  padding: 28px 14px;
  display: flex;
  justify-content: space-between;
  align-items: baseline;
  /* Sidebar Logo */
  /* Sidebar Half Expand Icon*/
}
.sidebar .sidebar-header .sidebar-logo .full-logo {
  opacity: 1;
  visibility: visible;
  height: auto;
  transition: -webkit- opacity 0.4s;
  transition: opacity 0.4s;
  transition: opacity 0.4s, -webkit- opacity 0.4s;
}
.sidebar .sidebar-header .sidebar-logo .half-logo {
  opacity: 0;
  visibility: hidden;
  height: 0;
  transition: opacity 1s;
}
.sidebar .sidebar-header .sidebar-toggle {
  border: 0;
  outline: 0;
  background: transparent;
  cursor: pointer;
  padding: 0;
  margin: 0;
}
.sidebar .sidebar-header .sidebar-close {
  border: 0;
  outline: 0;
  display: none;
  background: 0;
  padding: 0;
  margin-right: 10px;
  font-size: 28px;
}
@media (max-width: 992px) {
  .sidebar .sidebar-header .sidebar-close {
    display: block;
  }
}
.sidebar .sidebar-header .sidebar-close:hover {
  color: red;
}
.sidebar .sidebar-menu {
  height: calc(100vh - 60px);
  overflow-y: scroll;
  overflow-x: none;
  /* font icon size */
  /* has arrows color */
  /* Default State reboot*/
}
.sidebar .sidebar-menu i {
  font-size: 22px;
}
.sidebar .sidebar-menu ol,
.sidebar .sidebar-menu ul {
  list-style: none;
  padding-left: 0;
  margin-left: 0;
}
.sidebar .sidebar-menu ol a,
.sidebar .sidebar-menu ul a {
  text-decoration: none;
}
.sidebar .sidebar-menu .sidebar-menu-section .sidebar-menu-section-heading {
  text-transform: uppercase;
  font-size: 12px;
  font-weight: 500;
  line-height: 28px;
  margin-left: 17px;
  color: #0f6aff;
}
.sidebar .sidebar-menu .sidebar-menu-section .sidebar-menu-item > ul > li > a {
  padding-left: 47px;
  padding-top: 16px;
  padding-bottom: 16px;
}
.sidebar
  .sidebar-menu
  .sidebar-menu-section
  .sidebar-menu-item
  > ul
  > li
  > ul
  > li
  > a {
  padding-left: 64px;
  padding-top: 16px;
  padding-bottom: 16px;
}
.sidebar .sidebar-menu .sidebar-menu-section .sidebar-menu-item a {
  display: flex;
  flex-direction: row;
  align-items: center;
  cursor: pointer;
  gap: 10px;
  padding: 15px 0px 15px 25px;
}
.sidebar .sidebar-menu .sidebar-menu-section .sidebar-menu-item a::before {
  content: "";
  width: 5px;
  height: 5px;
  border-radius: 50%;
  background: #6f767e;
}
.sidebar
  .sidebar-menu
  .sidebar-menu-section
  .sidebar-menu-item
  a.parent-item-content::before {
  content: none;
}
.sidebar .sidebar-menu .sidebar-menu-section .sidebar-menu-item a:hover {
  background: linear-gradient(90deg, #0f6aff 0%, #21c6fb 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.sidebar .sidebar-menu .sidebar-menu-section .sidebar-menu-item .active a {
  background: linear-gradient(90deg, #0f6aff 0%, #21c6fb 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  border-left: 2px solid #0f6aff;
  background-color: rgba(115, 218, 255, 0.3);
}
.sidebar
  .sidebar-menu
  .sidebar-menu-section
  .sidebar-menu-item
  .active
  a::before {
  background: linear-gradient(90deg, #0f6aff 0%, #21c6fb 100%);
}
.sidebar
  .sidebar-menu
  .sidebar-menu-section
  .sidebar-menu-item
  .parent-item-content.has-arrow.active {
  background: linear-gradient(90deg, #0f6aff 0%, #21c6fb 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  border-left: 2px solid #0f6aff;
  background-color: rgba(115, 218, 255, 0.3);
}
.sidebar
  .sidebar-menu
  .sidebar-menu-section
  .sidebar-menu-item
  .parent-item-content.has-arrow.active::after {
  color: #0f6aff;
  transform: rotate(-135deg) translate(0, -50%);
}
.sidebar .sidebar-menu .sidebar-menu-section .sidebar-menu-item .mm-show {
  background: rgba(115, 218, 255, 0.12);
}
.sidebar .sidebar-menu .sidebar-menu-section .mm-active > a {
  background: linear-gradient(90deg, #0f6aff 0%, #21c6fb 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  border-left: 2px solid #0f6aff;
  background-color: rgba(115, 218, 255, 0.3);
}
.sidebar .sidebar-menu .sidebar-menu-section .mm-active > a::before {
  background: linear-gradient(90deg, #0f6aff 0%, #21c6fb 100%);
}
.sidebar .sidebar-menu .sidebar-menu-section .mm-active > a.has-arrow::after {
  color: #0f6aff;
}
.sidebar .sidebar-menu .sidebar-menu-section ul > li > a {
  padding-left: 28px;
}
.sidebar .sidebar-menu .sidebar-menu-section .mm-active ul {
  background: rgba(115, 218, 255, 0.12);
}

.sidebar-expand .sidebar {
  left: 0;
  transition: left 0.3s;
}

/*Changes while RTL Start*/
.rtl .half-expand {
  /* Sidebar Header */
  /* main content on half expand */
}
.rtl .half-expand .sidebar .sidebar-header {
  /* Sidebar Logo */
  /* Sidebar Half Expand Icon*/
}
.rtl .half-expand .sidebar .sidebar-header .half-expand-toggle {
  position: absolute;
  right: 100%;
  margin-right: 10px;
}
.rtl .half-expand .sidebar .sidebar-header .half-expand-toggle img {
  transform: rotate(180deg);
}
.rtl .half-expand .sidebar .sidebar-menu {
  overflow: visible;
}
.rtl .half-expand .sidebar .sidebar-menu .sidebar-menu-section-heading {
  margin-right: 0;
}
.rtl
  .half-expand
  .sidebar
  .sidebar-menu
  .parent-menu-list
  .sidebar-menu-item
  .parent-item-content
  span {
  display: none;
}
.rtl
  .half-expand
  .sidebar
  .sidebar-menu
  .parent-menu-list
  .sidebar-menu-item
  a {
  border: none;
}
.rtl .half-expand .sidebar .sidebar-menu-item > ul > li > a {
  padding-left: 10px !important;
}
.rtl .half-expand .sidebar .sidebar-menu-item > ul > li > ul > li > a {
  padding-left: 30px !important;
}
.rtl .half-expand .sidebar ul.parent-menu-list > li > ul {
  position: absolute;
  top: 0;
  width: 236px;
  left: 100px;
  box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.04);
  border-radius: 10px;
}
.rtl .half-expand .theme-switch {
  background: #edfaff;
  z-index: 3000000;
  position: absolute;
  bottom: 0;
}
.rtl .half-expand .sidebar-header {
  background: #edfaff;
  z-index: 3000000;
}
.rtl .half-expand .parent-menu-list > li > ul {
  right: 85px !important;
}
.rtl .half-expand .sidebar-menu {
  position: absolute;
  left: 50%;
  top: 10%;
  z-index: 2000;
  overflow: hide;
  height: calc(100vh - 100px);
  transform: translateX(-50%);
}
.rtl .half-expand .main-content {
  margin-right: 100px;
  transition: -webkit-margin-right 0.3s;
  transition: margin-right 0.3s;
  transition: margin-right 0.3s, -webkit-margin-right 0.3s;
}

/*Changes while RTL End*/
.half-expand {
  /* Sidebar Header */
  /* main content on half expand */
}
.half-expand .sidebar {
  width: 100px;
}
.half-expand .sidebar .sidebar-header {
  position: relative;
  /* Sidebar Logo */
  /* Sidebar Half Expand Icon*/
}
.half-expand .sidebar .sidebar-header .sidebar-logo {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.half-expand .sidebar .sidebar-header .sidebar-logo a .half-logo {
  opacity: 1;
  visibility: visible;
  height: auto;
  transition: -webkit- opacity 0.4s;
  transition: opacity 0.4s;
  transition: opacity 0.4s, -webkit- opacity 0.4s;
}
.half-expand .sidebar .sidebar-header .sidebar-logo a .full-logo {
  opacity: 0;
  visibility: hidden;
  height: 0;
  transition: -webkit- opacity 0.4s;
  transition: opacity 0.4s;
  transition: opacity 0.4s, -webkit- opacity 0.4s;
}
.half-expand .sidebar .sidebar-header .half-expand-toggle {
  position: absolute;
  left: 100%;
  margin-left: 10px;
}
.half-expand .sidebar .sidebar-header .half-expand-toggle img {
  transform: rotate(180deg);
}
.half-expand .sidebar .sidebar-header .sidebar-close {
  border: 0;
  outline: 0;
  display: none;
  background: 0;
  padding: 0;
  margin-right: 10px;
  font-size: 28px;
}
@media (max-width: 768px) {
  .half-expand .sidebar .sidebar-header .sidebar-close {
    display: block;
  }
}
.half-expand .sidebar .sidebar-header .sidebar-close:hover {
  color: #edfaff;
}
.half-expand .sidebar .sidebar-menu {
  overflow: visible;
}
.half-expand .sidebar .sidebar-menu .sidebar-menu-section-heading {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 0;
}
.half-expand
  .sidebar
  .sidebar-menu
  .sidebar-menu-section-heading
  .on-half-expanded {
  display: none;
}
.half-expand .sidebar .sidebar-menu .parent-menu-list .sidebar-menu-item {
  position: relative;
}
.half-expand
  .sidebar
  .sidebar-menu
  .parent-menu-list
  .sidebar-menu-item
  .parent-item-content {
  padding: 15px 0;
  display: flex;
  align-items: center;
  justify-content: center;
}
.half-expand
  .sidebar
  .sidebar-menu
  .parent-menu-list
  .sidebar-menu-item
  .parent-item-content::after {
  content: none;
}
.half-expand
  .sidebar
  .sidebar-menu
  .parent-menu-list
  .sidebar-menu-item
  .parent-item-content
  span {
  display: none;
}
.half-expand .sidebar .sidebar-menu .parent-menu-list .sidebar-menu-item a {
  border: none;
}
.half-expand .sidebar .sidebar-menu-item > ul > li > a {
  padding-left: 10px !important;
}
.half-expand .sidebar .sidebar-menu-item > ul > li > ul > li > a {
  padding-left: 30px !important;
}
.half-expand .sidebar ul.parent-menu-list > li > ul {
  position: absolute;
  top: 0;
  width: 236px;
  left: 100px;
  background: #fcfcfc !important;
  border: 1px solid #eaeaea;
  box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.04);
  border-radius: 10px;
}
.half-expand .theme-switch {
  background: var(--ot-bg-secondary);
  z-index: 3000000;
  position: absolute;
  bottom: 0;
}
.half-expand .sidebar-header {
  background: var(--ot-bg-secondary);
  z-index: 3000000;
}
.half-expand .parent-menu-list > li > ul {
  left: 85px !important;
}
.half-expand .sidebar-menu {
  position: absolute;
  left: 50%;
  top: 10%;
  z-index: 2000;
  overflow: hide;
  height: calc(100vh - 100px);
  transform: translateX(-50%);
}
.half-expand .main-content {
  margin-left: 100px;
  transition: -webkit-margin-left 0.3s;
  transition: margin-left 0.3s;
  transition: margin-left 0.3s, -webkit-margin-left 0.3s;
}

/* Dark Theme start*/
.dark-theme .half-expand ul.parent-menu-list > li > ul {
  background: var(--ot-bg-secondary) !important;
  border: none;
}

/* Dark Theme end*/
/* Dark Theme Start Start*/
.dark-theme .sidebar-menu .sidebar-menu-section .mm-active > a {
  background: linear-gradient(90deg, #0f6aff 0%, #21c6fb 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  border-left: 2px solid #0f6aff;
  background-color: var(--ot-bg-secondary-opacity);
}
.dark-theme .sidebar-menu .sidebar-menu-section .mm-active > a::before {
  background: linear-gradient(90deg, #0f6aff 0%, #21c6fb 100%);
}
.dark-theme .sidebar-menu .sidebar-menu-section .mm-active ul {
  background-color: var(--ot-bg-primary);
}

/* Dark Theme Start End*/
/* Theme Switch */
/* dark theming start */
.dark-theme .theme-switch::before {
  background: #272b30;
}

/* dark theming end */
/* When Half Expand start */
.half-expand .theme-switch .switch-field {
  justify-content: center;
}
.half-expand .theme-switch .switch-field label {
  width: 34px;
  height: 34px;
  justify-content: center;
}
.half-expand .theme-switch .switch-field label p {
  display: none;
}

/* When Half Expand start*/
.theme-switch {
  height: 10%;
  width: 100%;
  display: flex;
  align-items: center;
  padding: 5px 10px;
  position: relative;
}
.theme-switch::before {
  content: "";
  width: 80%;
  height: 2px;
  border-radius: 10px;
  background: #fcfcfc;
  margin-right: 10px;
  position: absolute;
  top: 5px;
}
.theme-switch .switch-field {
  overflow: hidden;
  display: flex;
  align-items: center;
  background: var(--ot-bg-secondary);
  padding: 10px;
  border-radius: 2rem;
}
.theme-switch .switch-field input {
  display: none;
}
.theme-switch .switch-field label {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: transparent;
  color: #333;
  font-size: 1em;
  font-weight: normal;
  text-align: center;
  text-shadow: none;
  padding: 0.5em 1em;
  transition: all 0.1s ease-in-out;
  border-radius: 2rem;
  cursor: pointer;
  color: var(--ot-text-primary);
}
.theme-switch .switch-field label p {
  margin: 0;
  font-weight: 600;
}
.theme-switch .switch-field:hover {
  cursor: pointer;
}
.theme-switch .switch-field input:checked + label {
  background: linear-gradient(90deg, #0f6aff 0%, #21c6fb 100%);
  box-shadow: 0px 2px 2px rgba(0, 0, 0, 0.04);
  border-radius: 50px;
}
.theme-switch .switch-field input:checked + label p {
  color: #fff;
}

.dark-theme .theme-switch .switch-field {
  background-color: var(--ot-bg-primary);
}

/* Main Content */
/*Changes while RTL Start*/
body.rtl .main-content {
  margin-right: 256px;
  margin-left: 0;
  transition: -webkit-right 0.3s;
  transition: margin-right 0.3s;
  transition: margin-right 0.3s, -webkit-margin-right 0.3s;
}
@media (max-width: 768px) {
  body.rtl .main-content {
    width: 100%;
    margin-right: 0;
    transition: -webkit-left 0.3s;
    transition: margin-right 0.3s;
    transition: margin-right 0.3s, -webkit-margin-right 0.3s;
  }
}

/*Changes while RTL Start*/
.main-content {
  margin-left: 256px;
  transition: -webkit-left 0.3s;
  transition: margin-left 0.3s;
  transition: margin-left 0.3s, -webkit-margin-left 0.3s;
}
@media (max-width: 992px) {
  .main-content {
    width: 100%;
    margin-left: 0;
    transition: -webkit-left 0.3s;
    transition: margin-left 0.3s;
    transition: margin-left 0.3s, -webkit-margin-left 0.3s;
  }
}
.main-content .dashboard-heading .title {
  font-weight: 700;
  font-size: 24px;
  line-height: 34px;
}

/* Common Card */
/* Common card for layout for dashboard */
.ot-card {
  background-color: var(--ot-bg-secondary);
  filter: drop-shadow(0px 2px 8px rgba(0, 0, 0, 0.05));
  border-radius: 5px;
  padding: 28px;
  border: none;
}
.ot-card .card-header,
.ot-card .card-body,
.ot-card .card-footer {
  padding: 0;
  margin: 0;
  background: transparent;
  border: none;
}
.ot-card .card-body {
  padding-top: 20px;
}

/* Summery Card */
/* Summery Card */
.summery-card .card-heading .card-icon {
  width: 55px;
  height: 55px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}
.summery-card .card-heading .card-icon.icon-circle-1 {
  background-color: rgba(172, 224, 195, 0.1);
}
.summery-card .card-heading .card-icon.icon-circle-2 {
  background-color: rgba(194, 183, 252, 0.1);
}
.summery-card .card-heading .card-icon.icon-circle-3 {
  background-color: rgba(254, 179, 145, 0.1);
}
.summery-card .card-heading .card-icon.icon-circle-4 {
  background-color: rgba(168, 225, 249, 0.1);
}
.summery-card .card-heading .card-content {
  margin-left: 16px;
  /* rtl */
}
.rtl .summery-card .card-heading .card-content {
  margin-left: 0;
  margin-right: 16px;
}
.summery-card .card-heading .card-content h4 {
  font-weight: 600;
  font-size: 12px;
  line-height: 15px;
  margin-bottom: 0px;
  color: var(--ot-text-subtitle);
}
.summery-card .card-heading .card-content h1 {
  font-weight: 600;
  font-size: 30px;
  line-height: 42px;
  margin-top: 0px;
  color: var(--ot-primary-text);
}
.summery-card .card-bottom .card-states {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 10px;
}
.summery-card .card-bottom .card-states .card-badge {
  border-radius: 50px;
  padding: 4px 10px;
  gap: 10px;
  font-size: 12px;
  font-weight: 600;
  color: var(--ot-primary-text);
}
.summery-card .card-bottom .card-states .card-badge:first-child {
  background-color: #e9faf4;
}
.dark-theme .summery-card .card-bottom .card-states .card-badge:first-child {
  background-color: rgba(233, 250, 244, 0.1);
}
.summery-card .card-bottom .card-states .card-badge:first-child .count {
  color: #7ab668;
}
.summery-card .card-bottom .card-states .card-badge:last-child {
  background-color: #fff0ed;
}
.dark-theme .summery-card .card-bottom .card-states .card-badge:last-child {
  background-color: rgba(255, 240, 237, 0.1);
}
.summery-card .card-bottom .card-states .card-badge:last-child .count {
  color: #ff6a54;
}

.chart-custom-content p {
  font-weight: 400;
  font-size: 12px;
  line-height: 21px;
}
.chart-custom-content h2 {
  font-weight: 600;
  font-size: 20px;
  line-height: 24px;
  color: #1a1d1f;
}
.chart-custom-content .percentage-spike img {
  width: 12px;
  height: 12px;
}
.chart-custom-content .percentage-spike .primary-blue {
  color: #5669ff;
}
.chart-custom-content .percentage-spike .secondary-blue {
  color: #55b1f3;
}
.chart-custom-content .percentage-spike .primary-red {
  color: #ff4f75;
}
.chart-custom-content .percentage-spike .primary-yellow {
  color: #ff991a;
}
.chart-custom-content .percentage-spike:nth-child(1) {
  background-color: #feb391;
}

.chart-header p {
  font-weight: 500;
  font-size: 12px;
  line-height: 15px;
  color: var(--ot-text-primary);
}

.statistics-card .card .card-bottom .card-states .btn.active-state-btn {
  background-color: #e9faf4;
}

.statistics-card .card .card-bottom .card-states .btn.active-state-btn .count {
  color: #24c087;
}

.statistics-card .card .card-bottom .card-states .btn.active-state-btn .status {
  color: var(--ot-text-primary);
}

.statistics-card .card .card-bottom .card-states .btn.inactive-state-btn {
  background-color: #fff0ed;
}

.statistics-card
  .card
  .card-bottom
  .card-states
  .btn.inactive-state-btn
  .count {
  color: var(#e55f4b);
}

.statistics-card
  .card
  .card-bottom
  .card-states
  .btn.inactive-state-btn
  .status {
  color: var(--ot-text-primary);
}

/* statistics-charts  */
.statistics-chart .visited-customer .card .card-top .title h4 {
  color: var(--ot-primary-text);
  font-weight: 600;
  font-size: 20px;
  line-height: 32px;
}

.statistics-chart .visited-customer .card .card-top {
  margin-bottom: 15px;
}

.statistics-chart .visited-customer .card .card-top .card-content .stats {
  font-weight: 600;
}

.statistics-chart
  .visited-customer
  .card
  .card-top
  .card-content
  .stats
  span.vs {
  margin-left: 12px;
}

.statistics-chart
  .visited-customer
  .card
  .card-top
  .card-content
  .btn.active-state-btn {
  background-color: #e9faf4;
}

.statistics-chart
  .visited-customer
  .card
  .card-top
  .card-content
  .btn.active-state-btn
  .count {
  color: var(--success-dark);
}

/* .statistics-chart #visited_customer_chart{
    width: 400px;
    height: 200px;
} */
/* Datatable */
.table-toolbar {
  font-family: "Lexend", sans-serif;
  font-style: normal;
  font-size: 14px;
  font-weight: 500;
  line-height: 18px;
  color: #000000;
}
.table-toolbar .form-select {
  font-family: "Lexend", sans-serif;
  font-style: normal;
  font-size: 14px;
  font-weight: 500;
  line-height: 18px;
  color: var(--ot-primary-text);
  width: auto;
  padding: 10px 30px 10px 14px;
  border: 1px solid var(--ot-border-table-toolbar-per-page);
  border-radius: 5px;
  background-repeat: no-repeat;
  background-position: right 14px center;
  background-size: 12px;
  outline: 0;
  box-shadow: none;
  background-color: var(--ot-bg-table-toolbar-per-page);
}
.table-toolbar .btn-add {
  text-decoration: none;
  padding: 10px 17px;
  border: 1px solid #21c6fb;
  background: linear-gradient(90deg, #0f6aff 0%, #21c6fb 100%);
  border-radius: 5px;
  display: block;
  text-align: center;
  font-family: "Lexend", sans-serif;
  font-style: normal;
  font-size: 14px;
  font-weight: 600;
  line-height: 18px;
  color: #ffffff;
}
.table-toolbar .btn-daterange,
.table-toolbar .dropdown-export .btn-export,
.table-toolbar .dropdown-designation .btn-designation {
  background-image: linear-gradient(90deg, #0f6aff 0%, #21c6fb 100%),
    linear-gradient(90deg, #0f6aff 0%, #21c6fb 100%);
  background-origin: border-box;
  background-clip: content-box, border-box;
  box-shadow: 2px 1000px 1px var(--ot-bg-table-toolbar-btn-outline-primary)
    inset;
  font-family: "Lexend", sans-serif;
  font-style: normal;
  font-size: 14px;
  font-weight: 600;
  line-height: 18px;
  color: var(--ot-text-primary);
  border: 1px solid transparent;
  padding: 10px 12px;
  border-radius: 5px;
  transition: all ease-in-out 0.1s;
}
.table-toolbar .btn-daterange .icon,
.table-toolbar .dropdown-export .btn-export .icon,
.table-toolbar .dropdown-designation .btn-designation .icon {
  color: #21c6fb;
  font-size: 14px;
}
.table-toolbar .btn-daterange:hover,
.table-toolbar .dropdown-export .btn-export:hover,
.table-toolbar .dropdown-designation .btn-designation:hover {
  box-shadow: none;
  background: linear-gradient(90deg, #0f6aff 0%, #21c6fb 100%);
  color: #ffffff;
}
.table-toolbar .btn-daterange:hover .icon,
.table-toolbar .dropdown-export .btn-export:hover .icon,

  .table-toolbar
  .dropdown-designation
  .btn-designation:hover
  .icon {
  color: #ffffff;
}
.table-toolbar .btn-daterange:focus,
.table-toolbar .dropdown-export .btn-export:focus,
.table-toolbar .dropdown-designation .btn-designation:focus {
  box-shadow: none;
  background: linear-gradient(90deg, #0f6aff 0%, #21c6fb 100%);
  color: #ffffff;
}
.table-toolbar .btn-daterange:focus .icon,
.table-toolbar .dropdown-export .btn-export:focus .icon,

  .table-toolbar
  .dropdown-designation
  .btn-designation:focus
  .icon {
  color: #ffffff;
}
.table-toolbar .search-box {
  position: relative;
  border: 1px solid var(--ot-border-table-toolbar-search);
  border-radius: 5px;
}
.table-toolbar .search-box .form-control,

  .table-toolbar
  .search-box
  .profile-content
  .profile-body-form
  .form-box
  .form-select,
.profile-content
  .profile-body-form
  .form-box
  
  .table-toolbar
  .search-box
  .form-select {
  padding: 8px 45px 8px 16px;
  border: 1px solid transparent;
  box-shadow: none;
  background-color: var(--ot-bg-table-toolbar-search);
  color: var(--ot-primary-text);
}
.table-toolbar .search-box .form-control::-moz-placeholder,

  .table-toolbar
  .search-box
  .profile-content
  .profile-body-form
  .form-box
  .form-select::-moz-placeholder,
.profile-content
  .profile-body-form
  .form-box
  
  .table-toolbar
  .search-box
  .form-select::-moz-placeholder {
  font-family: "Lexend", sans-serif;
  font-style: normal;
  font-size: 14px;
  font-weight: 400;
  line-height: 16px;
  color: #b2bec3;
}
.table-toolbar .search-box .form-control:-ms-input-placeholder,

  .table-toolbar
  .search-box
  .profile-content
  .profile-body-form
  .form-box
  .form-select:-ms-input-placeholder,
.profile-content
  .profile-body-form
  .form-box
  
  .table-toolbar
  .search-box
  .form-select:-ms-input-placeholder {
  font-family: "Lexend", sans-serif;
  font-style: normal;
  font-size: 14px;
  font-weight: 400;
  line-height: 16px;
  color: #b2bec3;
}
.table-toolbar .search-box .form-control::placeholder,

  .table-toolbar
  .search-box
  .profile-content
  .profile-body-form
  .form-box
  .form-select::placeholder,
.profile-content
  .profile-body-form
  .form-box
  
  .table-toolbar
  .search-box
  .form-select::placeholder {
  font-family: "Lexend", sans-serif;
  font-style: normal;
  font-size: 14px;
  font-weight: 400;
  line-height: 16px;
  color: #b2bec3;
}
.table-toolbar .search-box .form-control:focus-visible,

  .table-toolbar
  .search-box
  .profile-content
  .profile-body-form
  .form-box
  .form-select:focus-visible,
.profile-content
  .profile-body-form
  .form-box
  
  .table-toolbar
  .search-box
  .form-select:focus-visible {
  box-shadow: 0px 0px 10px rgba(10, 175, 255, 0.35);
  border: 1px solid #5669ff !important;
}
.table-toolbar .search-box .icon {
  position: absolute;
  top: 50%;
  right: 12px;
  transform: translateY(-50%);
  color: #6f767e;
}
.table-toolbar .dropdown-designation .dropdown-menu {
  background: var(--ot-bg-secondary);
  border: 1px solid var(--ot-border-table-toolbar-btn-action);
  position: relative;
  color: var(--ot-primary-text) !important;
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.05);
  border-radius: 7px;
  margin-top: 10px !important;
  padding: 0;
}
.table-toolbar .dropdown-designation .search-content {
  padding: 20px;
}

  .table-toolbar
  .dropdown-designation
  .search-content
  .search-box {
  position: relative;
  border: 1px solid #eaeaea;
  border-radius: 50px;
}

  .table-toolbar
  .dropdown-designation
  .search-content
  .search-box
  .form-control,

  .table-toolbar
  .dropdown-designation
  .search-content
  .search-box
  .profile-content
  .profile-body-form
  .form-box
  .form-select,
.profile-content
  .profile-body-form
  .form-box
  
  .table-toolbar
  .dropdown-designation
  .search-content
  .search-box
  .form-select {
  width: 232px;
  padding: 8px 45px 8px 16px;
  border: none;
  border-radius: 50px;
  box-shadow: none;
  background-color: #ffffff;
  color: #1a1d1f;
}

  .table-toolbar
  .dropdown-designation
  .search-content
  .search-box
  .form-control::-moz-placeholder,

  .table-toolbar
  .dropdown-designation
  .search-content
  .search-box
  .profile-content
  .profile-body-form
  .form-box
  .form-select::-moz-placeholder,
.profile-content
  .profile-body-form
  .form-box
  
  .table-toolbar
  .dropdown-designation
  .search-content
  .search-box
  .form-select::-moz-placeholder {
  font-family: "Lexend", sans-serif;
  font-style: normal;
  font-size: 14px;
  font-weight: 400;
  line-height: 16px;
  color: #b2bec3;
}

  .table-toolbar
  .dropdown-designation
  .search-content
  .search-box
  .form-control:-ms-input-placeholder,

  .table-toolbar
  .dropdown-designation
  .search-content
  .search-box
  .profile-content
  .profile-body-form
  .form-box
  .form-select:-ms-input-placeholder,
.profile-content
  .profile-body-form
  .form-box
  
  .table-toolbar
  .dropdown-designation
  .search-content
  .search-box
  .form-select:-ms-input-placeholder {
  font-family: "Lexend", sans-serif;
  font-style: normal;
  font-size: 14px;
  font-weight: 400;
  line-height: 16px;
  color: #b2bec3;
}

  .table-toolbar
  .dropdown-designation
  .search-content
  .search-box
  .form-control::placeholder,

  .table-toolbar
  .dropdown-designation
  .search-content
  .search-box
  .profile-content
  .profile-body-form
  .form-box
  .form-select::placeholder,
.profile-content
  .profile-body-form
  .form-box
  
  .table-toolbar
  .dropdown-designation
  .search-content
  .search-box
  .form-select::placeholder {
  font-family: "Lexend", sans-serif;
  font-style: normal;
  font-size: 14px;
  font-weight: 400;
  line-height: 16px;
  color: #b2bec3;
}

  .table-toolbar
  .dropdown-designation
  .search-content
  .search-box
  .icon {
  position: absolute;
  top: 50%;
  right: 12px;
  transform: translateY(-50%);
  color: #6f767e;
}
.table-toolbar .dropdown-designation .list {
  padding: 26px;
  margin: 0;
}
.table-toolbar .dropdown-designation .list .list-item {
  list-style: none;
  margin-bottom: 24px;
}

  .table-toolbar
  .dropdown-designation
  .list
  .list-item:last-child {
  margin-bottom: 0;
}
.table-toolbar .dropdown-designation .dropdown-item {
  padding: 0 0;
  display: block;
  font-family: "Lexend", sans-serif;
  font-style: normal;
  font-size: 14px;
  font-weight: 400;
  line-height: 16px;
  color: var(--ot-text-primary);
}
.table-toolbar .dropdown-designation .dropdown-item:hover {
  background-color: var(--ot-bg-secondary);
  color: var(--ot-primary-text);
}
.table-toolbar .dropdown-designation .dropdown-item:focus {
  background-color: var(--ot-bg-secondary);
  color: var(--ot-primary-text);
}
.table-toolbar .dropdown-designation .dropdown-item:active {
  background-color: var(--ot-bg-secondary);
  color: var(--ot-primary-text);
}
.table-toolbar .dropdown-designation .dropdown-item .icon {
  color: #0f6aff;
  font-size: 14px;
}
.table-toolbar .dropdown-action .btn-dropdown {
  border: 1px solid var(--ot-border-table-toolbar-btn-action);
  width: 40px;
  height: 40px;
  background: var(--ot-bg-table-toolbar-btn-action);
  box-shadow: 1px 1px 8px rgba(16, 108, 255, 0.12);
  border-radius: 50px;
  color: #0f6aff;
  font-size: 16px;
}
.table-toolbar .dropdown-action .btn-dropdown:focus {
  background: linear-gradient(90deg, #0f6aff 0%, #21c6fb 100%);
  box-shadow: 1px 1px 8px rgba(16, 108, 255, 0.12);
  color: #ffffff;
}
.table-toolbar .dropdown-action .dropdown-menu {
  position: relative;
  background: var(--ot-bg-secondary);
  border: 1px solid var(--ot-border-table-toolbar-btn-action);
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.05);
  border-radius: 7px;
  padding: 24px;
  margin-top: 10px !important;
}
.table-toolbar .dropdown-action .dropdown-menu::before {
  content: "";
  position: absolute;
  top: -5px;
  right: 8px;
  background: var(--ot-bg-secondary);
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.05);
  transform: rotate(45deg);
  padding: 10px;
  border: 1px solid var(--ot-border-table-toolbar-btn-action);
}
.table-toolbar .dropdown-action .dropdown-menu::after {
  content: "";
  position: absolute;
  top: 0;
  right: 5px;
  background: var(--ot-bg-secondary);
  padding: 10px 15px;
}
.table-toolbar .dropdown-action .dropdown-menu li {
  margin-bottom: 8px;
}
.table-toolbar .dropdown-action .dropdown-menu li:last-child {
  margin-bottom: 0;
}
.table-toolbar .dropdown-action .dropdown-item {
  padding: 0 0;
  display: block;
  font-family: "Lexend", sans-serif;
  font-style: normal;
  font-size: 14px;
  font-weight: 500;
  line-height: 28px;
  color: var(--ot-text-primary);
}
.table-toolbar .dropdown-action .dropdown-item:hover {
  background: var(--ot-bg-secondary);
  color: var(--ot-primary-text);
}
.table-toolbar .dropdown-action .dropdown-item:focus {
  background: var(--ot-bg-secondary);
  color: var(--ot-primary-text);
}
.table-toolbar .dropdown-action .dropdown-item:active {
  background: var(--ot-bg-secondary);
  color: var(--ot-primary-text);
}
.table-toolbar .dropdown-action .dropdown-item .icon {
  color: #0f6aff;
  font-size: 14px;
}
.table-toolbar .dropdown-export .dropdown-menu {
  position: relative;
  background-color: var(--ot-bg-secondary);
  border: 1px solid var(--ot-primary-border);
  color: var(--ot-primary-text);
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.05);
  border-radius: 7px;
  padding: 24px;
  margin-top: 10px !important;
}
.table-toolbar .dropdown-export .dropdown-menu li {
  margin-bottom: 8px;
}
.table-toolbar .dropdown-export .dropdown-menu li:last-child {
  margin-bottom: 0;
}
.table-toolbar .dropdown-export .dropdown-item {
  padding: 0 0;
  display: block;
  font-family: "Lexend", sans-serif;
  font-style: normal;
  font-size: 14px;
  font-weight: 500;
  line-height: 28px;
  color: var(--ot-text-primary);
}
.table-toolbar .dropdown-export .dropdown-item:hover {
  background-color: var(--ot-bg-secondary);
  color: var(--ot-primary-text);
}
.table-toolbar .dropdown-export .dropdown-item:focus {
  background-color: var(--ot-bg-secondary);
  color: var(--ot-primary-text);
}
.table-toolbar .dropdown-export .dropdown-item:active {
  background-color: var(--ot-bg-secondary);
  color: var(--ot-primary-text);
}
.table-toolbar .dropdown-export .dropdown-item .icon {
  color: #0f6aff;
  font-size: 14px;
}
 .card {
  background-color: var(--ot-bg-table-card);
  border: 1px solid transparent;
  filter: drop-shadow(0px 2px 8px rgba(0, 0, 0, 0.05));
  border-radius: 0;
}
 .card-body {
  padding: 40px 40px;
}
 .card-header {
  font-family: "Lexend", sans-serif;
  font-style: normal;
  font-size: 18px;
  font-weight: 700;
  line-height: 30px;
  color: var(--ot-primary-text);
  padding: 15px 40px;
  border-bottom: 1px solid var(--ot-border-table-card-header);
  background-color: var(--ot-bg-table-card-header);
  border-radius: 0;
}
 .table {
  border-color: var(--ot-border-table);
  vertical-align: middle;
  margin: 0;
}
 .table .sorting_asc,
 .table .sorting_desc {
  position: relative;
}
 .table .sorting_asc::before,
 .table .sorting_desc::before {
  position: absolute;
  left: auto;
  right: 16px;
  content: "\f106";
  font-family: "Font Awesome 6 Free";
  font-size: 12px;
  font-weight: 900;
  top: 42%;
  color: var(--ot-color-table-icon-sorting-asc-up);
  cursor: pointer;
  transform: translateY(-42%);
}
 .table .sorting_asc::after,
 .table .sorting_desc::after {
  position: absolute;
  left: auto;
  right: 16px;
  content: "\f107";
  font-family: "Font Awesome 6 Free";
  font-size: 12px;
  font-weight: 900;
  top: 62%;
  color: var(--ot-color-table-icon-sorting-asc-down);
  cursor: pointer;
  transform: translateY(-62%);
}
 .table .sorting_desc::before {
  color: var(--ot-color-table-icon-sorting-desc-up);
}
 .table .sorting_desc::after {
  color: var(--ot-color-table-icon-sorting-desc-down);
}
 .table .check-box .form-check-input {
  width: 20px;
  height: 20px;
  border-radius: 5px;
  border: 1px solid var(--ot-border-table-checkbox);
  background-color: var(--ot-bg-table-checkbox);
}
 .table .check-box .form-check-input:focus {
  box-shadow: none;
}
 .table .check-box .form-check-input:checked {
  background-repeat: no-repeat;
  background-position: center;
}
 .table .thead {
  background: transparent;
  border-bottom-color: var(--ot-border-table-thead);
}
 .table .thead tr th {
  font-family: "Lexend", sans-serif;
  font-style: normal;
  font-size: 14px;
  font-weight: 500;
  line-height: 21px;
  color: var(--ot-primary-text);
  padding: 16px 32px 16px 16px;
  vertical-align: middle;
  white-space: nowrap;
  border-color: var(--ot-border-table-thead);
}
 .table .tbody tr:nth-of-type(odd) {
  background: var(--ot-bg-table-tbody);
}
 .table .tbody tr td {
  font-family: "Lexend", sans-serif;
  font-style: normal;
  font-size: 12px;
  font-weight: 500;
  line-height: 21px;
  color: var(--ot-text-primary);
  padding: 16px;
  vertical-align: middle;
  white-space: nowrap;
}
 .table .dropdown-action .btn-dropdown {
  border: 1px solid var(--ot-border-table-toolbar-btn-action);
  width: 28px;
  height: 28px;
  background: #0f6aff;
  box-shadow: 1px 1px 8px rgba(16, 108, 255, 0.12);
  border-radius: 50px;
  color: var(--ot-bg-table-toolbar-btn-action);
  font-size: 10px;
}
 .table .dropdown-action .btn-dropdown:focus {
  background: linear-gradient(90deg, #0f6aff 0%, #21c6fb 100%);
  box-shadow: 1px 1px 8px rgba(16, 108, 255, 0.12);
  color: #ffffff;
}
 .table .dropdown-action .dropdown-menu {
  background-color: var(--ot-bg-secondary) !important;
  border: 1px solid var(--ot-primary-border) !important;
  color: var(--ot-primary-text) !important;
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.05);
  border-radius: 7px;
  padding: 24px;
  margin-top: 10px !important;
}
 .table .dropdown-action .dropdown-menu li {
  margin-bottom: 8px;
}

  .table
  .dropdown-action
  .dropdown-menu
  li:last-child {
  margin-bottom: 0;
}
 .table .dropdown-action .dropdown-item {
  padding: 0 0;
  display: block;
  font-family: "Lexend", sans-serif;
  font-style: normal;
  font-size: 14px;
  font-weight: 500;
  line-height: 28px;
  color: var(--ot-text-primary);
}
 .table .dropdown-action .dropdown-item:hover {
  background-color: var(--ot-bg-secondary);
  color: var(--ot-primary-text);
}
 .table .dropdown-action .dropdown-item:focus {
  background-color: var(--ot-bg-secondary);
  color: var(--ot-primary-text);
}
 .table .dropdown-action .dropdown-item:active {
  background-color: var(--ot-bg-secondary);
  color: var(--ot-primary-text);
}
 .table .dropdown-action .dropdown-item .icon {
  color: #0f6aff;
  font-size: 14px;
}

/* Charts */
.statistic-card .card-heading .card-title {
  font-weight: 600;
  font-size: 20px;
  line-height: 32px;
  color: #1a1d1f;
  margin: 0;
  padding: 0;
}

/* Common Buttons */
.ot-dropdown-btn {
  background: linear-gradient(90deg, #0f6aff 0%, #21c6fb 100%);
  border-radius: 5px;
  padding: 8px 14px;
  border: 0;
  outline: 0 !important;
  display: flex;
  align-items: center;
  gap: 13px;
}
.ot-dropdown-btn::after {
  content: none;
}

.ot-btn-primary,
.ot-btn-info,
.ot-btn-warning,
.ot-btn-danger,
.ot-btn-success {
  color: #ffffff !important;
  border: 1px solid #21c6fb;
  background: linear-gradient(90deg, #0f6aff 0%, #21c6fb 100%);
  border-radius: 5px;
}

.ot-btn-success {
  color: #ffffff !important;
  border: 1px solid #00ffa2;
  background: linear-gradient(90deg, #29d697 0%, #00ffa2 100%);
}

.ot-btn-danger {
  color: #ffffff !important;
  border: 1px solid #fd2201;
  background: linear-gradient(90deg, #ff6a54 0%, #fd2201 100%);
}

.ot-btn-warning {
  color: #ffffff !important;
  border: 1px solid #fec403;
  background: linear-gradient(90deg, #fdc400 0%, #fec403 100%);
}

.ot-btn-info {
  color: #ffffff !important;
  border: 1px solid #00ddff;
  background: linear-gradient(90deg, #138496 0%, #00ddff 100%);
}

/* Tables */
.badge-basic-success-text {
  font-family: "Lexend";
  font-weight: 600;
  font-size: 12px;
  border-radius: 50px;
  padding: 4px 10px;
  background: var(--ot-bg-badge-light-success);
  color: var(--ot-text-badge-deep-success);
}

.badge-basic-danger-text {
  font-family: "Lexend";
  font-weight: 600;
  font-size: 12px;
  border-radius: 50px;
  padding: 4px 10px;
  background: var(--ot-bg-badge-light-danger);
  color: var(--ot-text-badge-deep-danger);
}

.badge-basic-warning-text {
  font-family: "Lexend";
  font-weight: 600;
  font-size: 12px;
  border-radius: 50px;
  padding: 4px 10px;
  background: var(--ot-bg-badge-light-warning);
  color: var(--ot-text-badge-deep-warning);
}

.badge-basic-info-text {
  font-family: "Lexend";
  font-weight: 600;
  font-size: 12px;
  border-radius: 50px;
  padding: 4px 10px;
  background: #e5feff;
  color: #00dfe5;
}

.badge-basic-primary-text {
  font-family: "Lexend";
  font-weight: 600;
  font-size: 12px;
  border-radius: 50px;
  padding: 4px 10px;
  background: #eef0ff;
  color: #5669ff;
}

.page-heading h1 {
  font-size: 30px;
  line-height: 38px;
  color: #1a1d1f;
}

.component-page-heading h1 {
  font-weight: 600;
  font-size: 20px;
  line-height: 32px;
}

.basic-table,
.table-color-col,
.ot-table-bg {
  width: 100%;
}
.basic-table thead th,
.table-color-col thead th,
.ot-table-bg thead th {
  height: 56px;
  border-bottom: 1px solid #e9e9ef;
  padding-left: 16px;
  color: #1a1d1f;
}
.basic-table thead th:first-child,
.table-color-col thead th:first-child,
.ot-table-bg thead th:first-child {
  border-radius: 10px 0 0 0;
}
.basic-table thead th:last-child,
.table-color-col thead th:last-child,
.ot-table-bg thead th:last-child {
  border-radius: 0 10px 0 0;
}
.basic-table tbody td,
.table-color-col tbody td,
.ot-table-bg tbody td {
  font-weight: 400;
  padding-left: 16px;
  height: 56px;
  color: #6f767e;
}

.ot-table-bg th,
.ot-table-bg tr:nth-child(even) {
  background-color: #f7f7f7;
}

.table-color-col th:nth-child(odd),
.table-color-col td:nth-child(odd) {
  background-color: #f7f7f7;
}
.table-color-col td {
  border-bottom: 1px solid #f5f5f5;
}
.table-color-col tr:last-child td {
  border: none;
}

.progress-size,
.progress-darkviolet,
.progress-skyblue,
.progress-violet,
.progress-green,
.progress-darkblue {
  width: 100%;
  height: 8px;
}

.progress-darkblue {
  background-color: #dde1ff;
}
.progress-darkblue .progress-bar-darkblue {
  border-radius: 5px;
  background-color: #0010f7;
}

.progress-green {
  background-color: #e9faf4;
}
.progress-green .progress-bar-green {
  border-radius: 5px;
  background-color: #29d697;
}

.progress-violet {
  background-color: #ebe7ff;
}
.progress-violet .progress-bar-violet {
  border-radius: 5px;
  background-color: #c8bbfb;
}

.progress-skyblue {
  background-color: #d5e8fe;
}
.progress-skyblue .progress-bar-skyblue {
  border-radius: 5px;
  background-color: #4797ff;
}

.progress-darkviolet {
  background-color: #e8defe;
}
.progress-darkviolet .progress-bar-darkviolet {
  border-radius: 5px;
  background-color: #9d6fff;
}

.ot-badge.primary {
  background-color: #e9faf4;
  color: #7ab668;
  font-weight: 600;
  font-size: 12px;
  line-height: 15px;
  padding: 4px 10px;
  border-radius: 50px;
}

.btn-select-content {
  width: 305px;
  height: 50px;
}
.btn-select-content .btn-select {
  width: 100%;
  height: 50px;
  border: 1px solid #eaeaea;
  border-radius: 5px;
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  background-color: white;
  color: #b2bec3;
}
.btn-select-content .btn-select:hover {
  box-shadow: 0px 0px 10px rgba(10, 175, 255, 0.35);
  border: 1px solid #0010f7;
  outline: none;
}
.btn-select-content ul {
  width: 305px;
  padding: 20px;
}
.btn-select-content .sub-menu {
  position: relative !important;
  width: 100% !important;
  transform: translate(0px, 0px) !important;
}

.input-Range-date {
  height: 50px;
  flex-direction: row;
  border: 1px solid #eaeaea;
  border-radius: 5px;
  padding: 0px 10px;
  gap: 25px;
}
.input-Range-date input {
  border: none;
  outline: none;
}
.input-Range-date:hover {
  box-shadow: 0px 0px 10px rgba(10, 175, 255, 0.35);
  border: 1px solid #0010f7;
  outline: none;
}

.btn-primary-custom {
  font-weight: 600;
  height: 50px;
  background: linear-gradient(90deg, #0f6aff 0%, #21c6fb 100%);
  border: none;
}

.input-search-with-icon {
  position: relative;
  width: 320px;
  height: 40px;
}
.input-search-with-icon input {
  width: 100%;
  height: 100%;
  padding: 10px 45px 10px 16px;
  border: 1px solid #eaeaea;
  border-radius: 5px;
  outline: none;
}
.input-search-with-icon input:focus-visible {
  box-shadow: 0px 0px 10px rgba(10, 175, 255, 0.35);
  border: 1px solid #0010f7;
  outline: none;
}
.input-search-with-icon ::-moz-placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}
.input-search-with-icon :-ms-input-placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}
.input-search-with-icon ::placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}
.input-search-with-icon i {
  position: absolute;
  top: 12px;
  color: #6f767e;
}
.input-search-with-icon i.fa-search {
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
}

.button-group {
  border-color: #eaeaea !important;
}

.button-group:hover {
  background-color: rgba(161, 169, 255, 0.2039215686) !important;
  border-color: #5669ff !important;
}

.basic-table.pagination-content .pagination,
.pagination-content.ot-table-bg .pagination,
.pagination-content.table-color-col .pagination {
  width: 368px;
}
.basic-table.pagination-content .pagination li,
.pagination-content.ot-table-bg .pagination li,
.pagination-content.table-color-col .pagination li {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 3px;
  width: 32px;
  border: 1px solid #eaeaea;
  border-radius: 7px;
}
.basic-table.pagination-content .pagination li a,
.pagination-content.ot-table-bg .pagination li a,
.pagination-content.table-color-col .pagination li a {
  color: #1a1d1f;
}
.basic-table.pagination-content .pagination li a i,
.pagination-content.ot-table-bg .pagination li a i,
.pagination-content.table-color-col .pagination li a i {
  color: #6f767e;
}
.basic-table.pagination-content .pagination li:hover a,
.pagination-content.ot-table-bg .pagination li:hover a,
.pagination-content.table-color-col .pagination li:hover a {
  color: #5669ff;
}
.basic-table.pagination-content .pagination li:hover,
.pagination-content.ot-table-bg .pagination li:hover,
.pagination-content.table-color-col .pagination li:hover {
  border-color: #5669ff;
}

.selected {
  background: linear-gradient(90deg, #0f6aff 0%, #21c6fb 100%);
}
.selected > a {
  color: white !important;
}

.table-top-componet .right-component a {
  float: right;
}
@media (max-width: 768px) {
  .table-top-componet .right-component a {
    float: left;
    margin-top: 20px;
  }
}

/* Accordion */
.custom-accordion {
  margin: 0;
  padding: 0;
  color: #2d3436;
  font-size: 14px;
  line-height: 1.5715;
  list-style: none;
  background-color: #fff;
  border: 1px solid #b2bec3 !important;
  border-radius: 7px;
}
.custom-accordion .accordion-button:not(.collapsed) {
  border-bottom: 1px solid #b2bec3 !important;
  color: #5669ff;
  background-color: transparent !important;
}
.custom-accordion .accordion-button {
  border-bottom: 1px solid #b2bec3 !important;
}
.custom-accordion .accordion-button:focus {
  border-color: transparent !important;
  border-bottom: 1px solid #b2bec3 !important;
  box-shadow: none !important;
}
.custom-accordion .accordion-tag {
  background: #eef0ff;
  border: 1px solid #5669ff;
  color: #4d5ee5;
  padding: 0 16px;
  border-radius: 10px;
}

/* Signup, Signin */
.col-custom-height {
  min-height: 100vh;
}

.left-box .left-box-image img {
  max-width: 100%;
  max-height: 100%;
}
.left-box .title {
  font-family: "Lexend";
  font-weight: 700;
  font-size: 30px;
  background: linear-gradient(90deg, #0f6aff 0%, #21c6fb 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.left-box .text {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  font-size: 16px;
}

.form-wrapper .form-content {
  width: 360px;
  gap: 20px;
}
.form-wrapper .form-content .form-heading-text .title {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  font-weight: 700;
  font-size: 38px;
  line-height: 54px;
  color: #1a1d1f;
}
.form-wrapper .form-content .form-heading-text .text {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 21px;
}
.form-wrapper .form-content .form {
  gap: 20px;
}
.form-wrapper .form-content .form label {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  font-weight: 500;
  color: #1a1d1f;
}
.form-wrapper .form-content .text-font {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
}
.form-wrapper .form-content .link-text {
  background: linear-gradient(90deg, #0f6aff 0%, #21c6fb 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.form-wrapper .form-content .text-privacy-policy {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  font-size: 12px;
}

.submit-button {
  width: 100%;
  background: linear-gradient(90deg, #0f6aff 0%, #21c6fb 100%);
  border-radius: 5px;
  color: white;
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  font-weight: 600;
}

@media (max-width: 768px) {
  .frame {
    display: none !important;
  }
  .form-wrapper .form-content {
    width: 340px;
    gap: 20px;
    padding: 20px;
  }
  .form-wrapper .form-content .form-heading-text .title {
    font-size: 30px;
  }
  .form-wrapper .form-content .form {
    width: 100%;
    gap: 10px;
  }
  .form-wrapper .form-content .form .input-field-focus {
    width: 100%;
  }
  .error-content {
    width: 410px !important;
    padding: 20px !important;
  }
  .error-content h1 {
    font-size: 28px !important;
  }
}
@media (max-width: 576px) {
  .form-wrapper .form-content {
    width: 320px;
    gap: 20px;
    gap: 10px;
  }
  .form-wrapper .form-content .form-heading-text .title {
    font-size: 28px;
  }
  .form-wrapper .form-content .form-heading-text .text {
    font-family: "Lexend";
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    font-size: 13px;
  }
  .form-wrapper .form-content .form .input-field-focus input {
    height: 40px;
    padding: 10px;
  }
  .form-wrapper .form-content .form label {
    font-size: 13px;
  }
  .social-media-content label {
    font-size: 13px;
  }
  .error-content {
    width: 320px !important;
    padding: 20px !important;
  }
  .error-content h1 {
    font-size: 24px !important;
  }
}
.fogotPassword {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
}

.social-media-content {
  width: 100%;
  gap: 20px;
}
.social-media-content .social-media-items {
  width: 100%;
  height: 48px;
  padding: 16px;
  border: 2px solid #f0eeee;
  border-radius: 5px;
  outline: none;
}
.social-media-content .social-media-items:hover {
  box-shadow: 0px 0px 10px rgba(10, 175, 255, 0.35);
  border: 2px solid #0010f7;
}

.or-line {
  width: 45%;
  height: 1px;
  background-color: #eaeaea;
  margin-top: 8px;
}

.or-text {
  font-size: 10px;
  font-weight: 600;
}

/* Error Page */
.error-wrapper {
  width: 100%;
  height: 100vh;
}
.error-wrapper .error-content {
  width: 486px;
}
.error-wrapper .error-content h1 {
  font-weight: 600;
  font-size: 42px;
}
.error-wrapper .error-content img {
  width: 100%;
}

.btn-back-to-homepage {
  width: 215px;
}

/* Card */
.basic-card .list-group {
  gap: 10px;
}
.basic-card .card-title {
  margin-bottom: 0;
}

.customized .card-body {
  border-bottom: 1px solid #f0eeee;
  border-left: 1px solid #f0eeee;
  border-right: 1px solid #f0eeee;
  border-radius: 0px 0px 5px 5px;
}
.customized .card:hover {
  box-shadow: 0px 5px 10px 0px lightgrey;
}

.no-border-card .list-group {
  gap: 10px;
}
.no-border-card .card {
  box-shadow: 0px 10px 20px 0px lightgrey;
}

.simple-card .list-group {
  gap: 10px;
}

/* Modal */
.information-modal .fa {
  font-size: 28px;
}
.information-modal .modal-footer {
  border-top: none;
}

.confirmation-modal .fa {
  font-size: 28px;
}
.confirmation-modal .modal-footer {
  border-top: none;
}

.modal-header-style {
  background-size: cover;
  background-color: transparent;
  background-repeat: no-repeat;
}

.modal-content {
  border: none;
}

.btn-close {
  background: rgba(255, 255, 255, 0.3215686275);
  border-radius: 50%;
}

.btn-gradian {
  color: white;
  border: none;
  font-weight: 500;
  background: linear-gradient(90deg, #0f6aff 0%, #21c6fb 100%);
}

.btn-gradian:hover {
  color: white;
  background: linear-gradient(90deg, #004fcd 0%, #2796f7 100%);
}

/* Alert */
.alert-section {
  background: #fff;
  padding: 80px;
  background: #fcfcfc;
  margin-top: 8px;
}
.alert-section h1 {
  font-weight: 700;
  font-size: 32px;
  text-transform: uppercase;
  color: #120d26;
}
.alert-section p {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #4b4b4b;
}

.close-icon {
  float: right;
}

.basic-success-alert {
  padding: 15px;
  background-color: #e9faf4;
  color: #29d697;
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
}
.basic-success-alert .success-icon {
  color: #29d697;
}

.basic-secondary-alert {
  padding: 15px;
  background-color: #eef0ff;
  color: #4d5ee5;
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
}
.basic-secondary-alert .secondary-icon {
  color: #4d5ee5;
}

.basic-warning-alert {
  padding: 15px;
  background-color: #fef9e5;
  color: #fdc400;
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
}
.basic-warning-alert .warning-icon {
  color: #fdc400;
}

.basic-danger-alert {
  padding: 15px;
  background-color: #fff0ed;
  color: #ff6a54;
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
}
.basic-danger-alert .danger-icon {
  color: #ff6a54;
}

.alert-success-describe h4,
.alert-success-describe p {
  color: #29d697;
}

.alert-secondary-describe h4,
.alert-secondary-describe p {
  color: #4d5ee5;
}

.alert-warning-describe h4,
.alert-warning-describe p {
  color: #fdc400;
}

.alert-danger-describe h4,
.alert-danger-describe p {
  color: #ff6a54;
}

/* Color Template */
.color-title {
  background-color: transparent;
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
  filter: drop-shadow(0px 2px 8px rgba(0, 0, 0, 0.05));
}
.color-title h4 {
  font-size: 24px;
}

.color-background {
  width: 100%;
  height: 150px;
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
}
.color-background.bg-primary-dark {
  background: var(--blue-dark);
}
.color-background.bg-primary-main {
  background: var(--blue-primary);
}
.color-background.bg-primary-tin1 {
  background: var(--blue-tint-1);
}
.color-background.bg-primary-tin2 {
  background: var(--blue-tint-2);
}
.color-background.bg-secondary-dark {
  background: var(--cyan-dark);
}
.color-background.bg-secondary-main {
  background: var(--cyan-main);
}
.color-background.bg-secondary-tin1 {
  background: var(--cyan-tint-1);
}
.color-background.bg-secondary-tin2 {
  background: var(--cyan-tint-2);
}
.color-background.bg-success-dark {
  background: var(--success-dark);
}
.color-background.bg-success-main {
  background: var(--success-main);
}
.color-background.bg-success-tin1 {
  background: var(--success-tint-1);
}
.color-background.bg-success-tin2 {
  background: var(--success-tint-2);
}
.color-background.bg-danger-dark {
  background: var(--danger-dark);
}
.color-background.bg-danger-main {
  background: var(--danger-main);
}
.color-background.bg-danger-tin1 {
  background: var(--danger-tint-1);
}
.color-background.bg-danger-tin2 {
  background: var(--danger-tint-2);
}
.color-background.bg-info-dark {
  background: var(--info-dark);
}
.color-background.bg-info-main {
  background: var(--info-main);
}
.color-background.bg-info-tin1 {
  background: var(--info-tint-1);
}
.color-background.bg-info-tin2 {
  background: var(--info-tint-2);
}
.color-background.bg-warn-dark {
  background: var(--warn-dark);
}
.color-background.bg-warn-main {
  background: var(--warn-main);
}
.color-background.bg-warn-tin1 {
  background: var(--warn-tint-1);
}
.color-background.bg-warn-tin2 {
  background: var(--warn-tint-2);
}

.color-name-bg {
  background: #f5f5f5;
  border-bottom-left-radius: 5px;
  border-bottom-right-radius: 5px;
}
.color-name-bg.color-primary-main {
  color: var(--blue-primary);
}

.accent {
  width: 100px;
  height: 100px;
  border-radius: 5px;
  filter: drop-shadow(0px 2px 8px rgba(0, 0, 0, 0.05));
  position: relative;
}
.accent.accent-light-green {
  background: var(--accent-light-green);
}
.accent.accent-light-purple {
  background: var(--accent-light-purple);
}
.accent.accent-light-blue {
  background: var(--accent-light-blue);
}
.accent.accent-light-orange {
  background: var(--accent-light-orange);
}
.accent.accent-green {
  background: var(--accent-green);
}
.accent.accent-blue {
  background: var(--accent-blue);
}
.accent.accent-purple {
  background: var(--accent-purple);
}
.accent span {
  position: absolute;
  bottom: 10px;
  padding-left: 5px;
}

.typo {
  width: 100px;
  height: 100px;
  border-radius: 5px;
  filter: drop-shadow(0px 2px 8px rgba(0, 0, 0, 0.05));
  position: relative;
}
.typo.typo-black-dark {
  background: var(--typo-black-dark);
}
.typo.typo-black-main {
  background: var(--typo-black-main);
}
.typo.typo-black-bold {
  background: var(--typo-black-bold);
}
.typo.typo-black-light {
  background: var(--typo-black-light);
}
.typo.typo-black-lighter {
  background: var(--typo-black-lighter);
}
.typo span {
  position: absolute;
  bottom: 10px;
  padding-left: 5px;
}

.bgwhite {
  width: 100px;
  height: 100px;
  border-radius: 5px;
  filter: drop-shadow(0px 2px 8px rgba(0, 0, 0, 0.05));
  position: relative;
}
.bgwhite.bgwhite-dark {
  background: var(--bgwhite-dark);
}
.bgwhite.bgwhite-main {
  background: var(--bgwhite-main);
}
.bgwhite.bgwhite-tin1 {
  background: var(--bgwhite-tin1);
}
.bgwhite.bgwhite-tin2 {
  background: var(--bgwhite-tin2);
}
.bgwhite.typo-black-lighter {
  background: var(--typo-black-lighter);
}
.bgwhite span {
  position: absolute;
  bottom: 10px;
  padding-left: 5px;
}

/* Dropdown */
.dropdown-text-light {
  color: #6f767e;
}

.dropdown-text-dark {
  color: #1a1d1f;
}

.dropdown-text-red {
  color: #ff0022;
}

.dropdown-text-disable {
  color: #b2bec3;
}

.dropdown-text-blue {
  color: #5669ff;
}

.dropdown-content {
  margin-top: 24px;
  margin-left: 32px;
}

.dropdown-section {
  padding: 80px;
  background: #fcfcfc;
  margin-top: 8px;
}
.dropdown-section h1 {
  font-weight: 700;
  font-size: 32px;
  text-transform: uppercase;
  color: #120d26;
}
.dropdown-section p {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #4b4b4b;
}

.button-title {
  font-weight: 600;
  font-size: 14px;
  text-transform: uppercase;
  color: #120d26;
}

.dropdown-with-down-arrow,
.dropdown2-with-down-arrow {
  margin-top: 12px;
  background-color: #fcfcfc;
  font-weight: 500;
  font-size: 16px;
  padding: 8px 16px;
  letter-spacing: 0.02em;
  color: #1a1d1f;
  border: 2px solid #f0eeee;
  border-radius: 5px;
}
.dropdown-with-down-arrow i,
.dropdown2-with-down-arrow i {
  margin-left: 12px;
  font-weight: bold;
}
.dropdown-with-down-arrow:hover,
.dropdown2-with-down-arrow:hover {
  border: 2px solid #5669ff;
  color: #5669ff;
}
.dropdown-with-down-arrow:hover i,
.dropdown2-with-down-arrow:hover i {
  color: #5669ff;
}
.dropdown-with-down-arrow:focus,
.dropdown2-with-down-arrow:focus {
  border: 2px solid #4d5ee5;
  color: #066ecf;
}
.dropdown-with-down-arrow:focus i,
.dropdown2-with-down-arrow:focus i {
  color: #066ecf;
}

.dropdwon-btn-section {
  margin-top: 40px;
}

.dropdown-with-dots {
  margin-top: 12px;
}

button {
  border: none;
  outline: none;
}

.dropdown-menu {
  margin-top: 10px !important;
}

#dropdown {
  background-color: #fcfcfc;
  border: 2px solid #f0eeee;
  border-radius: 5px 0px 0px 5px;
  font-weight: 500;
  font-size: 16px;
  padding: 8px 16px;
  letter-spacing: 0.02em;
  color: #1a1d1f;
}
#dropdown:hover {
  border: 2px solid #5669ff;
  color: #5669ff;
}
#dropdown:focus {
  border: 2px solid #4d5ee5;
  color: #066ecf;
}

#three-dots {
  background-color: #fcfcfc;
  border: 2px solid #f0eeee;
  border-radius: 0px 5px 5px 0px;
  padding: 8px;
  letter-spacing: 0.02em;
  color: #1a1d1f;
}
#three-dots:hover {
  border: 2px solid #5669ff;
  color: #5669ff;
}
#three-dots:focus {
  border: 2px solid #4d5ee5;
  color: #066ecf;
}

.dropdown-items,
.second-item,
.third-item {
  position: relative;
  width: 191px;
  background: #ffffff;
  border: 1px solid #f0eeee;
  border-radius: 7px;
}
.dropdown-items ul,
.second-item ul,
.third-item ul {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  padding: 16px;
  padding-right: 0px;
  gap: 16px;
}
.dropdown-items ul .text-secondary > i,
.second-item ul .text-secondary > i,
.third-item ul .text-secondary > i {
  padding-right: 16px;
}
.dropdown-items ul li,
.second-item ul li,
.third-item ul li {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  padding: 8px;
  padding-right: 24px;
}
.dropdown-items ul .dropdown-text-dark > i,
.second-item ul .dropdown-text-dark > i,
.third-item ul .dropdown-text-dark > i {
  padding-left: 16px;
}

.second-item {
  display: block;
  width: 180px;
  position: absolute;
  opacity: 0;
  visibility: hidden;
  left: 100%;
  margin-top: -40px;
  transition: all ease-in-out 0.4s;
}

.third-item {
  display: none;
  width: 180px;
  position: absolute;
  left: 112%;
  margin-top: -66px;
}

ul li.dropdown-text-dark:hover .second-item {
  visibility: visible;
  opacity: 1;
  transition-delay: 0s;
  margin-left: 10px;
}

.dropdown-search-checkbox {
  margin-top: 12px;
  background-color: #fcfcfc;
  border: 2px solid #f0eeee;
  border-radius: 50px;
  font-weight: 500;
  font-size: 16px;
  letter-spacing: 0.02em;
  padding: 8px 16px;
  color: #5669ff;
}

.search-container {
  margin-top: 12px;
  width: 272px;
  background: #ffffff;
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.05);
  border-radius: 7px;
}

.search-input-checkbox input {
  width: 20px !important;
  height: 20px;
  border: 1px solid #eaeaea;
  border-radius: 5px;
}
.search-input-checkbox label {
  color: #6f767e;
  margin-left: 12px;
}
.search-input-checkbox ul li {
  display: flex;
}

.search-input {
  position: relative;
  text-align: center;
  padding: 15px 16px 15px 16px;
  border-bottom: 2px solid #f0eeee;
}
.search-input input {
  padding: 12px;
  width: 100%;
  border: 2px solid #f0eeee;
  border-radius: 50px;
  background: #fafafa;
  outline: none;
}
.search-input ::-moz-placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}
.search-input :-ms-input-placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}
.search-input ::placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}
.search-input i {
  position: absolute;
  top: 29px;
  right: 27px;
  padding: 5px;
  background-color: #fafafa;
  color: #6f767e;
}

.search-items {
  padding: 17px 25px;
}
.search-items ul {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  gap: 16px;
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
}
.search-items ul li {
  color: #6f767e;
}

.search-input ul {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  padding: 8px 16px;
  gap: 16px;
}
.search-input ul li {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #6f767e;
}
.search-input ul li input {
  width: auto;
}
.search-input ul li ::-moz-placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}
.search-input ul li :-ms-input-placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}
.search-input ul li ::placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}

.search-container .btn-items {
  padding: 5px 17px 25px;
  text-align: end;
}
.search-container .btn-items .btn {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
}
.search-container .btn-items .btn.clear {
  color: #6f767e;
}

.input-default input,
.input-date input {
  padding: 16px;
  width: 336px;
  height: 48px;
  border: 2px solid #f0eeee;
  border-radius: 5px;
  outline: none;
}
.input-default ::-moz-placeholder,
.input-date ::-moz-placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}
.input-default :-ms-input-placeholder,
.input-date :-ms-input-placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}
.input-default ::placeholder,
.input-date ::placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}

.input-field-focus {
  width: 360px;
}
.input-field-focus input {
  width: 100%;
  height: 48px;
  padding: 16px;
  border: 2px solid #f0eeee;
  border-radius: 5px;
  outline: none;
}
.input-field-focus input:focus-visible {
  box-shadow: 0px 0px 10px rgba(10, 175, 255, 0.35);
  border: 2px solid #0010f7;
  outline: none;
}
.input-field-focus ::-moz-placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}
.input-field-focus :-ms-input-placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}
.input-field-focus ::placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}

.input-with-icon {
  position: relative;
  margin-top: 16px;
  width: 336px;
  height: 48px;
}
.input-with-icon input {
  width: 336px;
  height: 48px;
  padding: 16px 45px;
  border: 2px solid #f0eeee;
  border-radius: 5px;
  outline: none;
}
.input-with-icon input:focus-visible {
  box-shadow: 0px 0px 10px rgba(10, 175, 255, 0.35);
  border: 2px solid #0010f7;
  outline: none;
}
.input-with-icon ::-moz-placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}
.input-with-icon :-ms-input-placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}
.input-with-icon ::placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}
.input-with-icon i {
  position: absolute;
  top: 16px;
  color: #6f767e;
}
.input-with-icon i.fa-user-o {
  left: 20px;
}
.input-with-icon i.fa-search {
  right: 20px;
}

.input-pre-post {
  position: relative;
  margin-top: 16px;
  width: 296px;
  height: 48px;
}
.input-pre-post input {
  padding: 16px 45px;
  width: 296px;
  height: 48px;
  border: 2px solid #f0eeee;
  border-radius: 5px;
  outline: none;
}
.input-pre-post ::-moz-placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}
.input-pre-post :-ms-input-placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}
.input-pre-post ::placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}
.input-pre-post i {
  position: absolute;
  top: 16px;
  color: #6f767e;
}
.input-pre-post i.fa-user-o {
  left: 20px;
}
.input-pre-post i.fa-search {
  right: 60px;
}
.input-pre-post img {
  position: absolute;
  top: 2px;
  right: 1px;
  padding: 15px;
  width: 47px;
  background-color: #f7f7f7;
  border-left: 2px solid #f0eeee;
  border-radius: 0px 5px 5px 0px;
}

.input-https-post {
  position: relative;
  margin-top: 16px;
  width: 296px;
  height: 48px;
}
.input-https-post input {
  padding: 16px 43px 16px 110px;
  width: 296px;
  height: 48px;
  border: 2px solid #f0eeee;
  border-radius: 5px;
  outline: none;
}
.input-https-post ::-moz-placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}
.input-https-post :-ms-input-placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}
.input-https-post ::placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}
.input-https-post i {
  position: absolute;
  top: 16px;
  color: #6f767e;
}
.input-https-post i.fa-user-o {
  left: 88px;
}
.input-https-post img {
  position: absolute;
  top: 2px;
  right: 1px;
  padding: 15px;
  width: 47px;
  background-color: #f7f7f7;
  border-left: 2px solid #f0eeee;
  border-radius: 0px 5px 5px 0px;
}
.input-https-post .https {
  position: absolute;
  color: #1a1d1f;
  top: 2px;
  left: 1.5px;
  background-color: #f7f7f7;
  padding: 12px;
  border-right: 2px solid #f0eeee;
  border-radius: 5px 0px 0px 5px;
}

.input-https-post2 {
  position: relative;
  margin-top: 16px;
  width: 296px;
  height: 48px;
}
.input-https-post2 input {
  padding: 16px 43px 16px 86px;
  width: 296px;
  height: 48px;
  border: 2px solid #f0eeee;
  border-radius: 5px;
  outline: none;
}
.input-https-post2 ::-moz-placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}
.input-https-post2 :-ms-input-placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}
.input-https-post2 ::placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}
.input-https-post2 i {
  position: absolute;
  top: 16px;
  color: #6f767e;
}
.input-https-post2 i.fa-user-o {
  left: 88px;
}
.input-https-post2 i.fa-search {
  right: 20px;
}
.input-https-post2 .https {
  position: absolute;
  color: #1a1d1f;
  top: 2px;
  left: 1.5px;
  background-color: #f7f7f7;
  padding: 12px;
  border-right: 2px solid #f0eeee;
  border-radius: 5px 0px 0px 5px;
}

textarea {
  width: 280px;
  height: 79px;
  margin-top: 16px;
  padding: 12px 16px;
  background: #ffffff;
  border: 1px solid #eaeaea;
  border-radius: 7px;
  outline: none;
}

textarea::-moz-placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}

textarea:-ms-input-placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}

textarea::placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}

.text-area {
  width: 280px;
  height: 79px;
}
.text-area .text-count {
  font-family: "Lexend";
  font-size: 10px;
  font-weight: 500;
  text-align: end;
  color: #b2bec3;
}

.Input-search-tab {
  position: relative;
  margin-top: 16px;
  width: 273px;
  height: 48px;
}
.Input-search-tab input {
  padding: 16px 43px 16px 16px;
  width: 100%;
  height: 100%;
  border: 2px solid #f0eeee;
  border-radius: 5px;
  outline: none;
}
.Input-search-tab ::-moz-placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}
.Input-search-tab :-ms-input-placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}
.Input-search-tab ::placeholder {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}
.Input-search-tab i {
  position: absolute;
  top: 0;
  color: #6f767e;
  border-left: 2px solid #f0eeee;
  padding: 16px;
}
.Input-search-tab i.fa-search {
  right: 0px;
}
.Input-search-tab .search {
  position: absolute;
  color: #ffffff;
  top: 0px;
  right: 0px;
  background-color: #5669ff;
  padding: 13px;
  border-radius: 0px 5px 5px 0px;
}
.Input-search-tab i.fa-microphone {
  right: 70px;
  color: #5669ff;
  border: none;
}

.Input-search-tab.search-color > input {
  width: 289px;
}

.Input-search-tab.search-color > i {
  right: -15px;
  color: #fafafa;
  background-color: #5669ff;
  border-radius: 0px 5px 5px 0px;
}

.Input-search-tab .search-tab1 {
  width: 237px;
}

.Input-search-tab.microphone > input {
  padding: 16px 110px 16px 16px;
}

.input-date {
  margin-top: 16px;
}
.input-date input {
  width: 272px;
}

.Range-date {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  width: 272px;
  height: 48px;
  flex-direction: row;
  border: 2px solid #f0eeee;
  border-radius: 5px;
  margin-top: 16px;
  padding: 10px 16px;
  gap: 5px;
}
.Range-date input {
  border: none;
  outline: none;
}

.time-field {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  flex-direction: row;
  width: 386px;
  gap: 5px;
  margin-top: 16px;
}
.time-field .input-time {
  border: 2px solid #f0eeee;
  border-radius: 5px;
  width: 107px;
  height: 50px;
}
.time-field .input-time select {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  color: #1a1d1f;
  border: none;
  outline: none;
  padding: 11px 16px;
}
.time-field .select-time input {
  border: 2px solid #f0eeee;
  border-radius: 5px;
  width: 272px;
  height: 48px;
  outline: none;
  padding: 16px;
}

.input-group-start-end-time {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  justify-content: flex-end;
  flex-direction: row;
  gap: 10px;
}
.input-group-start-end-time .input-start-end-time {
  border: 2px solid #f0eeee;
  border-radius: 5px;
  width: 169px;
  height: 44px;
  padding: 12px 16px;
  color: #6f767e;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
}
.input-group-start-end-time .input-start-end-time span {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
}

option:hover {
  color: #5669ff;
}

.input-time-select {
  display: flex;
  justify-content: end;
  align-items: center;
  margin-top: 16px;
}
.input-time-select .input-start-end-time-select {
  border: 2px solid #f0eeee;
  border-radius: 5px;
  width: 178px;
  height: 60px;
  padding: 12px 16px;
  color: #6f767e;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
}
.input-time-select .input-start-end-time-select p {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 500;
  font-size: 12px;
  color: #0010f7;
}
.input-time-select .input-start-end-time-select h6 {
  font-family: "Lexend";
  font-style: normal;
  font-weight: 700;
  font-size: 16px;
  color: #1a1d1f;
}

/* Badges */
.badge-success,
.badge-light-success,
.badge-primary,
.badge-light-primary,
.badge-warning,
.badge-light-warning,
.badge-danger,
.badge-light-danger {
  padding: 4px 10px;
  background: var(--ot-bg-badge-success);
  border-radius: 5px;
  font-family: "Lexend", sans-serif;
  font-style: normal;
  font-size: 12px;
  font-weight: 600;
  line-height: 15px;
  color: var(--ot-text-badge-success);
}
.badge-success.ot-badge-circle,
.ot-badge-circle.badge-light-success,
.ot-badge-circle.badge-primary,
.ot-badge-circle.badge-light-primary,
.ot-badge-circle.badge-warning,
.ot-badge-circle.badge-light-warning,
.ot-badge-circle.badge-danger,
.ot-badge-circle.badge-light-danger {
  width: 30px;
  height: 30px;
  border-radius: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.badge-danger,
.badge-light-danger {
  background: var(--ot-bg-badge-danger);
  color: var(--ot-text-badge-danger);
}

.badge-warning,
.badge-light-warning {
  background: var(--ot-bg-badge-warning);
  color: var(--ot-text-badge-warning);
}

.badge-primary,
.badge-light-primary {
  background: var(--ot-bg-badge-primary);
  color: var(--ot-text-badge-primary);
}

.badge-light-success {
  background: var(--ot-bg-badge-light-success);
  color: var(--ot-text-badge-light-success);
}

.badge-light-danger {
  background: var(--ot-bg-badge-light-danger);
  color: var(--ot-text-badge-light-danger);
}

.badge-light-warning {
  background: var(--ot-bg-badge-light-warning);
  color: var(--ot-text-badge-light-warning);
}

.badge-light-primary {
  background: var(--ot-bg-badge-light-primary);
  color: var(--ot-text-badge-light-primary);
}

/* Global Components */
/* Reborting Default Bootstrap carousel */
.ot-carousel .carousel-control-prev-icon {
  background-image: url(../../../assets/images/icons/slider-left-arrow.svg);
}
.ot-carousel .carousel-control-next-icon {
  background-image: url(../../../assets/images/icons/slider-right-arrow.svg);
}
.ot-carousel .carousel-indicators button {
  background-color: #0f6aff;
}

/* custom tooltip */
.custom-tooltip {
  --bs-tooltip-bg: #0f6aff;
}

/* Components */
.ot-pagination {
  margin-top: 40px;
}
.ot-pagination .pagination {
  margin: 0;
}
.ot-pagination .pagination .page-item {
  margin-right: 10px;
}
.ot-pagination .pagination .page-item:last-child {
  margin-right: 0;
}
.ot-pagination .pagination .page-item .page-link {
  font-family: "Lexend", sans-serif;
  font-style: normal;
  font-size: 14px;
  font-weight: 400;
  line-height: 16px;
  color: var(--ot-text-table-pagination);
  border: 1px solid var(--ot-border-table-pagination);
  border-radius: 7px;
  padding: 8px 12px;
  background-color: var(--ot-bg-table-pagination);
}
.ot-pagination .pagination .page-item .page-link:hover {
  background-color: transparent;
}
.ot-pagination .pagination .page-item .page-link:focus {
  box-shadow: none;
  background-color: transparent;
}
.ot-pagination .pagination .page-item .page-link.active {
  background-image: linear-gradient(90deg, #0f6aff 0%, #21c6fb 100%),
    linear-gradient(90deg, #0f6aff 0%, #21c6fb 100%);
  background-origin: border-box;
  background-clip: content-box, border-box;
  box-shadow: 2px 1000px 1px var(--ot-bg-table-pagination) inset;
  font-family: "Lexend", sans-serif;
  font-style: normal;
  font-size: 14px;
  font-weight: 400;
  line-height: 16px;
  color: #21c6fb;
  border: 1px solid transparent;
}

/* profile */
.profile-content {
  .profile-menu-mobile {
    background-color: var(--ot-bg-profile-mobile-menu);

    @media (min-width: 992px) {
      display: none;
    }

    .btn-menu-mobile {
      background-color: var(--ot-bg-profile-mobile-menu);
      padding: 16px 24px;
      width: 100%;
      display: block;
      text-align: left;

      .icon {
        font-size: 18px;
        color: var(--ot-primary-text);
      }
    }

    .offcanvas {
      width: 100%;
      max-width: 300px;
      background-color: var(--ot-bg-profile-mobile-menu);

      .offcanvas-body {
        padding: 0;
      }

      .offcanvas-header {
        justify-content: end;

        .btn-close {
          width: 20px;
          height: 20px;
          line-height: 20px;

          &:focus {
            box-shadow: none;
          }
        }

        .icon {
          font-size: 18px;
          color: var(--ot-primary-text);
        }
      }
    }

    .profile-menu {
      background-color: var(--ot-bg-profile-mobile-menu);
      width: 100%;
      max-width: 100%;
      display: flex;
      flex-direction: column;
      flex-wrap: wrap;
      gap: 40px;
      border-right: 1px solid #eaeaea;
      flex: 0 0 300px;
      min-height: 1000px;
    }

    .profile-menu-head {
      padding: 40px 28px 0px 40px;

      .body {
        margin-left: 12px;

        .title {
          font-family: "Lexend", sans-serif;
          font-style: normal;
          font-size: 18px;
          font-weight: 600;
          line-height: 16px;
          color: #1a1d1f;
          margin-bottom: 8px;
        }

        .paragraph {
          font-family: "Lexend", sans-serif;
          font-style: normal;
          font-size: 14px;
          font-weight: 400;
          line-height: 18px;
          color: #6f767e;
        }
      }
    }

    .profile-menu-body {
      .nav-item {
        margin-bottom: 35px;
        border-right: 2px solid transparent;
        border-radius: 2px 0px 0px 2px;

        &:last-child {
          margin-bottom: 0;
        }
      }

      .nav-link {
        padding-left: 40px;
        border-right: 2px solid transparent;
        border-radius: 2px 0px 0px 2px;
        font-family: "Lexend", sans-serif;
        font-style: normal;
        font-size: 15px;
        font-weight: 500;
        line-height: 16px;
        color: #6f767e;

        &.active,
        &:hover {
          color: #5669ff;
          border-right: 2px solid #5669ff;
        }
      }
    }
  }

  .profile-menu {
    background-color: var(--ot-bg-profile-menu);
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    gap: 40px;
    border-right: 1px solid var(--ot-primary-border);
    min-height: 1000px;
    flex: 0 0 300px;

    @media (max-width: 1399.98px) {
      flex: 0 0 240px;
    }

    @media (max-width: 991.98px) {
      display: none;
    }

    .profile-menu-head {
      padding: 60px 28px 0px 60px;

      @media (max-width: 1399.98px) {
        padding: 60px 16px 0px 16px;
      }

      .body {
        margin-left: 12px;

        @media (max-width: 1399.98px) {
          margin-left: 4px;
        }

        .title {
          font-family: "Lexend", sans-serif;
          font-style: normal;
          font-size: 18px;
          font-weight: 600;
          line-height: 16px;
          color: var(--ot-primary-text);
          margin-bottom: 8px;
        }

        .paragraph {
          font-family: "Lexend", sans-serif;
          font-style: normal;
          font-size: 14px;
          font-weight: 400;
          line-height: 18px;
          color: #6f767e;
        }
      }
    }

    .profile-menu-body {
      .nav-item {
        margin-bottom: 35px;
        border-right: 2px solid transparent;
        border-radius: 2px 0px 0px 2px;

        &:last-child {
          margin-bottom: 0;
        }
      }

      .nav-link {
        padding-left: 60px;
        border-right: 2px solid transparent;
        border-radius: 2px 0px 0px 2px;
        font-family: "Lexend", sans-serif;
        font-style: normal;
        font-size: 15px;
        font-weight: 500;
        line-height: 16px;
        color: #6f767e;

        @media (max-width: 1399.98px) {
          padding-left: 16px;
        }

        &.active,
        &:hover {
          color: #5669ff;
          border-right: 2px solid #5669ff;
        }
      }
    }
  }

  .profile-body {
    width: 0%;
    flex: 1;
    background-color: var(--ot-bg-profile-body);
    padding: 80px 250px 80px 80px;

    @media (max-width: 991.98px) {
      width: unset;
    }

    @media (max-width: 1399.98px) {
      padding: 80px 24px;
    }

    .btn-back {
      text-decoration: none;
      font-family: "Lexend", sans-serif;
      font-style: normal;
      font-size: 14px;
      font-weight: 600;
      line-height: 18px;
      color: #ffffff;
      padding: 11px 16px;
      background: #5669ff;
      border-radius: 5px;
    }

    .title {
      font-family: "Lexend", sans-serif;
      font-style: normal;
      font-size: 30px;
      font-weight: 700;
      line-height: 38px;
      color: var(--ot-primary-text);
      margin-bottom: 40px;
    }

    .nav {
      width: 100%;
      border-bottom: 1px solid var(--ot-primary-border);

      .nav-item {
        margin-right: 40px;

        @media (max-width: 1399.98px) {
          margin-right: 12px;
        }

        &:last-child {
          margin-right: 0;
        }
      }

      .nav-link {
        padding: 16px 0;
        border-radius: 0;
        font-family: "Lexend", sans-serif;
        font-style: normal;
        font-size: 16px;
        font-weight: 500;
        line-height: 20px;
        color: #6f767e;
        cursor: pointer;
        border-bottom: 1px solid transparent;

        &.active,
        &:hover {
          background-color: transparent;
          color: #5669ff;
          border-bottom: 1px solid #5669ff;
        }
      }
    }
  }

  .profile-body-form {
    .form-item {
      padding: 32px 0px;
      border-bottom: 1px solid var(--ot-primary-border);
    }

    .image-box {
      width: 100px;
      height: 100px;
      position: relative;

      .icon {
        position: absolute;
        right: 0;
        bottom: 0;
        width: 32px;
        height: 32px;
        font-size: 18px;
        line-height: 32px;
        border: none;
        border-radius: 200px;
        background-color: #ffffff;
        color: #5669ff;
        box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.05);
        text-align: center;
        cursor: pointer;
      }
    }

    .title {
      font-family: "Lexend", sans-serif;
      font-style: normal;
      font-size: 16px;
      font-weight: 500;
      line-height: 20px;
      color: var(--ot-primary-text);
      margin-bottom: 8px;

      .icon-required {
        font-size: 8px;
        color: #ff6a54;
      }
    }
  }
}

.profile-content {
  .profile-body-form {
    .paragraph {
      font-family: "Lexend", sans-serif;
      font-style: normal;
      font-size: 14px;
      font-weight: 400;
      line-height: 18px;
      color: #6f767e;
    }

    .btn-edit {
      background-color: transparent;
      font-family: "Lexend", sans-serif;
      font-style: normal;
      font-size: 14px;
      font-weight: 400;
      line-height: 18px;
      color: #6f767e;
    }

    .form-box {
      margin-top: 12px;

      .form-control,
      .form-select {
        max-width: 336px;
        max-height: 48px;
        border: 1px solid #eaeaea;
        border-radius: 5px;

        &::placeholder {
          font-family: "Lexend", sans-serif;
          font-style: normal;
          font-size: 14px;
          font-weight: 400;
          line-height: 16px;
          color: #6f767e;
        }

        &:-ms-input-placeholder {
          font-family: "Lexend", sans-serif;
          font-style: normal;
          font-size: 14px;
          font-weight: 400;
          line-height: 16px;
          color: #6f767e;
        }

        &::-moz-placeholder {
          font-family: "Lexend", sans-serif;
          font-style: normal;
          font-size: 14px;
          font-weight: 400;
          line-height: 16px;
          color: #6f767e;
        }

        &:focus {
          border-color: #eaeaea;
          box-shadow: none;
        }
      }

      .unset-size {
        max-width: unset;
        max-height: unset;
      }

      .btn-update {
        font-family: "Lexend", sans-serif;
        font-style: normal;
        font-size: 14px;
        font-weight: 600;
        line-height: 18px;
        color: #ffffff;
        padding: 11px 20px;
        background: #5669ff;
        border-radius: 5px;
        margin-top: 12px;
      }
    }
  }

  .profile-body {
    &.padding-reduce {
      padding: 80px 40px;

      @media (max-width: 1399.98px) {
        padding: 80px 24px;
      }
    }
  }
}

.profile-table-content {
  .table-toolbar {
    font: 500 14px/18px "Lexend", sans-serif;
    color: #000;

    .form-select {
      width: auto;
      padding: 10px 30px 10px 14px;
      border: 1px solid var(--ot-border-table-toolbar-per-page);
      border-radius: 5px;
      background: var(--ot-bg-table-toolbar-per-page) no-repeat right 14px center;
      background-size: 12px;
      color: var(--ot-primary-text);
      outline: 0;
      box-shadow: none;
    }

    .btn-add {
      display: block;
      padding: 10px 17px;
      background: linear-gradient(90deg, #0f6aff, #21c6fb);
      color: #fff;
      border: 1px solid #21c6fb;
      border-radius: 5px;
      font-weight: 600;
      text-align: center;
      text-decoration: none;
    }

    .btn-daterange,
    .dropdown-export .btn-export,
    .dropdown-designation .btn-designation {
      background-image: linear-gradient(90deg, #0f6aff, #21c6fb),
        linear-gradient(90deg, #0f6aff, #21c6fb);
      background-clip: content-box, border-box;
      background-origin: border-box;
      box-shadow: 2px 1000px 1px var(--ot-bg-table-toolbar-btn-outline-primary) inset;
      border: 1px solid transparent;
      color: var(--ot-text-primary);
      padding: 10px 12px;
      border-radius: 5px;
      font-weight: 600;
      transition: all ease-in-out 0.1s;

      .icon {
        color: #21c6fb;
        font-size: 14px;
      }

      &:hover,
      &:focus {
        box-shadow: none;
        background: linear-gradient(90deg, #0f6aff, #21c6fb);
        color: #fff;

        .icon {
          color: #fff;
        }
      }
    }

    .search-box {
      position: relative;
      border: 1px solid var(--ot-border-table-toolbar-search);
      border-radius: 5px;

      .form-control {
        padding: 8px 45px 8px 16px;
        border: none;
        background: var(--ot-bg-table-toolbar-search);
        color: var(--ot-primary-text);
        box-shadow: none;

        &::placeholder {
          font: 400 14px/16px "Lexend", sans-serif;
          color: #b2bec3;
        }
      }

      .icon {
        position: absolute;
        top: 50%;
        right: 12px;
        transform: translateY(-50%);
        color: #6f767e;
      }
    }

    .dropdown-designation {
      .dropdown-menu {
        position: relative;
        background: var(--ot-bg-secondary) !important;
        border: 1px solid var(--ot-primary-border) !important;
        color: var(--ot-primary-text) !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        border-radius: 7px;
        margin-top: 10px !important;
        padding: 0;
      }

      .search-content {
        padding: 20px;

        .search-box {
          border: 1px solid #eaeaea;
          border-radius: 50px;

          .form-control {
            width: 232px;
            padding: 8px 45px 8px 16px;
            border: none;
            border-radius: 50px;
            box-shadow: none;
            background: #fff;
            color: #1a1d1f;

            &::placeholder {
              font: 400 14px/16px "Lexend", sans-serif;
              color: #b2bec3;
            }
          }
        }
      }
    }
  }
}
/* Inputs */
/* Custom Input field */
.ot-input {
  background-color: var(--ot-bg-secondary) !important;
  border: 1px solid var(--ot-primary-border) !important;
  outline: none !important;
  color: var(--ot-primary-text) !important;
  font-size: 14px;
  padding-left: 16px;
}
.ot-input:focus-visible {
  box-shadow: 0px 0px 10px rgba(10, 175, 255, 0.35);
  border: 1px solid #5669ff !important;
  outline: none;
}
.ot-input::-moz-placeholder {
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}
.ot-input:-ms-input-placeholder {
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}
.ot-input::placeholder {
  font-weight: 400;
  font-size: 14px;
  color: #b2bec3;
}

.input-check-radio {
  padding: 8px 0;
}
.input-check-radio .form-check {
  display: flex;
  align-items: self-end;
}
.input-check-radio .form-check .form-check-input {
  width: 20px;
  height: 20px;
  border-radius: 5px;
  border: 1px solid var(--ot-border-table-checkbox);
  background-color: var(--ot-bg-table-checkbox);
  margin-right: 4px;
}
.input-check-radio .form-check .form-check-input:focus {
  box-shadow: none;
}
.input-check-radio .form-check .form-check-input:checked {
  background-repeat: no-repeat;
  background-position: center;
}
.input-check-radio .form-check .form-check-label {
  margin-left: 8px;
}


/* Email Template */
.email-template {
  font-family: "Lexend", sans-serif;
  text-align: center;
  padding: 56px 60px;
  background: white;
  border-radius: 5px;
  max-width: 600px;
  /* Template Buttton start */
  /* Template Buttton end */
}
.email-template .template-heading h1 {
  font-family: "Lexend", sans-serif;
  font-weight: 600;
  font-size: 24px;
  line-height: 34px;
  margin-top: 20px;
}
.email-template .template-heading p {
  font-family: "Lexend", sans-serif;
  font-size: 16px;
  line-height: 24px;
  color: #6f767e;
  margin-top: 20px;
}
.email-template .template-heading .color-black {
  color: #1a1d1f;
}
.email-template .template-body {
  font-family: "Lexend", sans-serif;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #6f767e;
  padding: 14px;
}
.email-template .template-body .content-part {
  text-align: left;
  margin-bottom: 28px;
}
.email-template .template-body .content-part p a {
  font-family: "Lexend", sans-serif;
  color: #0f6aff;
}
.email-template .template-body .content-part h5 {
  font-family: "Lexend", sans-serif;
  color: #1a1d1f;
  margin-top: 28px;
  padding: 0;
}
.email-template .template-body .content-details p {
  font-family: "Lexend", sans-serif;
  padding: 0 14px;
  margin-bottom: 28px;
}
.email-template .template-body .content-details p .link {
  color: #0f6aff;
}
.email-template .template-body .ot-primary-text {
  font-family: "Lexend", sans-serif;
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
  color: #0f6aff;
  margin-top: 26px;
}
.email-template .template-body h4 {
  font-family: "Lexend", sans-serif;
  font-weight: 600;
  font-size: 16px;
  color: #29d697;
}
.email-template .template-body h5 {
  font-family: "Lexend", sans-serif;
  padding: 0 14px;
}
.email-template .template-button-group {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-left: 14px;
  gap: 10px;
}
.email-template .template-button-group .template-btn {
  padding: 9px 2px;
  border-radius: 7px;
  background: linear-gradient(90deg, #0f6aff 0%, #21c6fb 100%);
}
.email-template .template-button-group .template-btn span {
  font-family: "Lexend", sans-serif;
  padding: 10px 16px;
  font-weight: 600;
  color: white;
  background: linear-gradient(90deg, #0f6aff 0%, #21c6fb 100%);
}
.email-template .template-button-group .template-btn span:hover {
  outline: none;
  border: none;
  color: #0f6aff;
  border-radius: 5px;
  background: white;
}
.email-template .template-btn-container {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.email-template .template-btn-container .template-btn {
  padding: 9px 2px;
  border-radius: 7px;
  background: linear-gradient(90deg, #0f6aff 0%, #21c6fb 100%);
}
.email-template .template-btn-container .template-btn span {
  font-family: "Lexend", sans-serif;
  padding: 10px 16px;
  font-weight: 600;
  color: white;
  background: linear-gradient(90deg, #0f6aff 0%, #21c6fb 100%);
}
.email-template .template-btn-container .template-btn span:hover {
  outline: none;
  border: none;
  color: #0f6aff;
  border-radius: 5px;
  background: white;
}
.email-template .template-footer {
  font-family: "Lexend", sans-serif;
  font-weight: 500;
  font-size: 12px;
  line-height: 15px;
  color: #6f767e;
  border-top: 1px solid #dfe6e9;
  margin-top: 26px;
}
.email-template .template-footer p > a {
  color: #0f6aff;
}
.email-template .template-footer .social-media-button {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 26px;
  gap: 8px;
}
.email-template .template-footer .social-media-button a {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 8.5px;
  border-radius: 50%;
  background: linear-gradient(90deg, #0f6aff 0%, #21c6fb 100%);
}
.email-template .template-footer .social-media-button a:hover {
  background: linear-gradient(90deg, #21c6fb 0%, #0f6aff 100%);
}
.email-template .template-footer .template-footer-image {
  margin-top: 28px;
  margin-bottom: 8px;
}

@media (max-width: 576px) {
  .email-template {
    padding: 26px 30px;
  }
  .email-template .template-heading h1 {
    font-size: 20px;
    padding: 0 10px;
  }
  .email-template .template-heading p {
    font-size: 16px;
    padding: 0 8px;
  }
  .email-template .template-body {
    font-weight: 400;
    font-size: 14px;
    line-height: 24px;
    color: #6f767e;
  }
  .email-template .template-body p {
    padding: 0;
  }
  .email-template .template-body .template-content-image img {
    width: 100%;
    height: 100%;
  }
  .email-template .template-body h5 {
    padding: 0;
  }
  .email-template .template-button-group {
    flex-direction: column;
    padding: 0;
  }
  .email-template .template-button-group button {
    width: 100%;
  }
  .email-template .template-footer {
    font-size: 7px;
  }
} /*# sourceMappingURL=style.css.map */
