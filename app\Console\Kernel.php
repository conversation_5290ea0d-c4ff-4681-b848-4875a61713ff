<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [

    ];

    /**
     * Define the application's command schedule.
     *
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        // $schedule->command('inspire')->hourly();
        $schedule->command('auto:checkout')->daily();

        // expire notification
        $schedule->command('notification:expire')->dailyAt('00:00');

        // $schedule->command('queue:work --stop-when-empty')
        //         ->everyFiveMinutes()
        //          ->withoutOverlapping();

        $schedule->command('queue:work --timeout=0 --stop-when-empty')->everyMinute()->withoutOverlapping();
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__.'/Commands');

        require \base_path('routes/console.php');
    }
}
