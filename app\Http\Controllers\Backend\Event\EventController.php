<?php

namespace App\Http\Controllers\Backend\Event;

use App\Http\Controllers\Controller;
use App\Http\Resources\Hrm\HolidayCollection;
use App\Repositories\Hrm\Holiday\HolidayRepository;
use Illuminate\Http\Request;

class EventController extends Controller
{
    protected $holiday;

    public function __construct(HolidayRepository $holiday)
    {
        $this->holiday = $holiday;
    }

    public function index(Request $request)
    {
        $events = $this->holiday->appScreen($request);

        return new HolidayCollection($events);
    }
}
