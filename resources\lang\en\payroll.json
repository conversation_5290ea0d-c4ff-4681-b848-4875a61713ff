{"Payroll": "Payroll", "Commissions": "Commissions", "Setup": "Setup", "Advance Type": "Advance Type", "Advance": "Advance", "Salary Generate": "Salary Generate", "Salary Report": "Salary Report", "Accounts": "Accounts", "Account List": "Account List", "Deposit": "<PERSON><PERSON><PERSON><PERSON>", "Expense": "Expense", "Transaction History": "Transaction History", "Accounts Settings": "Accounts Set<PERSON>s", "Deposit Category": "Deposit Category", "Active": "Active", "Inactive": "Inactive", "Commission": "Commission", "Salary": "Salary", "Employee Setup List": "Employee Setup List", "Set Commission": "Set Commission", "Travel": "Travel", "Installment": "Installment", "One Time": "One Time", "Monthly": "Monthly", "Requested": "Requested", "Approved": "Approved", "Returned": "Returned", "Approve": "Approve", "Make Payment": "Make Payment", "Month": "Month", "Salary Type": "Salary Type", "Calculation": "Calculation", "Status": "Status", "Action": "Action", "Edit Account": "Edit Account", "Contract Date": "Contract Date", "Contract End": "Contract End", "Gross Salary": "Gross Salary", "Enter Gross Salary": "Enter Gross <PERSON>", "Payslip Type": "Payslip Type", "Per Month": "Per Month", "Late Check In": "Late Check In", "Early Check out": "Early Check out", "Extra Leave": "Extra Leave", "Monthly Leave": "Monthly Leave", "Payslip": "Payslip", "Addition": "Addition", "Adjust Salary": "Adjust Sal<PERSON>", "Absent": "Absent", "Performance": "Performance", "Late Check out": "Late Check out", "Salary Components": "Salary Components", "Commission Setup": "Commission Setup", "Fixed": "Fixed", "Percentage": "Percentage", "Deduction": "Deduction", "Add Commission": "Add Commission", "Salary details": "Salary details", "Salary Calculation": "Salary Calculation", "Advance Salary": "Advance Salary", "Addition Salary": "Addition Salary", "Deduction Salary": "Deduction Salary", "Adjust:": "Adjust:", "Net Salary": "Net Salary", "Edit Deposit": "<PERSON>", "Expense Approve": "Expense Approve", "Attendance Deduction": "Attendance Deduction", "Enter Salary": "<PERSON>ter <PERSON>"}