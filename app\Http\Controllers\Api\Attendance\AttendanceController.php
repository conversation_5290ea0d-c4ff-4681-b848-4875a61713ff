<?php

namespace App\Http\Controllers\Api\Attendance;

use App\Helpers\CoreApp\Traits\ApiReturnFormatTrait;
use App\Http\Controllers\Controller;
use App\Models\Hrm\Attendance\Attendance;
use App\Repositories\Hrm\Attendance\AttendanceRepository;
use App\Services\Hrm\EmployeeBreakService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AttendanceController extends Controller
{
    use ApiReturnFormatTrait;

    protected $attendance;

    protected $breakBackService;

    public function __construct(AttendanceRepository $attendanceRepository, EmployeeBreakService $breakBackService)
    {
        $this->attendance = $attendanceRepository;
        $this->breakBackService = $breakBackService;
    }

    public function checkInCheckoutStatus(Request $request)
    {
        return $this->attendance->getCheckInCheckoutStatus(Auth::id());
    }

    public function manageAttendance(Request $request)
    {
        try {
            if ($request->attendance_id) {
                $attendance = Attendance::with(['dutyCalendar'])->find($request->attendance_id);

                if (! $attendance) {
                    return $this->responseWithError('Attendance not found', [], 400);
                }

                return $this->attendance->checkOut($request, $attendance);
            } else {
                return $this->attendance->checkIn($request);
            }
        } catch (\Throwable $th) {
            \info($th->getMessage());

            return $this->responseWithError('Something went wrong', [], 400);
        }
    }

    public function liveLocationStore(Request $request)
    {
        return $this->attendance->liveLocationStore($request);
    }

    public function breakBack(Request $request, $slug)
    {
        return $this->breakBackService->breakStartEnd($request, $slug);
    }

    public function breakBackEnd(Request $request)
    {
        return $this->breakBackService->breakBackEnd($request);
    }

    public function breakBackListView()
    {
        return $this->breakBackService->breakBackList();
    }

    public function breakBackHistory(Request $request)
    {
        return $this->breakBackService->breakBackHistory($request);
    }

    public function qrStatus(Request $request)
    {
        return $this->attendance->qrStatus($request);
    }
}
