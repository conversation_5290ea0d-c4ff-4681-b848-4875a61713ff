<?php

namespace App\Http\Controllers\Backend\Finance;

use App\Http\Controllers\Controller;
use App\Repositories\Hrm\Finance\AccountRepository;
use App\Repositories\Hrm\Finance\TransactionRepository;
use Illuminate\Http\Request;

class TransactionController extends Controller
{
    protected $transactionRepository;

    protected $accountRepository;

    public function __construct(
        TransactionRepository $transactionRepository,
        AccountRepository $accountRepository
    ) {
        $this->transactionRepository = $transactionRepository;
        $this->accountRepository = $accountRepository;
    }

    public function index(Request $request)
    {
        if ($request->ajax()) {
            return $this->transactionRepository->table($request);
        }
        $data['checkbox'] = true;
        $data['class'] = 'transaction_datatable';
        $data['title'] = _trans('common.Transaction History');
        $data['fields'] = $this->transactionRepository->fields('transaction');
        $data['accounts'] = $this->accountRepository->model()->get();

        return \view('backend.finance.transaction.index', \compact('data'));
    }

    public function datatable(Request $request)
    {
        return $this->transactionRepository->datatable($request, 'transaction');
    }
}
