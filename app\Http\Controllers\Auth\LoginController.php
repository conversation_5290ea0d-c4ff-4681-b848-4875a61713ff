<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Providers\RouteServiceProvider;
use Illuminate\Foundation\Auth\AuthenticatesUsers;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class LoginController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Login Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles authenticating users for the application and

    | redirecting them to your home screen. The controller uses a trait
    | to conveniently provide its functionality to your applications.
    |
    */

    use AuthenticatesUsers;

    /**
     * Where to redirect users after login.
     *
     * @var string
     */
    protected $redirectTo = RouteServiceProvider::HOME;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest')->except('logout');
    }

    public function authenticate(Request $request)
    {
        $request->validate([
            'email' => 'required|exists:users,email',
            'password' => 'required',
            'device_uuid' => 'required',
        ], [
            'email.exists' => 'The email does not exist in our records',
        ]);

        try {
            $credentials = $request->only('email', 'password');

            $user = User::query()->where('email', $request->email)->first();

            if ($user) {
                session()->put('session_branch_id', $user->branch_id);
                session()->put('session_company_id', $user->company_id);

                if (isModuleActive('SingleDeviceLogin')) {
                    if ($user->is_admin == 1 || $user->role_id == 1) {
                        return $this->userLogin($credentials, $user, $request->device_uuid);
                    }
                    if ($user->device_uuid == null || $user->device_uuid == $request->device_uuid) {
                        return $this->userLogin($credentials, $user, $request->device_uuid);
                    } else {
                        return \redirect()->back()->withInput($request->only('email'))->withErrors(['email' => 'User already registered with another device']);
                    }
                } else {
                    return $this->userLogin($credentials, $user, $request->device_uuid);
                }
            } else {
                return \redirect()->back()->withInput($request->only('email'))->withErrors(['email' => 'The email does not exist in our records']);
            }
        } catch (\Throwable $th) {
            Log::error($th->getMessage());

            return \redirect()->back()->with('error', 'Something went wrong!');
        }
    }

    public function userLogin($input, $user, $uuid)
    {
        if (\isModuleActive('SingleDeviceLogin')) {
            if ($user->role->web_login != 1 && $user->id != 1) {
                throwException('You don\'t have permission to login');
            }
        }

        if (Auth::attempt($input)) {
            try {
                $user->_token = Auth::getSession()->getId();
                $user->last_login_device = 'web';
                $user->device_uuid = $uuid;
                // $this->logoutFromMobile($user);
                $user->save();
            } catch (\Throwable $th) {
                throw $th;
            }

            // Auth::logoutOtherDevices(request('password'));
            return \redirect()->route('admin.dashboard')->with('success', 'Login successful');
        } else {
            return \redirect()->back()->withErrors(['login_form_error' => 'Credentials do not match our records']);
        }
    }

    public function authenticated()
    {
        if (Auth::check()) {
            return \redirect()->route('admin.dashboard');
        } else {
            return \redirect()->back();
        }
    }
}
