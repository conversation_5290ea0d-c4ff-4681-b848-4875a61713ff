<?php

namespace App\Http\Controllers\Backend\Department;

use App\Helpers\CoreApp\Traits\ApiReturnFormatTrait;
use App\Http\Controllers\Controller;
use App\Http\Requests\DepartmentReqeust;
use App\Models\coreApp\Relationship\RelationshipTrait;
use App\Models\Hrm\Department\Department;
use App\Repositories\Hrm\Department\DepartmentRepository;
use Illuminate\Http\Request;

class DepartmentController extends Controller
{
    use ApiReturnFormatTrait,  RelationshipTrait;

    protected DepartmentRepository $department;

    protected $model;

    public function __construct(DepartmentRepository $department, Department $model)
    {
        $this->department = $department;
        $this->model = $model;
    }

    public function index(Request $request)
    {
        $data['title'] = _trans('common.Departments');
        $data['formTitle'] = _trans('common.Add New Department');
        $data['collection'] = $this->department->getPaginateData($request);

        if ($request->filled('id')) {
            $data['formTitle'] = _trans('common.Edit Department');
            $data['department'] = $this->department->show($request->id);
        }

        return \view('backend.department.index')->with($data);
    }

    public function store(DepartmentReqeust $request)
    {
        try {
            $this->department->store($request->validated());

            return \redirect()->route('department.index')->with('success', 'Department created successfully');
        } catch (\Throwable $th) {
            return \redirect()->back()->with('error', _trans('alert.Something went wrong!'));
        }
    }

    public function update(DepartmentReqeust $request, $id)
    {
        if (\config('app.style') === 'demo' || \env('APP_STYLE') === 'demo') {
            return \redirect()->back()->with('error', _trans('alert.You are not allowed to perform the delete action in demo mode'));
        }

        try {
            $this->department->update($request, id: $id);

            return \redirect()->route('department.index')->with('success', _trans('alert.Department updated successfully'));
        } catch (\Throwable $th) {
            return \redirect()->back()->with('error', _trans('alert.something went wrong!'));
        }
    }

    public function delete(Department $department)
    {
        if (\config('app.style') === 'demo' || \env('APP_STYLE') === 'demo') {
            return \redirect()->back()->with('error', _trans('alert.You are not allowed to perform the delete action in demo mode'));
        }

        return $this->department->destroy($department);
    }

    public function statusUpdate(Request $request)
    {
        if (\demoCheck()) {
            return \redirect()->back()->with('error', _trans('alert.You cannot do it for demo'));
        }

        return $this->department->statusUpdate($request);
    }

    public function deleteData(Request $request)
    {
        if (\demoCheck()) {
            return \redirect()->back()->with('error', _trans('alert.You cannot delete for demo'));
        }

        return $this->department->destroyAll($request);
    }
}
