<?php

namespace App\Http\Controllers\Backend\Employee;

use App\Http\Controllers\Controller;
use App\Http\Requests\Appointment\CreateAppointmentRequest;
use App\Repositories\Hrm\Department\DepartmentRepository;
use App\Repositories\Hrm\Employee\AppoinmentRepository;
use Brian2694\Toastr\Facades\Toastr;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AppointmentController extends Controller
{
    public function __construct(
        protected AppoinmentRepository $appointRepo
    ) {}

    public function index()
    {
        try {
            $data['title'] = _trans('leave.Leave Request');
            $data['departments'] = \resolve(DepartmentRepository::class)->getAll();
            $data['url'] = \route('leaveRequest.dataTable');

            return \view('backend.leave.leaveRequest.index', \compact('data'));
        } catch (\Exception $exception) {
            Toastr::error(_trans('response.Something went wrong!'), 'Error');

            return \redirect()->back();
        }
    }

    public function list()
    {
        try {
            $data['title'] = _trans('appointment.Appointment List');
            $data['id'] = Auth::user()->id;

            $data['checkbox'] = true;
            $data['fields'] = $this->appointRepo->fields();
            $data['table'] = \route('appointment.table');
            $data['url_id'] = 'appointment_table_url';
            $data['class'] = 'table_class';

            return \view('backend.appointment.index', \compact('data'));
        } catch (\Exception $exception) {
            Toastr::error(_trans('response.Something went wrong!'), 'Error');

            return \redirect()->back();
        }
    }

    public function table(Request $request)
    {
        if ($request->ajax()) {
            return $this->appointRepo->table($request);
        }
    }

    public function create()
    {
        try {
            $data['title'] = _trans('common.Appointment');

            return \view('backend.appointment.create', \compact('data'));
        } catch (\Exception $exception) {
            Toastr::error(_trans('response.Something went wrong!'), 'Error');

            return \redirect()->back();
        }
    }

    public function store(CreateAppointmentRequest $request)
    {
        try {
            $data = $this->appointRepo->store($request);
            if ($data->original['result']) {
                Toastr::success(_trans('response.Appointment created successfully'), 'Success');

                return \redirect()->route('appointment.index');
            } else {
                Toastr::error('Appointment is not available for you', 'Error');
            }

            return \redirect()->back();
        } catch (\Exception $exception) {
            \dd($exception);
            Toastr::error(_trans('response.Something went wrong!'), 'Error');

            return \redirect()->back();
        }
    }

    public function dataTable(Request $request)
    {
        return $this->appointRepo->dataTable($request);
    }

    public function profileDataTable(Request $request)
    {
        return $this->appointRepo->profileDataTable($request, $request->id);
    }

    public function edit($id)
    {
        try {
            $data['title'] = _trans('common.Appointment');
            $data['appointment'] = $this->appointRepo->show($id);

            return \view('backend.appointment.edit', \compact('data'));
        } catch (\Exception $exception) {
            \info($exception->getMessage());
            Toastr::error(_trans('response.Something went wrong!'), 'Error');

            return \redirect()->back();
        }
    }

    public function update(CreateAppointmentRequest $request, $id)
    {
        try {
            $this->appointRepo->update($request, $id);

            Toastr::success(_trans('response.Appointment updated successfully'), 'Success');

            return \redirect()->route('appointment.index');
        } catch (\Exception $exception) {
            \info($exception);
            Toastr::error(_trans('response.Something went wrong!'), 'Error');

            return \redirect()->back();
        }
    }

    public function delete($id)
    {
        try {
            $this->appointRepo->destroy($id);
            Toastr::success(_trans('response.Operation successful'), 'Success');

            return \redirect()->route('appointment.index');
        } catch (\Exception $exception) {
            \info($exception->getMessage());
            Toastr::error(_trans('response.Something went wrong!'), 'Error');

            return \redirect()->back();
        }
    }

    public function userProfileTable(Request $request)
    {
        if ($request->ajax()) {
            return $this->appointRepo->table($request);
        }
    }
}
