<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class GetDatabaseSchema extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'db:schema
                            {--table= : Specific table to show schema for}
                            {--format=text : Output format (text, json)}
                            {--limit= : Limit the number of tables shown}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get database schema information';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $table = $this->option('table');
        $format = $this->option('format');
        $limit = (int) $this->option('limit');

        try {
            // Get the database connection
            $connection = DB::connection();
            $driver = $connection->getDriverName();

            // Output connection info if in text mode
            if ($format === 'text') {
                $this->info("Database driver: {$driver}");
                $this->info('Database: '.config("database.connections.{$driver}.database"));
                $this->newLine();
            }

            // Get tables
            $tables = $this->getTables($connection, $driver);

            // If specific table, filter to just that table
            if ($table) {
                if (! in_array($table, $tables)) {
                    $this->error("Table '{$table}' not found in database.");

                    return 1;
                }
                $tables = [$table];
            }

            // Skip Laravel system tables unless specifically requested
            if (! $table) {
                $skipTables = ['migrations', 'password_resets', 'failed_jobs', 'personal_access_tokens', 'jobs'];
                $tables = array_filter($tables, function ($tableName) use ($skipTables) {
                    return ! in_array($tableName, $skipTables);
                });
            }

            // Limit the number of tables if specified
            if ($limit > 0) {
                $tables = array_slice($tables, 0, $limit);
            }

            // Get foreign keys for all tables
            $foreignKeys = $this->getForeignKeys($connection, $driver);

            // Generate schema based on format
            if ($format === 'json') {
                $schema = $this->generateJsonSchema($connection, $driver, $tables, $foreignKeys);
                $this->line(json_encode($schema, JSON_PRETTY_PRINT));
            } else {
                $schema = $this->generateTextSchema($connection, $driver, $tables, $foreignKeys);
                $this->line($schema);
            }

            return 0;
        } catch (\Exception $e) {
            $this->error('Error: '.$e->getMessage());

            return 1;
        }
    }

    /**
     * Get list of database tables
     *
     * @param  \Illuminate\Database\Connection  $connection
     * @param  string  $driver
     * @return array
     */
    protected function getTables($connection, $driver)
    {
        if ($driver === 'pgsql') {
            $tables = $connection->select("
                SELECT table_name
                FROM information_schema.tables
                WHERE table_schema = 'public'
                ORDER BY table_name
            ");

            return array_map(function ($row) {
                return $row->table_name;
            }, $tables);
        } else {
            // MySQL
            $tables = $connection->select('SHOW TABLES');
            $columnName = 'Tables_in_'.config("database.connections.{$driver}.database");

            return array_map(function ($row) use ($columnName) {
                return $row->$columnName;
            }, $tables);
        }
    }

    /**
     * Get foreign keys for all tables
     *
     * @param  \Illuminate\Database\Connection  $connection
     * @param  string  $driver
     * @return array
     */
    protected function getForeignKeys($connection, $driver)
    {
        $foreignKeys = [];

        try {
            if ($driver === 'pgsql') {
                $fkQuery = "
                    SELECT
                        tc.table_name,
                        kcu.column_name,
                        ccu.table_name AS foreign_table_name,
                        ccu.column_name AS foreign_column_name
                    FROM information_schema.table_constraints AS tc
                    JOIN information_schema.key_column_usage AS kcu
                        ON tc.constraint_name = kcu.constraint_name
                        AND tc.table_schema = kcu.table_schema
                    JOIN information_schema.constraint_column_usage AS ccu
                        ON ccu.constraint_name = tc.constraint_name
                        AND ccu.table_schema = tc.table_schema
                    WHERE tc.constraint_type = 'FOREIGN KEY'
                ";
                $fkResults = $connection->select($fkQuery);
            } else {
                // MySQL
                $dbName = config("database.connections.{$driver}.database");
                $fkQuery = '
                    SELECT
                        TABLE_NAME as table_name,
                        COLUMN_NAME as column_name,
                        REFERENCED_TABLE_NAME as foreign_table_name,
                        REFERENCED_COLUMN_NAME as foreign_column_name
                    FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
                    WHERE REFERENCED_TABLE_SCHEMA = ?
                    AND REFERENCED_TABLE_NAME IS NOT NULL
                ';
                $fkResults = $connection->select($fkQuery, [$dbName]);
            }

            foreach ($fkResults as $fk) {
                if (! isset($foreignKeys[$fk->table_name])) {
                    $foreignKeys[$fk->table_name] = [];
                }
                $foreignKeys[$fk->table_name][$fk->column_name] = [
                    'table' => $fk->foreign_table_name,
                    'column' => $fk->foreign_column_name,
                ];
            }
        } catch (\Exception $e) {
            $this->warn('Error getting foreign keys: '.$e->getMessage());
        }

        return $foreignKeys;
    }

    /**
     * Generate schema in text format
     *
     * @param  \Illuminate\Database\Connection  $connection
     * @param  string  $driver
     * @param  array  $tables
     * @param  array  $foreignKeys
     * @return string
     */
    protected function generateTextSchema($connection, $driver, $tables, $foreignKeys)
    {
        $output = [];

        foreach ($tables as $tableName) {
            try {
                // Get columns based on database driver
                if ($driver === 'pgsql') {
                    $columns = $connection->select("
                        SELECT column_name, data_type, character_maximum_length,
                               is_nullable, column_default,
                               (SELECT COUNT(*) FROM information_schema.table_constraints tc
                                JOIN information_schema.constraint_column_usage ccu
                                  ON tc.constraint_name = ccu.constraint_name
                                WHERE tc.constraint_type = 'PRIMARY KEY'
                                  AND tc.table_name = c.table_name
                                  AND ccu.column_name = c.column_name) as is_primary_key
                        FROM information_schema.columns c
                        WHERE table_name = ?
                        ORDER BY ordinal_position
                    ", [$tableName]);
                } else {
                    // MySQL
                    $columns = $connection->select("SHOW COLUMNS FROM `{$tableName}`");
                }

                // Format table header
                $output[] = "Table: {$tableName}";
                $output[] = 'Columns:';

                // Format columns
                foreach ($columns as $column) {
                    $columnName = $driver === 'pgsql' ? $column->column_name : $column->Field;
                    $type = $driver === 'pgsql' ? $column->data_type : $column->Type;
                    $isPrimary = $driver === 'pgsql'
                    ? ($column->is_primary_key > 0)
                    : ($column->Key === 'PRI');
                    $primaryStr = $isPrimary ? ' (PK)' : '';
                    $nullable = $driver === 'pgsql'
                    ? ($column->is_nullable === 'YES' ? ', nullable' : '')
                    : ($column->Null === 'YES' ? ', nullable' : '');

                    // Add foreign key info
                    $foreignKeyInfo = isset($foreignKeys[$tableName][$columnName])
                    ? ", -> {$foreignKeys[$tableName][$columnName]['table']}.{$foreignKeys[$tableName][$columnName]['column']}"
                    : '';

                    $output[] = "  - {$columnName}{$primaryStr}: {$type}{$nullable}{$foreignKeyInfo}";
                }

                // Add a blank line between tables
                $output[] = '';

            } catch (\Exception $e) {
                $output[] = "Error processing table {$tableName}: ".$e->getMessage();
                $output[] = '';
            }
        }

        return implode("\n", $output);
    }

    /**
     * Generate schema in JSON format
     *
     * @param  \Illuminate\Database\Connection  $connection
     * @param  string  $driver
     * @param  array  $tables
     * @param  array  $foreignKeys
     * @return array
     */
    protected function generateJsonSchema($connection, $driver, $tables, $foreignKeys)
    {
        $schema = [];

        foreach ($tables as $tableName) {
            try {
                // Get columns based on database driver
                if ($driver === 'pgsql') {
                    $columns = $connection->select("
                        SELECT column_name, data_type, character_maximum_length,
                               is_nullable, column_default,
                               (SELECT COUNT(*) FROM information_schema.table_constraints tc
                                JOIN information_schema.constraint_column_usage ccu
                                  ON tc.constraint_name = ccu.constraint_name
                                WHERE tc.constraint_type = 'PRIMARY KEY'
                                  AND tc.table_name = c.table_name
                                  AND ccu.column_name = c.column_name) as is_primary_key
                        FROM information_schema.columns c
                        WHERE table_name = ?
                        ORDER BY ordinal_position
                    ", [$tableName]);
                } else {
                    // MySQL
                    $columns = $connection->select("SHOW COLUMNS FROM `{$tableName}`");
                }

                $tableData = [
                    'name' => $tableName,
                    'columns' => [],
                ];

                // Process columns
                foreach ($columns as $column) {
                    $columnName = $driver === 'pgsql' ? $column->column_name : $column->Field;
                    $type = $driver === 'pgsql' ? $column->data_type : $column->Type;

                    $columnData = [
                        'name' => $columnName,
                        'type' => $type,
                        'primary_key' => $driver === 'pgsql'
                        ? ($column->is_primary_key > 0)
                        : ($column->Key === 'PRI'),
                        'nullable' => $driver === 'pgsql'
                        ? ($column->is_nullable === 'YES')
                        : ($column->Null === 'YES'),
                    ];

                    // Add default value if exists
                    if ($driver === 'pgsql') {
                        if ($column->column_default !== null) {
                            $columnData['default'] = $column->column_default;
                        }
                    } else {
                        if ($column->Default !== null) {
                            $columnData['default'] = $column->Default;
                        }
                    }

                    // Add foreign key info if exists
                    if (isset($foreignKeys[$tableName][$columnName])) {
                        $columnData['references'] = [
                            'table' => $foreignKeys[$tableName][$columnName]['table'],
                            'column' => $foreignKeys[$tableName][$columnName]['column'],
                        ];
                    }

                    $tableData['columns'][] = $columnData;
                }

                $schema[] = $tableData;

            } catch (\Exception $e) {
                // Skip tables with errors
                continue;
            }
        }

        return $schema;
    }
}
