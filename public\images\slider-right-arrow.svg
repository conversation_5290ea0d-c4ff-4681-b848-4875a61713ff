<svg width="89" height="81" viewBox="0 0 89 81" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_6_46)">
<path d="M61.1667 3.00012L82 23.8335L61.1667 44.6668" stroke="url(#paint0_linear_6_46)" stroke-width="4.66667" stroke-linecap="round" stroke-linejoin="round" shape-rendering="crispEdges"/>
</g>
<g filter="url(#filter1_d_6_46)">
<path d="M82 23.8334H48.6667C25.6542 23.8334 7 42.4875 7 65.5V69.6667" stroke="url(#paint1_linear_6_46)" stroke-width="5" stroke-linecap="round" stroke-linejoin="round" shape-rendering="crispEdges"/>
</g>
<defs>
<filter id="filter0_d_6_46" x="54.8333" y="0.666748" width="33.5" height="54.3334" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_6_46"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_6_46" result="shape"/>
</filter>
<filter id="filter1_d_6_46" x="0.5" y="21.3334" width="88" height="58.8334" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_6_46"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_6_46" result="shape"/>
</filter>
<linearGradient id="paint0_linear_6_46" x1="71.5833" y1="3.00012" x2="71.5833" y2="44.6668" gradientUnits="userSpaceOnUse">
<stop stop-color="#0F6AFB"/>
<stop offset="1" stop-color="#0F6AFB"/>
</linearGradient>
<linearGradient id="paint1_linear_6_46" x1="44.5" y1="23.8334" x2="44.5" y2="69.6667" gradientUnits="userSpaceOnUse">
<stop stop-color="#0F6AFB"/>
<stop offset="1" stop-color="#21C6FB"/>
</linearGradient>
</defs>
</svg>
