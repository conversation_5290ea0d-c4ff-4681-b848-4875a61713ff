<div class="check-in-modal modal  lead-modal" id="lead-modal" aria-labelledby="modalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content data">
            <div class="modal-header modal-header-style mb-3">
                <h5 class="modal-title text-white">{{ @$data['title'] }} </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                    <i class="fa fa-times" aria-hidden="true"></i>
                </button>
            </div>
            <div class="modal-body">
                {{-- Tabs --}}
                <div class="form-group">
                    <div class="place-switch">
                        <div class="switch-field">
                            <input type="radio" id="modal_check" name="place_mode" value="1" checked="">
                            <label for="modal_check">
                                <div class="icon icon-size-20">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24"
                                        fill="none">
                                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                            stroke-width="1.5"
                                            d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10zM8.5 12h6">
                                        </path>
                                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                            stroke-width="1.5" d="M12.5 15l3-3-3-3">
                                        </path>
                                    </svg>
                                </div>
                                <p class="on-half-expanded">{{ _trans('common.Check') }}</p>
                            </label>
                            <input type="radio" id="modal_break" name="place_mode" value="0">
                            <label for="modal_break">
                                <div class="icon icon-size-20">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24"
                                        fill="none">
                                        <path d="M9.54 15.92v-5.34l-1.5 1.67M10.02 4.47 12 2" stroke="currentColor"
                                            stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                        <path
                                            d="M4.91 7.8c-1.11 1.48-1.8 3.31-1.8 5.31A8.89 8.89 0 0 0 12 22a8.89 8.89 0 0 0 8.89-8.89A8.89 8.89 0 0 0 12 4.22c-.68 0-1.34.09-1.98.24"
                                            stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
                                            stroke-linejoin="round">
                                        </path>
                                        <path
                                            d="M14 10.58c1.1 0 2 .9 2 2v1.35c0 1.1-.9 2-2 2s-2-.9-2-2v-1.35a2 2 0 0 1 2-2Z"
                                            stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
                                            stroke-linejoin="round">
                                        </path>
                                    </svg>
                                </div>
                                <p class="on-half-expanded">{{ _trans('common.Break') }}</p>
                            </label>

                        </div>
                    </div>
                </div>

                {{-- Clock & Date --}}
                <div class="form-group">
                    <div class="timer-field pt-2 pb-2 text-center">
                        <h5 class="company_name_clock ui-widget text-40" id="clock">
                            <sup class="session fw-regular">AM</sup>
                            <span class="hour fw-semibold">10</span>:<span class="minute fw-semibold">12</span>
                            <sup class="second session-time  fw-regular">12</sup>
                        </h5>
                        <span class="mb-20 d-block">Wednesday, June 22, 2025</span>
                    </div>
                </div>
                {{-- Clock & Date --}}

                {{-- Note Box - When Late--}}
                @if (@$data['reason'][0] == 'L' || @$data['reason'][0] == 'LE')
                    <div class="form-group w-50 mx-auto mb-3">
                        <label class="form-label float-left">{{ _trans('common.Note') }} </label>
                        <textarea type="text" name="reason" id="reason" rows="3" class="form-control mt-0 ot-input"
                            onkeyup="textAreaValidate(this.value, 'error_show_reason')"
                            placeholder="{{ _trans('common.Note') }}">{{ old('reason') }}</textarea>
                        <small class="error_show_reason text-left text-danger"></small>
                    </div>
                @endif
                {{-- / --}}

                {{-- Start Check In checkout --}}
                <div id="modalCheckInOrCheckOutSection" style="display: none;">
                    {{--Start Check In Buttons --}}
                    @if (!isAttendee()['checkin'] || (isAttendee()['checkin'] && isAttendee()['checkout']))
                    <div class="form-group button-hold-container mb-20">
                        <button class="button-hold check-in-out-button-hold" id="check-in-out-button-hold">
                            <div>
                                <div class="icon_text">
                                    <div class="icon text-white">
                                        @include('backend.dashboard.attendance.finger_icon')
                                    </div>
                                    <span class="text-14 fw-semibold text-white">{{ _trans('common.Check In') }}</span>
                                </div>
                                <svg class="progress" viewBox="0 0 32 32">
                                    <circle r="8" cx="16" cy="16" stroke="transparent" />
                                </svg>
                                <svg class="tick" viewBox="0 0 32 32">
                                    <polyline points="18,7 11,16 6,12" />
                                </svg>
                            </div>
                        </button>
                        <input type="hidden" id="checkInOrCheckOutUrl"
                            value="{{ route('admin.ajaxDashboardCheckin') }}">
                    </div>
                    @endif
                    {{--End Check In Buttons --}}

                    {{--Start Check Out Buttons --}}
                    @if (isAttendee()['checkin'] && !isAttendee()['checkout'])
                    <div class="form-group button-hold-container mb-20">
                        <button class="button-hold check-in-out-button-hold" id="check-in-out-button-hold">
                            <div>
                                <div class="icon_text">
                                    <div class="icon text-danger">
                                        @include('backend.dashboard.attendance.finger_icon')
                                    </div>
                                    <span class="text-14 fw-semibold text-danger">{{ _trans('common.Check Out') }}</span>
                                </div>
                                <svg class="progress" viewBox="0 0 32 32">
                                    <circle r="8" cx="16" cy="16" stroke="transparent" />
                                </svg>
                                <svg class="tick" viewBox="0 0 32 32">
                                    <polyline points="18,7 11,16 6,12" />
                                </svg>
                            </div>
                        </button>
                        <input type="hidden" id="checkInOrCheckOutUrl"
                            value="{{ route('admin.ajaxDashboardCheckOut') }}">
                    </div>
                    @endif
                    {{--End Check Out Buttons --}}
                </div>
                {{--End Check in out Buttons --}}

                {{--Start break start buttons --}}
                <div id="modalBreakStarEndSection" style="display: none;">
                    @if (isModuleActive('Break'))
                    @if (isAttendee()['checkin'] && !isAttendee()['checkout'])
                    @if (isUserBreakRunning())
                    <div class="form-group button-hold-container mb-20 break-running mb-20 heartbeat">
                        <button class="button-hold break-button-hold" id="break-button-hold">
                            <div>
                                <div class="icon_text">
                                    <div class="icon text-white">
                                        @include('backend.dashboard.attendance.finger_icon')
                                    </div>
                                    <span class="text-14 fw-semibold text-white">{{ _trans('common.Break End') }}</span>
                                </div>
                                <svg class="progress" viewBox="0 0 32 32">
                                    <circle r="8" cx="16" cy="16" stroke="transparent" />
                                </svg>
                                <svg class="tick" viewBox="0 0 32 32">
                                    <polyline points="18,7 11,16 6,12" />
                                </svg>
                            </div>
                        </button>
                        <input type="hidden" id="breakStartEndUrl" value="{{ route('break.update') }}">
                    </div>
                    {{-- Break Time --}}
                    <div class="user-related-info d-flex align-items-center justify-content-center flex-wrap gap-20">
                        <div class="user-related-info-item d-flex align-items-center gap-10">
                            <div class="contents text-center d-flex align-items-center gap-8">
                                <div class="icon icon-size-20 text-title">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24"
                                        fill="none">
                                        <path d="M22 12c0 5.52-4.48 10-10 10S2 17.52 2 12 6.48 2 12 2s10 4.48 10 10Z"
                                            stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
                                            stroke-linejoin="round"></path>
                                        <path d="m15.71 15.18-3.1-1.85c-.54-.32-.98-1.09-.98-1.72v-4.1"
                                            stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
                                            stroke-linejoin="round"></path>
                                    </svg>
                                </div>
                                <h6 class="title text-16 fw-semibold mb-0">{{ _trans('common.Total Break Time') }}</h6>
                                <p class="paragraph text-16 fw-semibold line-height-1 red-color mb-0">{{
                                    @$data['break_duration'] }}</p>
                            </div>
                        </div>
                    </div>
                    {{-- / --}}
                    @else
                    <div class="form-group button-hold-container mb-20">
                        <button class="button-hold break-button-hold" id="break-button-hold">
                            <div>
                                <div class="icon_text">
                                    <div class="icon text-white">
                                        @include('backend.dashboard.attendance.finger_icon')
                                    </div>
                                    <span class="text-14 fw-semibold text-white">{{ _trans('common.Break Start')
                                        }}</span>
                                </div>
                                <svg class="progress" viewBox="0 0 32 32">
                                    <circle r="8" cx="16" cy="16" stroke="transparent" />
                                </svg>
                                <svg class="tick" viewBox="0 0 32 32">
                                    <polyline points="18,7 11,16 6,12" />
                                </svg>
                            </div>
                        </button>
                        <input type="hidden" id="breakStartEndUrl" value="{{ route('break.store') }}">
                    </div>
                    @endif
                    @endif
                    @endif
                </div>
                {{--End break start buttons --}}




                {{-- Example Output UI

                Checked in at 10:20 AM → "You are late by 20m."
                Checked in at 09:50 AM → "You are early by 10m."
                Checked in at 10:00 AM → "You checked in on time."


                --}}

                {{-- Info Text --}}
                <div class="p-3 bg-primary radius-8  mt-40 mb-20 mx-4">
                    <div
                        class="user-related-info d-flex align-items-center justify-content-center flex-wrap gap-20 mx-20">
                        <div class="user-related-info-item d-flex align-items-center gap-10">
                            <div class="contents text-center d-flex align-items-center gap-8">
                                <div class="icon icon-size-20 text-title">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24"
                                        fill="none">
                                        <path d="M22 12c0 5.52-4.48 10-10 10S2 17.52 2 12 6.48 2 12 2s10 4.48 10 10Z"
                                            stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
                                            stroke-linejoin="round"></path>
                                        <path d="m15.71 15.18-3.1-1.85c-.54-.32-.98-1.09-.98-1.72v-4.1"
                                            stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
                                            stroke-linejoin="round"></path>
                                    </svg>
                                </div>
                                {{-- <h6 class="title text-16 fw-semibold mb-0 text-capitalize">You're late by - </h6>
                                <h6 class="title text-16 fw-semibold mb-0 text-capitalize">You're early by - </h6> --}}
                                <h6 class="title text-16 fw-semibold mb-0 text-capitalize">You're on time -
                                </h6>
                                <p class="paragraph text-16 fw-semibold line-height-1 red-color mb-0">10:00:00</p>
                            </div>
                        </div>
                    </div>
                </div>
                {{-- / --}}

            </div>
        </div>
    </div>
</div>
</div>
<script src="{{ global_asset('backend/js/fs_d_ecma/components/__attendance.js') }}"></script>
<script src="{{ global_asset('backend/js/fs_d_ecma/components/__break.js') }}"></script>

<script>
    $(document).ready(function () {
        var isUserBreakRunning = "{{ isUserBreakRunning() }}";
        var checkIn            = "{{ isAttendee()['checkin'] }}";
        var checkOut           = "{{ isAttendee()['checkout'] }}";

        if (checkIn && checkOut || (!checkIn && !checkOut)) {
            $("#modal_check").prop("disabled", false).prop("checked", true);
            $("#modal_break").prop("disabled", true);
        }

        if (isUserBreakRunning) {
            $("#modal_check").prop("disabled", true);
            $("#modal_break").prop("disabled", false).prop("checked", true);
        }

        function toggleSections() {
            if ($('#modal_check').is(':checked')) {
                $('#modalCheckInOrCheckOutSection').show();
                $('#modalBreakStarEndSection').hide();
            } else if ($('#modal_break').is(':checked')) {
                $('#modalCheckInOrCheckOutSection').hide();
                $('#modalBreakStarEndSection').show();
            }
        }

        // Run on page load
        toggleSections();

        // Run on radio change
        $('input[name="place_mode"]').on('change', toggleSections);
    });
</script>