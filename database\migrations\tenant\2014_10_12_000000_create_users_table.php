<?php

use App\Enums\EmployeeStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('users', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('email');
            $table->string('phone')->nullable();
            $table->json('image')->nullable();
            $table->enum('gender', ['male', 'female', 'other'])->nullable();

            $table->foreignId('role_id')->constrained('roles')->noActionOnDelete();
            $table->foreignId('company_id')->constrained('companies')->noActionOnDelete();
            $table->foreignId('branch_id')->constrained('branches')->noActionOnDelete();
            $table->foreignId('department_id')->constrained('departments')->noActionOnDelete();
            $table->foreignId('designation_id')->constrained('designations')->noActionOnDelete();

            // Employment
            $table->enum('status', array_column(EmployeeStatus::cases(), 'value'))->default(EmployeeStatus::PROBATION->value);
            $table->enum('job_type', ['full_time', 'part_time'])->default('full_time');

            // Reporting
            $table->string('employee_id')->nullable();

            // Security
            $table->string('password');

            // Verification
            $table->timestamp('email_verified_at')->nullable();

            $table->rememberToken();
            $table->softDeletes();
            $table->timestamps();
        });

        Schema::table('users', function (Blueprint $table) {
            $table->foreignId('manager_id')->nullable()->constrained('users')->noActionOnDelete()->after('employee_id');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('users');
    }
};
