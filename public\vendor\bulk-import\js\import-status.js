/**
 * Import Status Tracker - Handles real-time updates of import status
 * 
 * This script uses Laravel Echo to receive real-time updates for import status changes
 * and updates the UI accordingly. It also provides fallback polling for older browsers.
 */
class ImportStatusTracker {
    /**
     * Initialize the import status tracker
     * 
     * @param {Object} options Configuration options
     * @param {Number} options.importId The ID of the import to track
     * @param {Number} options.userId The ID of the current user
     * @param {String} options.statusEndpoint The API endpoint to fetch status updates
     * @param {Number} options.pollInterval Polling interval in milliseconds
     * @param {Function} options.onUpdate Callback function when status is updated
     * @param {Boolean} options.useReverb Whether to use Laravel Reverb (default: true)
     */
    constructor(options) {
        this.importId = options.importId;
        this.userId = options.userId;
        this.statusEndpoint = options.statusEndpoint || `/api/admin/bulk-import/history/${this.importId}/status`;
        this.pollInterval = options.pollInterval || 3000;
        this.onUpdate = options.onUpdate || (() => { });
        this.useReverb = options.useReverb !== undefined ? options.useReverb : true;
        this.polling = false;
        this.completed = false;
        this.pollTimer = null;

        this.init();
    }

    /**
     * Initialize the status tracker
     */
    init() {
        if (this.useReverb && typeof window.Echo !== 'undefined') {
            this.initReverb();
        } else {
            console.log('Echo not available, falling back to polling');
            this.startPolling();
        }
    }

    /**
     * Initialize Laravel Echo for real-time updates
     */
    initReverb() {
        try {
            // Listen to the private channel for this specific import
            window.Echo.private(`import-history.${this.importId}`)
                .listen('ImportStatusChanged', (data) => {
                    this.updateStatus(data);
                });

            // Also listen to user channel for all imports
            window.Echo.private(`import-history.user.${this.userId}`)
                .listen('ImportStatusChanged', (data) => {
                    if (data.id === this.importId) {
                        this.updateStatus(data);
                    }
                });

            // Start polling as a fallback
            this.startPolling();
        } catch (error) {
            console.error('Error initializing Echo:', error);
            this.startPolling();
        }
    }

    /**
     * Start polling for status updates
     */
    startPolling() {
        if (this.polling || this.completed) {
            return;
        }

        this.polling = true;
        this.pollForStatus();
    }

    /**
     * Stop polling for status updates
     */
    stopPolling() {
        this.polling = false;

        if (this.pollTimer) {
            clearTimeout(this.pollTimer);
            this.pollTimer = null;
        }
    }

    /**
     * Poll the server for status updates
     */
    pollForStatus() {
        if (!this.polling || this.completed) {
            return;
        }

        fetch(this.statusEndpoint, {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            credentials: 'same-origin'
        })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`Network response was not ok: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                this.updateStatus(data);

                // Schedule the next poll if still in progress
                if (this.polling && !this.completed) {
                    this.pollTimer = setTimeout(() => this.pollForStatus(), this.pollInterval);
                }
            })
            .catch(error => {
                console.error('Error fetching import status:', error);

                // Continue polling even on error
                if (this.polling && !this.completed) {
                    this.pollTimer = setTimeout(() => this.pollForStatus(), this.pollInterval);
                }
            });
    }

    /**
     * Update the status based on received data
     * 
     * @param {Object} data The status data
     */
    updateStatus(data) {
        // Call the user-provided callback
        this.onUpdate(data);

        // Check if import is completed or failed
        if (['completed', 'failed', 'cancelled'].includes(data.status)) {
            this.completed = true;
            this.stopPolling();
        }
    }

    /**
     * Clean up resources
     */
    destroy() {
        this.stopPolling();

        // Clean up Echo listeners if needed
        if (this.useReverb && typeof window.Echo !== 'undefined') {
            try {
                window.Echo.private(`import-history.${this.importId}`).stopListening('ImportStatusChanged');
                window.Echo.private(`import-history.user.${this.userId}`).stopListening('ImportStatusChanged');
            } catch (error) {
                console.error('Error cleaning up Echo listeners:', error);
            }
        }
    }
}

// Make available globally
window.ImportStatusTracker = ImportStatusTracker; 