

<?php $__env->startSection('title', 'Chat'); ?>

<?php $__env->startPush('css'); ?>
    <style>
        .chat-sidebar-toggle {
            top: 56px;
            left: -16px;
        }

        .sidebar-toggle-btn.btn-primary-outline {
            padding: 0;
            width: 30px;
            height: 30px;
            font-size: 10px !important;
            border-radius: 15px;
        }

        .sidebar-toggle-btn.btn-primary-outline i {
            font-size: 14px
        }

        .chat-container .chat-sidebar {
            transform: translateX(0%);
            transition: transform width opacity 0.3s ease-in-out;
        }

        .chat-container.is-open-history .chat-sidebar {
            transition: transform 0.3s ease-in-out;
            transform: translateX(100%);
            margin-left: 0;
            width: 0;
            padding: 0 !important;
            opacity: 0;
            visibility: hidden;
        }

        .chat-main {
            transition: transform 0.3s ease-in-out;
        }

        /* Typing indicator styles */
        .typing-indicator {
            display: none;
            background-color: #F5F5F7;
            padding: 15px 20px;
            border-radius: 20px;
            width: fit-content;
            margin: 15px;
            position: relative;
        }

        .typing-indicator.active {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .typing-indicator span {
            height: 8px;
            width: 8px;
            background: #919AA4;
            display: block;
            border-radius: 50%;
            opacity: 0.4;
        }

        .typing-indicator span:nth-of-type(1) {
            animation: typing 1s infinite;
        }

        .typing-indicator span:nth-of-type(2) {
            animation: typing 1s infinite 0.2s;
        }

        .typing-indicator span:nth-of-type(3) {
            animation: typing 1s infinite 0.4s;
        }

        @keyframes typing {
            0% {
                opacity: 0.4;
                transform: translateY(0px);
            }

            50% {
                opacity: 1;
                transform: translateY(-4px);
            }

            100% {
                opacity: 0.4;
                transform: translateY(0px);
            }
        }

        /* Empty chat styles */
        .empty_chat {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100%;
            text-align: center;
            padding: 20px;
            background: #fff;
        }

        .empty_chat .empty-chat-icon {
            width: 180px;
            height: 180px;
            margin-bottom: 24px;
            background: rgba(26, 115, 233, 0.05);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .empty_chat .empty-chat-icon svg {
            width: 80px;
            height: 80px;
            color: var(--primary-color);
        }

        .empty_chat h5 {
            font-size: 24px;
            font-weight: 600;
            color: var(--ot-text-title);
            margin-bottom: 12px;
        }

        .empty_chat p {
            font-size: 16px;
            color: var(--ot-text-subtitle);
            max-width: 460px;
            margin-bottom: 24px;
            line-height: 1.5;
        }

        .empty_chat .suggestion-chips {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            justify-content: center;
            margin-top: 20px;
        }

        .empty_chat .suggestion-chip {
            padding: 8px 16px;
            background: rgba(26, 115, 233, 0.05);
            border: 1px solid rgba(26, 115, 233, 0.1);
            border-radius: 20px;
            color: var(--primary-color);
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .empty_chat .suggestion-chip:hover {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        /* Sources section styles */
        .sources-section {
            font-size: 0.8rem;
            background-color: rgba(240, 240, 240, 0.5);
            padding: 10px;
            border-radius: 6px;
            margin-top: 15px;
        }

        .sources-section p.fw-bold {
            color: #555;
            margin-bottom: 5px;
        }

        .sources-list {
            list-style-type: disc;
        }

        .sources-list li {
            margin-bottom: 8px;
            color: #555;
        }

        .sources-list li strong {
            color: #333;
        }

        .source-preview {
            font-size: 0.75rem;
            color: #666;
            margin-top: 2px;
            margin-left: 5px;
            padding-left: 8px;
            border-left: 2px solid #ddd;
            font-style: italic;
            line-height: 1.3;
        }
    </style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
    <div class="chat-container ot-card p-0 position-relative is-open-history">

        <!-- Chat Sidebar -->
        <div class="chat-sidebar p-4 position-relative">
            <div class="chat-header pb-3">
                <h5 class="text-20 fw-semibold text-capitalize pb-0 mb-0">Chat List</h5>
            </div>
            <div class="chat_search input-group mb-3 border radius-8 h-40px">
                <span class="input-group-text border-0 bg-transparent" id="basic-addon1">
                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <g clip-path="url(#clip0_768_5612)">
                            <path
                                d="M18.3346 18.3337L16.668 16.667M9.58464 17.5003C10.6243 17.5003 11.6537 17.2956 12.6142 16.8977C13.5747 16.4999 14.4474 15.9167 15.1826 15.1816C15.9177 14.4465 16.5008 13.5737 16.8987 12.6132C17.2965 11.6527 17.5013 10.6233 17.5013 9.58366C17.5013 8.54403 17.2965 7.51458 16.8987 6.55408C16.5008 5.59359 15.9177 4.72086 15.1826 3.98573C14.4474 3.2506 13.5747 2.66746 12.6142 2.26961C11.6537 1.87176 10.6243 1.66699 9.58464 1.66699C7.48501 1.66699 5.47137 2.50107 3.98671 3.98573C2.50204 5.47039 1.66797 7.48403 1.66797 9.58366C1.66797 11.6833 2.50204 13.6969 3.98671 15.1816C5.47137 16.6663 7.48501 17.5003 9.58464 17.5003Z"
                                stroke="#919AA4" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                        </g>
                        <defs>
                            <clipPath id="clip0_768_5612">
                                <rect width="20" height="20" fill="white" />
                            </clipPath>
                        </defs>
                    </svg>
                </span>
                <input type="text" class="form-control border-0 text-14 ps-0 bg-transparent " placeholder="Search"
                    aria-label="Search" aria-describedby="basic-addon1">
            </div>
            <div class="chat-list" id="chat-history-list">
                <!-- Chat history will be loaded here -->
            </div>
        </div>

        <!-- Chat Main Area -->
        <div class="chat-main position-relative">
            <div class="chat-sidebar-toggle position-absolute">
                <button class="sidebar-toggle-btn btn-primary-outline">
                    <i class="fa fa-chevron-left"></i>
                </button>
            </div>
            <div class="chat-main-header">
                <div class="current-chat-info">
                    <div class="chat-avatar">
                        <div class="online-status">
                            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12"
                                fill="none">
                                <circle cx="6" cy="6" r="5" fill="#16B274" stroke="white"
                                    stroke-width="2" />
                            </svg>
                        </div>
                        <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face"
                            alt="Harvey Smith">
                    </div>
                    <div>
                        <div class="current-chat-name">Harvey Smith</div>
                        <div class="current-chat-status">Active</div>
                    </div>
                </div>
                <div class="chat-actions d-flex gap-4 align-items-center">
                    <button class="bg-transparent p-0">
                        <svg width="20" height="20" viewBox="0 0 20 20" fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M18.0583 11.917L17.725 16.0837C17.6 17.3587 17.5 18.3337 15.2416 18.3337H4.7583C2.49997 18.3337 2.39997 17.3587 2.27497 16.0837L1.94163 11.917C1.87497 11.2253 2.09163 10.5837 2.4833 10.092L2.49997 10.0753C2.9583 9.51699 3.64997 9.16699 4.42497 9.16699H15.575C16.35 9.16699 17.0333 9.51699 17.4833 10.0587C17.4916 10.067 17.5 10.0753 17.5 10.0837C17.9083 10.5753 18.1333 11.217 18.0583 11.917Z"
                                stroke="#545C66" stroke-width="1.5" stroke-miterlimit="10" />
                            <path
                                d="M2.91797 9.52474V5.23307C2.91797 2.39974 3.6263 1.69141 6.45964 1.69141H7.51797C8.5763 1.69141 8.81797 2.00807 9.21797 2.54141L10.2763 3.95807C10.543 4.30807 10.7013 4.52474 11.4096 4.52474H13.5346C16.368 4.52474 17.0763 5.23307 17.0763 8.06641V9.55807M7.85964 14.1664H12.143"
                                stroke="#545C66" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round"
                                stroke-linejoin="round" />
                        </svg>

                    </button>
                    <button class="bg-transparent p-0">
                        <svg width="20" height="20" viewBox="0 0 20 20" fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M10 12.5C10.663 12.5 11.2989 12.2366 11.7678 11.7678C12.2366 11.2989 12.5 10.663 12.5 10C12.5 9.33696 12.2366 8.70107 11.7678 8.23223C11.2989 7.76339 10.663 7.5 10 7.5C9.33696 7.5 8.70107 7.76339 8.23223 8.23223C7.76339 8.70107 7.5 9.33696 7.5 10C7.5 10.663 7.76339 11.2989 8.23223 11.7678C8.70107 12.2366 9.33696 12.5 10 12.5Z"
                                stroke="#545C66" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round"
                                stroke-linejoin="round" />
                            <path
                                d="M1.66797 10.7329V9.26621C1.66797 8.39954 2.3763 7.68287 3.2513 7.68287C4.75964 7.68287 5.3763 6.61621 4.61797 5.30787C4.18464 4.55787 4.44297 3.58287 5.2013 3.14954L6.64297 2.32454C7.3013 1.93287 8.1513 2.16621 8.54297 2.82454L8.63463 2.98287C9.38463 4.29121 10.618 4.29121 11.3763 2.98287L11.468 2.82454C11.8596 2.16621 12.7096 1.93287 13.368 2.32454L14.8096 3.14954C15.568 3.58287 15.8263 4.55787 15.393 5.30787C14.6346 6.61621 15.2513 7.68287 16.7596 7.68287C17.6263 7.68287 18.343 8.39121 18.343 9.26621V10.7329C18.343 11.5995 17.6346 12.3162 16.7596 12.3162C15.2513 12.3162 14.6346 13.3829 15.393 14.6912C15.8263 15.4495 15.568 16.4162 14.8096 16.8495L13.368 17.6745C12.7096 18.0662 11.8596 17.8329 11.468 17.1745L11.3763 17.0162C10.6263 15.7079 9.39297 15.7079 8.63463 17.0162L8.54297 17.1745C8.1513 17.8329 7.3013 18.0662 6.64297 17.6745L5.2013 16.8495C4.83805 16.6404 4.57262 16.2957 4.46326 15.891C4.35389 15.4864 4.40953 15.0549 4.61797 14.6912C5.3763 13.3829 4.75964 12.3162 3.2513 12.3162C2.3763 12.3162 1.66797 11.5995 1.66797 10.7329Z"
                                stroke="#545C66" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round"
                                stroke-linejoin="round" />
                        </svg>
                    </button>
                </div>
            </div>
            
            <div class="chat-messages" id="chat-messages">

                <div id="empty-state" class="empty_chat">
                    <div class="empty-chat-icon">
                        <img src="<?php echo e(asset('static/bot2.gif')); ?>" alt="AI Assistant" width="120" height="120">
                    </div>
                    <h5>Welcome to Onest HRM Assistant</h5>
                    <p>I'm your AI-powered HR assistant. Ask me anything about employee management, leave policies, payroll,
                        or any HR-related questions. I'm here to help you find information quickly and efficiently.</p>
                    <div class="suggestion-chips">
                        <div class="suggestion-chip">How do I apply for leave?</div>
                        <div class="suggestion-chip">Show my attendance report</div>
                        <div class="suggestion-chip">Payroll calculation policy</div>
                        <div class="suggestion-chip">Employee onboarding process</div>
                    </div>
                </div>
            </div>
            
            <div class="chat-input-container">
                <div class="input-wrapper">
                    <textarea class="message-textarea" placeholder="Type your message..." id="messageInput"></textarea>
                    <div class="bottom-actions">
                        <div class="left-actions">
                            <button class="action-button" id="imageBtn">
                                <svg width="20" height="20" viewBox="0 0 20 20" fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="M15.8333 2.5H4.16667C3.24619 2.5 2.5 3.24619 2.5 4.16667V15.8333C2.5 16.7538 3.24619 17.5 4.16667 17.5H15.8333C16.7538 17.5 17.5 16.7538 17.5 15.8333V4.16667C17.5 3.24619 16.7538 2.5 15.8333 2.5Z"
                                        stroke="#545C66" stroke-width="1.5" stroke-linecap="round"
                                        stroke-linejoin="round" />
                                    <path
                                        d="M7.4987 9.16659C8.41917 9.16659 9.16536 8.42039 9.16536 7.49992C9.16536 6.57944 8.41917 5.83325 7.4987 5.83325C6.57822 5.83325 5.83203 6.57944 5.83203 7.49992C5.83203 8.42039 6.57822 9.16659 7.4987 9.16659Z"
                                        stroke="#545C66" stroke-width="1.5" stroke-linecap="round"
                                        stroke-linejoin="round" />
                                    <path
                                        d="M17.5 12.5001L14.9283 9.92841C14.6158 9.61595 14.1919 9.44043 13.75 9.44043C13.3081 9.44043 12.8842 9.61595 12.5717 9.92841L5 17.5001"
                                        stroke="#545C66" stroke-width="1.5" stroke-linecap="round"
                                        stroke-linejoin="round" />
                                </svg>

                                <span>Use Image</span>
                            </button>
                            <button class="action-button" id="attachBtn">
                                <svg width="20" height="20" viewBox="0 0 20 20" fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path d="M11.0273 16.8767L17.499 10.25" stroke="#545C66" stroke-width="1.5"
                                        stroke-linecap="round" stroke-linejoin="round" />
                                    <path
                                        d="M13.3338 5.0001L6.32209 12.1551C6.00964 12.4676 5.83411 12.8915 5.83411 13.3334C5.83411 13.7754 6.00964 14.1992 6.32209 14.5118C6.63464 14.8242 7.05848 14.9997 7.50042 14.9997C7.94236 14.9997 8.36621 14.8242 8.67876 14.5118L15.6904 7.35677C16.3153 6.73168 16.6664 5.88398 16.6664 5.0001C16.6664 4.11622 16.3153 3.26853 15.6904 2.64344C15.0653 2.01853 14.2176 1.66748 13.3338 1.66748C12.4499 1.66748 11.6022 2.01853 10.9771 2.64344L3.96459 9.7976C3.02683 10.7354 2.5 12.0072 2.5 13.3334C2.5 14.6596 3.02683 15.9315 3.96459 16.8693C4.90235 17.807 6.17423 18.3339 7.50042 18.3339C8.82662 18.3339 10.0985 17.807 11.0363 16.8693"
                                        stroke="#545C66" stroke-width="1.5" stroke-linecap="round"
                                        stroke-linejoin="round" />
                                </svg>

                                <span>Add Attachment</span>
                            </button>
                            <button class="action-button" id="emojiBtn">
                                <svg width="20" height="20" viewBox="0 0 20 20" fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <g clip-path="url(#clip0_768_5681)">
                                        <path
                                            d="M10.0013 18.3334C14.6037 18.3334 18.3346 14.6025 18.3346 10.0001C18.3346 5.39771 14.6037 1.66675 10.0013 1.66675C5.39893 1.66675 1.66797 5.39771 1.66797 10.0001C1.66797 14.6025 5.39893 18.3334 10.0013 18.3334Z"
                                            stroke="#545C66" stroke-width="1.5" stroke-linecap="round"
                                            stroke-linejoin="round" />
                                        <path
                                            d="M6.66797 11.6667C6.66797 11.6667 7.91797 13.3334 10.0013 13.3334C12.0846 13.3334 13.3346 11.6667 13.3346 11.6667"
                                            stroke="#545C66" stroke-width="1.5" stroke-linecap="round"
                                            stroke-linejoin="round" />
                                        <path d="M7.5 7.5H7.50875" stroke="#545C66" stroke-width="1.5"
                                            stroke-linecap="round" stroke-linejoin="round" />
                                        <path d="M12.5 7.5H12.5088" stroke="#545C66" stroke-width="1.5"
                                            stroke-linecap="round" stroke-linejoin="round" />
                                    </g>
                                    <defs>
                                        <clipPath id="clip0_768_5681">
                                            <rect width="20" height="20" fill="white" />
                                        </clipPath>
                                    </defs>
                                </svg>

                                <span>Emoji</span>
                            </button>
                        </div>

                        <button class="sent-btn btn-primary-fill radius-8 " id="sendBtn">
                            <svg width="18" height="18" viewBox="0 0 18 18" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M2.78735 2.28591C2.7199 2.25298 2.64408 2.24114 2.5698 2.25193C2.49552 2.26272 2.42621 2.29564 2.37092 2.3464C2.31562 2.39717 2.2769 2.46341 2.25981 2.5365C2.24272 2.60959 2.24805 2.68614 2.2751 2.75616L4.40735 8.47641C4.5331 8.81408 4.5331 9.18573 4.40735 9.52341L2.27585 15.2437C2.24893 15.3136 2.24368 15.39 2.26076 15.463C2.27784 15.5359 2.31647 15.6021 2.37164 15.6528C2.4268 15.7035 2.49595 15.7364 2.57008 15.7473C2.64422 15.7582 2.71992 15.7466 2.78735 15.7139L16.2873 9.33891C16.3516 9.30853 16.4058 9.26055 16.4438 9.20053C16.4818 9.14052 16.502 9.07094 16.502 8.99991C16.502 8.92887 16.4818 8.85929 16.4438 8.79928C16.4058 8.73926 16.3516 8.69128 16.2873 8.66091L2.78735 2.28591Z"
                                    stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                                <path d="M4.5 9H16.5" stroke="white" stroke-width="1.5" stroke-linecap="round"
                                    stroke-linejoin="round" />
                            </svg>
                            Send Now
                        </button>
                    </div>
                </div>
            </div>
            <input type="file" id="imageInput" accept="image/*" style="display: none;">
            <input type="file" id="fileInput" accept="*/*" style="display: none;">
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
    <script>
        $(document).ready(function() {
            const $messageInput = $('#messageInput');
            const $sendBtn = $('#sendBtn');
            const $sidebarToggleBtn = $('.sidebar-toggle-btn');
            const $chatContainer = $('.chat-container');
            $sidebarToggleBtn.on('click', function() {
                $(this).find('i').toggleClass('fa-chevron-left fa-chevron-right');
                $chatContainer.toggleClass('is-open-history');

            });

            // Auto-resize textarea
            $messageInput.on('input', function() {
                $(this).css({
                    height: 'auto',
                    'max-height': '300px'
                });
                $(this).css('height', this.scrollHeight + 'px');
            });

            // Button event listeners
            $('#imageBtn').on('click', function() {
                $('#imageInput').click();
            });

            $('#attachBtn').on('click', function() {
                $('#fileInput').click();
            });

            $('#emojiBtn').on('click', function() {
                console.log('Emoji picker clicked');
            });

            // File input handlers
            $('#imageInput').on('change', function(e) {
                if (e.target.files.length > 0) {
                    console.log('Image selected:', e.target.files[0].name);
                }
            });

            $('#fileInput').on('change', function(e) {
                if (e.target.files.length > 0) {
                    console.log('File selected:', e.target.files[0].name);
                }
            });

            function sendMessage() {
                const messageInput = document.getElementById('messageInput');
                const message = messageInput.value.trim();

                if (!message || isLoading) return;

                isLoading = true;

                // Add user message to chat
                addMessage('user', message);

                // Clear input
                messageInput.value = '';

                // Show loading indicator
                showLoadingIndicator();

                // Get selected entity types
                const selectedTypes = [];
                document.querySelectorAll('.entity-type:checked').forEach(function(checkbox) {
                    selectedTypes.push(checkbox.value);
                });

                // Send message to API
                fetch('/api/universal-chatbot/chat/message', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': csrfToken
                        },
                        body: JSON.stringify({
                            message: message,
                            conversation_id: conversationId,
                            filters: {
                                types: selectedTypes
                            }
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        // Store conversation ID for continuing the conversation
                        if (!conversationId) {
                            conversationId = data.conversation_id;
                            // Update conversation history after creating a new conversation
                            loadConversationHistory();
                        }

                        // Remove loading indicator
                        removeLoadingIndicator();

                        // Add bot response to chat
                        addMessage('bot', data.response, data.sources);

                        isLoading = false;
                    })
                    .catch(error => {
                        console.error('Error sending message:', error);

                        // Remove loading indicator
                        removeLoadingIndicator();

                        // Add error message
                        addMessage('bot',
                            'Sorry, I encountered an error processing your request. Please try again later.'
                        );

                        isLoading = false;
                    });
            }

            // Handle suggestion chip clicks
            $('.suggestion-chip').on('click', function() {
                const chipText = $(this).text();
                $('#messageInput').val(chipText).trigger('input');
            });



            //my codes
            let conversationId = null;
            let isLoading = false;

            // Set up CSRF token for all AJAX requests
            const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

            // Load conversation history when page loads
            loadConversationHistory();

            // Send message when button is clicked
            document.getElementById('sendBtn').addEventListener('click', sendMessage);

            // Send message when Enter key is pressed
            document.getElementById('messageInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    sendMessage();
                }
            });

            function loadConversation(id) {
                // Clear current conversation
                document.getElementById('chat-messages').innerHTML = '';

                // Set active conversation
                conversationId = id;

                // Update active state in sidebar
                const chatItems = document.querySelectorAll('.chat-item');
                chatItems.forEach(item => {
                    item.classList.remove('active');
                    if (item.dataset.conversationId === id.toString()) {
                        item.classList.add('active');
                    }
                });

                // Show loading indicator
                showLoadingIndicator();

                fetch(`/api/universal-chatbot/chat/conversation/${id}`, {
                        method: 'GET',
                        headers: {
                            'X-CSRF-TOKEN': csrfToken
                        }
                    })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`HTTP error! Status: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        // Remove loading indicator
                        removeLoadingIndicator();

                        // Clear any empty state
                        const emptyState = document.getElementById('empty-state');
                        if (emptyState) {
                            emptyState.style.display = 'none';
                        }

                        // Check if data is valid
                        if (!data) {
                            throw new Error('No data received from server');
                        }

                        console.log('Conversation data:', data); // Debug log

                        // Format messages for display
                        if (data.messages && data.messages.length > 0) {
                            data.messages.forEach(function(msg) {
                                if (!msg) return; // Skip if message is null

                                try {
                                    addMessageWithTimestamp('user', msg.message_text, [], msg
                                        .created_at);

                                    if (msg.response_text) {
                                        addMessageWithTimestamp('bot', msg.response_text, msg
                                            .source_chunks, msg.created_at);
                                    }
                                } catch (err) {
                                    console.error('Error displaying message:', err, msg);
                                }
                            });
                        } else {
                            // If no messages, show empty state
                            document.getElementById('empty-state').style.display = 'flex';
                        }

                        // Scroll to bottom
                        scrollToBottom();
                    })
                    .catch(error => {
                        console.error('Error loading conversation:', error);
                        removeLoadingIndicator();

                        // Show error message
                        const chatMessages = document.getElementById('chat-messages');
                        chatMessages.innerHTML = `
                            <div class="alert alert-danger m-3">
                                Error loading conversation. Please try again.
                                <br><small>${error.message || 'Unknown error'}</small>
                            </div>
                        `;
                    });
            }

            /**
             * Add a message with a specific timestamp
             */
            function addMessageWithTimestamp(type, content, sources = [], timestamp = null) {
                try {
                    // Validate inputs
                    if (!content) {
                        console.error('Empty message content received');
                        content = '[Empty message]';
                    }

                // Hide empty state if it's visible
                const emptyState = document.getElementById('empty-state');
                if (emptyState) {
                    emptyState.style.display = 'none';
                }

                // Create message element
                const isUser = type === 'user';

                    // Format the timestamp
                    let formattedTime;
                    if (timestamp) {
                        const date = new Date(timestamp);
                        formattedTime = formatTime(date);
                    } else {
                        const currentTime = new Date();
                        formattedTime = formatTime(currentTime);
                    }

                // Format the content if it's a bot message
                let formattedContent = content;
                if (!isUser) {
                        try {
                    // Format code blocks (text between triple backticks)
                    formattedContent = formattedContent.replace(/```(.*?)\n([\s\S]*?)```/g, function(match,
                        language, code) {
                        return `<pre><code class="language-${language}">${code.trim()}</code></pre>`;
                    });

                    // Format inline code (text between single backticks)
                    formattedContent = formattedContent.replace(/`([^`]+)`/g, '<code>$1</code>');

                // Format headings (bold text followed by colon)
                formattedContent = formattedContent.replace(/\*\*(.*?)\*\*:/g, '<strong>$1:</strong>');

                // Format bold text
                formattedContent = formattedContent.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

                // Format lists (lines starting with * or - followed by space)
                formattedContent = formattedContent.replace(/^\s*[\*\-]\s+(.*?)$/gm, '<li>$1</li>');

                // Wrap lists in <ul> tags
                if (formattedContent.includes('<li>')) {
                    formattedContent = formattedContent.replace(/(<li>.*?<\/li>)+/gs,
                        '<ul class="mb-2 ml-20">$&</ul>');
                }

                // Format headings (lines with # at the beginning)
                formattedContent = formattedContent.replace(/^#\s+(.*?)$/gm, '<h1>$1</h1>');
                formattedContent = formattedContent.replace(/^##\s+(.*?)$/gm, '<h2>$1</h2>');
                formattedContent = formattedContent.replace(/^###\s+(.*?)$/gm, '<h3>$1</h3>');

                // Format paragraphs (double line breaks)
                formattedContent = formattedContent.replace(/\n\n/g, '</p><p class="mb-2">');

                // Format single line breaks within paragraphs
                formattedContent = formattedContent.replace(/([^\n])\n([^\n])/g, '$1<br>$2');

                // Format disclaimers (text starting with "Disclaimer:")
                const disclaimerRegex = /(?:^|\n)(Disclaimer:.*?)(?:\n\n|$)/gs;
                        formattedContent = formattedContent.replace(disclaimerRegex, function(match,
                            disclaimer) {
                    return `<div class="disclaimer">${disclaimer}</div>`;
                });

                // Wrap in paragraph tags if not already
                if (!formattedContent.startsWith('<')) {
                    formattedContent = '<p class="mb-10">' + formattedContent + '</p>';
                }

                // Clean up any empty paragraphs
                formattedContent = formattedContent.replace(/<p class="mb-2"><\/p>/g, '');

                // Add spacing between sections
                formattedContent = formattedContent.replace(/<\/ul>\s*<p/g, '</ul><p class="mt-3"');
                        formattedContent = formattedContent.replace(/<\/h[1-3]>\s*<p/g,
                            '</h$1><p class="mt-2"');

                // Format key points sections
                formattedContent = formattedContent.replace(/Key (things|points) to know about/gi,
                    '<strong>Key points about</strong>');
                    } catch (err) {
                        console.error('Error formatting message:', err);
                        // Use original content if formatting fails
                        formattedContent = content;
                    }
            }

            const botMessageClass = isUser ? 'sent' : '';
                let messageHtml = `
                                            <div class="message ${botMessageClass}">

                            <div class="message-content">
                                                                            <div class="message-content-text">
                                                    ${formattedContent}
                                                                                    
                                                                                    ${sources && sources.length > 0 ? `
                                                        <div class="mt-2 pt-2 border-top sources-section">
                            <p class="mb-1 fw-bold">Sources:</p>
                                                            <ul class="mb-0 ps-3 sources-list">
                                                                ${sources.map(source => {
                                                                    try {
                                                                        const entityType = source.display_type || (source.type ? source.type.split('\\').pop() : 'Reference');
                                                                        const sourceName = source.name || 'Reference';
                                                                        const sourceText = source.text ? `<div class="source-preview">${source.text}</div>` : '';
                                                                        return `<li><strong>${sourceName}</strong>${sourceText}</li>`;
                                                                    } catch (err) {
                                                                        console.error('Error processing source:', err, source);
                                                                        return '<li>Error processing source</li>';
                                                                    }
                                                                }).join('')}
                            </ul>
                                                        </div>
                                                        ` : ''}
                                                                                </div>
                                                                                <div class="message-time">${formattedTime}</div>
                        </div>
                    `;

                messageHtml += `
                        </div>
                    </div>
                `;

                // Append message to chat
                const chatMessages = document.getElementById('chat-messages');
                chatMessages.insertAdjacentHTML('beforeend', messageHtml);

                // Scroll to bottom
                scrollToBottom();
            } catch (error) {
                console.error('Error adding message:', error);
            }
        }

        // Update the addMessage function to use addMessageWithTimestamp
        function addMessage(type, content, sources = []) {
            addMessageWithTimestamp(type, content, sources);
        }

        /**
         * Format time to display in messages
         */
        function formatTime(date) {
            let hours = date.getHours();
            let minutes = date.getMinutes();
            const ampm = hours >= 12 ? 'PM' : 'AM';

            hours = hours % 12;
            hours = hours ? hours : 12; // the hour '0' should be '12'
            minutes = minutes < 10 ? '0' + minutes : minutes;

            return hours + ':' + minutes + ' ' + ampm;
            }

            function showLoadingIndicator() {
            const loadingHtml =
                `
                            <!-- Typing Indicator -->
                            <div id="loading-indicator" class="typing-indicator active">
                                <span></span>
                                <span></span>
                                <span></span>
                    </div>
                `;

                const chatMessages = document.getElementById('chat-messages');
                chatMessages.insertAdjacentHTML('beforeend', loadingHtml);
                scrollToBottom();
            }

            function removeLoadingIndicator() {
                const loadingIndicator = document.getElementById('loading-indicator');
                if (loadingIndicator) {
                    loadingIndicator.remove();
                }
            }

            function scrollToBottom() {
                const chatMessages = document.getElementById('chat-messages');
                chatMessages.scrollTop = chatMessages.scrollHeight;
            }

        function loadConversationHistory() {
            const chatHistoryList = document.getElementById('chat-history-list');
            chatHistoryList.innerHTML = '<div class="p-3 text-center">Loading conversations...</div>';

            fetch('/api/universal-chatbot/chat/conversations', {
                        method: 'GET',
                        headers: {
                            'X-CSRF-TOKEN': csrfToken
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                    chatHistoryList.innerHTML = '';

                    if (data.conversations && data.conversations.length > 0) {
                        data.conversations.forEach(conversation => {
                            // Format date
                            const date = new Date(conversation.updated_at);
                            const formattedDate = date.toLocaleDateString('en-US', {
                                month: 'short',
                                day: 'numeric'
                            });

                            // Create chat item
                            const chatItem = document.createElement('div');
                            chatItem.className = 'chat-item';
                            chatItem.dataset.conversationId = conversation.id;
                            if (conversation.id === conversationId) {
                                chatItem.classList.add('active');
                            }

                            chatItem.innerHTML =
                                `
                                                                                                                                        <div class="d-flex align-items-center">
                                                                                                                                            <div class="chat-info">
                                                                                                                                                <div class="chat-name-row">
                                                                                                                                                    <div class="chat-name">${conversation.title}</div>
                                                                                                                                                </div>
                                                                                                                                                <div class="chat-message">Conversation #${conversation.id}</div>
                                                                                                                                            </div>
                                                                                                                                            <div class="chat-meta">
                                                                                                                                                <div class="chat-time">${formattedDate}</div>
                                                                                                                                            </div>
                                                                                                                                        </div>
                                                                                                                                    `;

                            // Add click event
                            chatItem.addEventListener('click', function() {
                                loadConversation(conversation.id);
                            });

                            chatHistoryList.appendChild(chatItem);
                        });

                        // Add a "New Chat" button at the top
                        const newChatButton = document.createElement('div');
                        newChatButton.className = 'chat-item new-chat-button mb-3';
                        newChatButton.innerHTML =
                            `
                                                                                                                                        <div class="d-flex align-items-center justify-content-center p-2">
                                                                                                                                            <div class="d-flex align-items-center">
                                                                                                                                                <i class="las la-plus-circle  me-2"></i>
                                                                                                                                                <div>New Chat</div>
                                                                                                                                            </div>
                                                                                                                                        </div>
                                                                                                                                    `;

                            newChatButton.addEventListener('click', function() {
                                // Clear conversation ID to start a new chat
                                conversationId = null;

                                // Clear messages
                                document.getElementById('chat-messages').innerHTML = '';

                                // Show empty state
                                document.getElementById('empty-state').style.display = 'flex';

                                // Update active state
                                document.querySelectorAll('.chat-item').forEach(item => {
                                    item.classList.remove('active');
                                });
                                newChatButton.classList.add('active');
                            });

                            // Insert at the beginning
                            chatHistoryList.insertBefore(newChatButton, chatHistoryList.firstChild);
                        } else {
                            chatHistoryList.innerHTML =
                                '<div class="p-3 text-center text-muted">No conversations yet</div>';
                        }
                    })
                    .catch(error => {
                        console.error('Error loading conversation history:', error);
                        document.getElementById('chat-history-list').innerHTML =
                            '<div class="p-3 text-center text-danger">Failed to load conversations</div>';
                    });
            }
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('backend.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\onest-hrm\resources\views/vendor/universal_chatbot/chat.blade.php ENDPATH**/ ?>