<?php

namespace UniversalChatbot\Services;

use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class QueryAnalyzer
{
    protected GeminiApiClient $geminiClient;
    protected DatabaseSchemaService $schemaService;

    public function __construct(GeminiApiClient $geminiClient, DatabaseSchemaService $schemaService)
    {
        $this->geminiClient = $geminiClient;
        $this->schemaService = $schemaService;
    }

    /**
     * Analyze the user's question to determine if a database query is needed
     *
     * @param string $prompt User's question
     * @param array $conversationHistory Previous conversation
     * @param array|null $followUpContext Follow-up question context
     * @return array Analysis result with query decision and SQL
     */
    public function analyzeQuestion(string $prompt, array $conversationHistory = [], ?array $followUpContext = null): array
    {
        $currentDate = Carbon::now()->format('Y-m-d');

        // Create the analysis prompt
        $analysisPrompt = $this->createAnalysisPrompt($prompt, $currentDate, $conversationHistory, $followUpContext);

        // Call Gemini API
        $response = $this->geminiClient->analyzeQuestion($analysisPrompt);

        if (!$response['success']) {
            Log::error('Failed to analyze question: ' . ($response['error'] ?? 'Unknown error'));

            return [
                'needs_database_query' => false,
                'sql_query' => null,
                'reason' => 'API call failed',
            ];
        }

        // Parse the response
        return $this->parseAnalysisResponse($response['response']);
    }

    /**
     * Create prompt for question analysis
     *
     * @param string $prompt User's question
     * @param string $currentDate Current date
     * @param array $conversationHistory Previous conversation
     * @param array|null $followUpContext Follow-up question context
     * @return string Analysis prompt
     */
    protected function createAnalysisPrompt(string $prompt, string $currentDate, array $conversationHistory = [], ?array $followUpContext = null): string
    {
        // Include database schema
        $dbSchema = $this->schemaService->getDatabaseSchemaInfo();

        // Build conversation context
        $conversationContext = '';
        if (!empty($conversationHistory)) {
            $conversationContext = "Previous conversation:\n";
            foreach (array_slice($conversationHistory, -3) as $exchange) {
                $conversationContext .= "User: {$exchange['message_text']}\n";
                $conversationContext .= "Assistant: {$exchange['response_text']}\n";
            }
        }

        // Add follow-up context if available
        $followUpInfo = '';
        if ($followUpContext) {
            $followUpInfo = <<<EOT
FOLLOW-UP CONTEXT:
This appears to be a follow-up question to a previous query.

Previous question: {$followUpContext['previous_query']}
Previous SQL query: {$followUpContext['previous_sql']}

You may need to modify the previous query to answer this follow-up question.
EOT;
        }

        return <<<EOT
You are an AI assistant for an HRM system that can analyze questions and generate SQL queries when needed.

DATABASE SCHEMA:
{$dbSchema}

CURRENT DATE: {$currentDate}

{$conversationContext}

{$followUpInfo}

USER QUESTION: {$prompt}

Your task is to:
1. Analyze if this question requires database access to be answered accurately
2. If yes, generate a PostgreSQL SQL query that will retrieve the necessary data

INSTRUCTIONS:
- For questions about leave requests, employees, departments, etc., generate a SQL query
- For general questions, greetings, or questions that don't need data, don't generate a query
- For time-sensitive queries (about "today", "this month", etc.), always generate a SQL query
- Make sure your SQL query is secure, using only the tables and columns in the schema
- Format your response as valid JSON with the following structure:

{
  "needs_database_query": true/false,
  "sql_query": "SELECT ... FROM ... WHERE ...",
  "explanation": "Brief explanation of your decision"
}

RESPONSE:
EOT;
    }

    /**
     * Parse the analysis response from the AI
     *
     * @param array $response Raw API response
     * @return array Parsed analysis result
     */
    protected function parseAnalysisResponse(array $response): array
    {
        try {
            $text = $this->geminiClient->extractTextFromResponse($response);

            if (!$text) {
                throw new \Exception('Invalid response format');
            }

            // Extract JSON from the response
            preg_match('/\{.*\}/s', $text, $matches);

            if (empty($matches)) {
                throw new \Exception('No JSON found in response');
            }

            $jsonData = json_decode($matches[0], true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new \Exception('Invalid JSON: ' . json_last_error_msg());
            }

            return [
                'needs_database_query' => $jsonData['needs_database_query'] ?? false,
                'sql_query' => $jsonData['sql_query'] ?? null,
                'explanation' => $jsonData['explanation'] ?? 'No explanation provided',
            ];
        } catch (\Exception $e) {
            Log::error('Error parsing analysis response: ' . $e->getMessage());

            return [
                'needs_database_query' => false,
                'sql_query' => null,
                'reason' => 'Failed to parse response',
            ];
        }
    }
}
