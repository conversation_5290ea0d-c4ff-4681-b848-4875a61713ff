// Define constants for URL and jQuery elements
const tbody_url = $('#tbody_url').val();
const $page = $("#page");
const $entries = $('#entries');
const $search = $('#search');
const $departmentId = $('#departmentId');

// Function to build the query string
function buildQueryString() {
    const queryParams = [];

    // Append parameters conditionally
    if ($entries.val()) {
        queryParams.push('entries=' + $entries.val());
    }
    if ($search.val()) {
        queryParams.push('search=' + $search.val());
    }
    if ($departmentId.val()) {
        queryParams.push('department_id=' + $departmentId.val());
    }
    if (__date_range['from']) {
        queryParams.push('from=' + __date_range['from']);
    }
    if (__date_range['to']) {
        queryParams.push('to=' + __date_range['to']);
    }

    // Always include 'page' parameter
    queryParams.push('page=' + $page.val());

    // Join the query parameters with '&'
    return queryParams.join('&');
}

// Function to update the user document
function renderSearchResult() {
    const queryString = buildQueryString();

    // Construct the complete URL
    const current_url = `${tbody_url}?${queryString}`;

    updateTbody(current_url);
}

// Function to update the table body via AJAX
function updateTbody(current_url) {
    $.ajax({
        url: current_url,
        method: 'GET',
        success: function (data) {
            $('._ajaxData').empty().html(data.view);
        },
        error: function (error) {
            console.error(error);
        }
    });
}



// Function for pagination
function ModulePagination(page) {
    $page.val(page);
    renderSearchResult();
}
// Initial call to renderSearchResult
renderSearchResult();