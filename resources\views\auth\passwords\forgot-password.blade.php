@extends('frontend.auth.app')
@section('title', 'Forgot password')

@section('content')
    <div class="form-heading mb-40 text-center">
        <h3 class="title mb-8 text-capitalize">{{ _trans('common.forgot password') }}</h3>
        <p class="subtitle mb-0 text-capitalize">{{ _trans('auth.Enter your email to recover your password') }}</p>
    </div>
    <input type="hidden" id="send_reset_link_successfully" value="{{ _trans('auth.Send reset link successfully') }}" />

    <div class="auth-form d-flex justify-content-center align-items-start flex-column gap-20 w-100 mb-20">

        <!-- username input field  -->
        <div class="input-field-group w-100">
            <label for="email">{{ _trans('common.Email') }} <sup>*</sup></label><br>
            <div class="custom-input-field">
                <input type="email" name="email" id="email" class="form-control"
                    placeholder="{{ _trans('common.Email') }}">
            </div>
            <p class="text-danger cus-error __email small-text"></p>
        </div>
        <!-- submit button  -->

        <button type="submit" class="submit-btn submit_btn-primary-fill  w-100 __login_btn d-flex align-items-center justify-content-center gap-8 mb-6">
            {{ _trans('common.Send Code') }}
        </button>
    </div>
    <!-- End form -->

    <div class="authenticate-now mb-0">
        <a class="link-text d-flex align-items-center gap-10" href="{{ route('adminLogin') }}"> 
            <div class="icon icon-size-20 text-title">
                <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none"><path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="1.5" d="M9.57 5.93L3.5 12l6.07 6.07M20.5 12H3.67"></path></svg>
            </div>
           <span> {{ _trans('auth.Back to Sign in') }}</span>
        </a>
    </div>

@endsection
@section('script')
    <script src="{{ global_asset('/') }}frontend/assets/jquery.min.js"></script>
    <script src="{{ global_asset('/') }}frontend/assets/bootstrap/bootstrap.min.js"></script>
    <script src="{{ global_asset('/') }}backend/js/select2.min.js"></script>
    @include('backend.partials.message')
    <script src="{{ global_asset('js/toastr.js') }}"></script>
    {!! Toastr::message() !!}
    <script src="{{ global_asset('frontend/js/auth.js') }}"></script>
@endsection
