<?php

namespace UniversalChatbot\Services;

use UniversalChatbot\Models\ChatbotQuery;

class PerformanceTracker
{
    protected array $metrics = [];
    protected float $startTime;

    public function __construct()
    {
        $this->startTime = microtime(true);
    }

    /**
     * Start tracking a specific metric
     *
     * @param string $metricName Name of the metric to track
     * @return void
     */
    public function startMetric(string $metricName): void
    {
        $this->metrics[$metricName] = [
            'start_time' => microtime(true),
            'end_time' => null,
            'duration' => null,
        ];
    }

    /**
     * End tracking a specific metric
     *
     * @param string $metricName Name of the metric to stop tracking
     * @return float Duration in seconds
     */
    public function endMetric(string $metricName): float
    {
        if (!isset($this->metrics[$metricName])) {
            return 0.0;
        }

        $endTime = microtime(true);
        $this->metrics[$metricName]['end_time'] = $endTime;
        $this->metrics[$metricName]['duration'] = round($endTime - $this->metrics[$metricName]['start_time'], 4);

        return $this->metrics[$metricName]['duration'];
    }

    /**
     * Get duration for a specific metric
     *
     * @param string $metricName Name of the metric
     * @return float|null Duration in seconds or null if not found
     */
    public function getMetricDuration(string $metricName): ?float
    {
        return $this->metrics[$metricName]['duration'] ?? null;
    }

    /**
     * Get all tracked metrics
     *
     * @return array All metrics with their durations
     */
    public function getAllMetrics(): array
    {
        return $this->metrics;
    }

    /**
     * Get total execution time since tracker was created
     *
     * @return float Total time in seconds
     */
    public function getTotalExecutionTime(): float
    {
        return round(microtime(true) - $this->startTime, 4);
    }

    /**
     * Track query performance metrics and save to database
     *
     * @param ChatbotQuery $queryRecord Query record to update
     * @param array|null $analysisResponse Analysis response data
     * @param array|null $queryResults Query execution results
     * @return void
     */
    public function trackQueryMetrics(ChatbotQuery $queryRecord, ?array $analysisResponse = null, ?array $queryResults = null): void
    {
        $totalExecutionTime = $this->getTotalExecutionTime();

        // Prepare metrics data
        $metrics = [
            'total_execution_time' => $totalExecutionTime,
            'analysis_time' => $analysisResponse['analysis_time'] ?? $this->getMetricDuration('analysis'),
            'query_execution_time' => $queryResults['execution_time'] ?? $this->getMetricDuration('query_execution'),
            'formatting_time' => $this->getMetricDuration('formatting'),
            'validation_time' => $this->getMetricDuration('validation'),
        ];

        // Remove null values
        $metrics = array_filter($metrics, function ($value) {
            return $value !== null;
        });

        // Update the query record with performance metrics
        $queryRecord->metadata = array_merge($queryRecord->metadata ?? [], [
            'performance_metrics' => $metrics,
            'tracked_metrics' => $this->getAllMetrics(),
        ]);

        $queryRecord->save();
    }

    /**
     * Create a performance summary
     *
     * @return array Performance summary
     */
    public function getPerformanceSummary(): array
    {
        $summary = [
            'total_execution_time' => $this->getTotalExecutionTime(),
            'metrics_count' => count($this->metrics),
            'slowest_operation' => null,
            'fastest_operation' => null,
        ];

        if (!empty($this->metrics)) {
            $durations = array_filter(array_column($this->metrics, 'duration'));
            
            if (!empty($durations)) {
                $maxDuration = max($durations);
                $minDuration = min($durations);

                // Find slowest operation
                foreach ($this->metrics as $name => $metric) {
                    if ($metric['duration'] === $maxDuration) {
                        $summary['slowest_operation'] = [
                            'name' => $name,
                            'duration' => $maxDuration,
                        ];
                        break;
                    }
                }

                // Find fastest operation
                foreach ($this->metrics as $name => $metric) {
                    if ($metric['duration'] === $minDuration) {
                        $summary['fastest_operation'] = [
                            'name' => $name,
                            'duration' => $minDuration,
                        ];
                        break;
                    }
                }
            }
        }

        return $summary;
    }

    /**
     * Log performance metrics
     *
     * @param string $context Context for the log entry
     * @return void
     */
    public function logPerformance(string $context = 'Query execution'): void
    {
        $summary = $this->getPerformanceSummary();
        
        \Illuminate\Support\Facades\Log::info("Performance metrics for {$context}", [
            'total_time' => $summary['total_execution_time'],
            'metrics' => $this->getAllMetrics(),
            'summary' => $summary,
        ]);
    }

    /**
     * Check if any metric exceeds a threshold
     *
     * @param float $thresholdSeconds Threshold in seconds
     * @return array Metrics that exceed the threshold
     */
    public function getSlowMetrics(float $thresholdSeconds = 5.0): array
    {
        $slowMetrics = [];

        foreach ($this->metrics as $name => $metric) {
            if ($metric['duration'] && $metric['duration'] > $thresholdSeconds) {
                $slowMetrics[$name] = $metric;
            }
        }

        return $slowMetrics;
    }

    /**
     * Reset all metrics
     *
     * @return void
     */
    public function reset(): void
    {
        $this->metrics = [];
        $this->startTime = microtime(true);
    }

    /**
     * Add a custom metric value
     *
     * @param string $metricName Name of the metric
     * @param float $value Value to record
     * @param array $metadata Additional metadata
     * @return void
     */
    public function addCustomMetric(string $metricName, float $value, array $metadata = []): void
    {
        $this->metrics[$metricName] = [
            'start_time' => null,
            'end_time' => null,
            'duration' => $value,
            'metadata' => $metadata,
        ];
    }

    /**
     * Get metrics formatted for display
     *
     * @return string Formatted metrics string
     */
    public function getFormattedMetrics(): string
    {
        $output = "Performance Metrics:\n";
        $output .= "Total Execution Time: {$this->getTotalExecutionTime()}s\n";

        foreach ($this->metrics as $name => $metric) {
            if ($metric['duration']) {
                $output .= "- {$name}: {$metric['duration']}s\n";
            }
        }

        return $output;
    }
}
