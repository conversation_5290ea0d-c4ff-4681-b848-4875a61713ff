@extends('backend.layouts.app')
@section('title', @$data['title'])

@section('content')

<div class="ot-card">
    <!-- toolbar table start -->
    <div
        class="table-toolbar d-flex flex-wrap gap-2 flex-xl-row justify-content-center justify-content-xxl-between align-content-center pb-3">
        <div class="align-self-center">
            <div class="d-flex flex-wrap gap-2  flex-lg-row justify-content-center align-content-center">
                <!-- show per page -->
                <div class="align-self-center">
                    <label>
                        <span class="mr-8">{{ _trans('common.Show') }}</span>
                        <select class="form-select d-inline-block" id="entries" onchange="absentDatatable()">
                            @include('backend.partials.tableLimit')
                        </select>
                        <span class="ml-8">{{ _trans('common.Entries') }}</span>
                    </label>
                </div>



                <div class="align-self-center d-flex flex-wrap gap-2">

                    <div class="align-self-center">
                        <div class="search-box">
                            <input type="date" class="form-control pe-2" id="date" onchange="absentDatatable()"
                                value="{{ date('Y-m-d') }}" autocomplete="off">
                        </div>
                    </div>
                    <!-- Designation -->
                    <div class="align-self-center">
                        <div class="dropdown dropdown-designation">
                            <button type="button" class="btn-designation" data-bs-toggle="dropdown"
                                aria-expanded="false" data-bs-auto-close="false">
                                <span class="icon"><i class="fa-solid fa-user-shield"></i></span>

                                <span class="d-none d-xl-inline">{{ _trans('common.Department') }}</span>
                            </button>

                            <div class="dropdown-menu align-self-center ">
                                <select name="department_id" id="department_id" class="form-control pl-2 select2 w-100"
                                    onchange="absentDatatable()">
                                    <option value=" " selected>
                                        {{ _trans('common.Select department') }}</option>
                                    @foreach ($data['departments'] as $role)
                                    <option value="{{ $role->id }}">{{ @$role->title }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                    </div>
                    <!-- search -->
                    <div class="align-self-center">
                        <div class="search-box d-flex">
                            <input class="form-control" placeholder="{{ _trans('common.Search') }}" name="search"
                                onkeyup="absentDatatable()" autocomplete="off">
                            <span class="icon"><i class="fa-solid fa-magnifying-glass"></i></span>
                        </div>
                    </div>
                    <div class="align-self-center">
                        <a href="{{ route('attendance.index') }}" role="button" class="btn btn-success py-2"
                            data-bs-title="Present List">
                            <span><i class="las la-check-square"></i></span>
                            <span class="d-none d-xl-inline">{{ _trans('common.Present') }}</span>
                        </a>
                    </div>
                </div>

            </div>
        </div>
        <!-- export -->
        @include('backend.partials.buttons')
    </div>
    <!-- toolbar table end -->
    <!--  table start -->
    <div class="table-responsive min-height-300">
        @include('backend.partials.table')
    </div>
    <!--  table end -->
</div>
@endsection
@section('script')
@include('backend.partials.table_js')

<script>
    //absent_table start 
        function absentDatatable(...values) {
            let data = [];
            let url = $("#absent_table_url").val();
            data["url"] = url;
            var shortBy = $("#short_by").val();
            data["value"] = {
                date: $("#date").val(),
                short_by: shortBy ? shortBy : null,
                limit: $("#entries").val(),
                search: $('input[name="search"]').val(),
                department: $("#department_id").val(),
                user_id: $("#__user_id").val() ?? null,
                type: $("#type").val(),
                _token: _token,
            };
            data["column"] = [
                "id",
                "date",
                "name",
                "employee_id",
                "department",
                "designation",
            ];

            data["table_id"] = "absent_table";
            table(data);
        }
        $(".absent_table").length > 0 ? absentDatatable() : "";
        //absent_table end 
</script>
@endsection