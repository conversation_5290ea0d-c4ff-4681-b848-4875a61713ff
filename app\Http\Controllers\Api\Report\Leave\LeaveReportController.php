<?php

namespace App\Http\Controllers\Api\Report\Leave;

use App\Http\Controllers\Controller;
use App\Repositories\Admin\RoleRepository;
use App\Repositories\Hrm\Leave\LeaveRequestRepository;

class LeaveReportController extends Controller
{
    public function __construct(
        protected LeaveRequestRepository $leaveRequestRepository,
        protected RoleRepository $role
    ) {}

    // public function dateSummary(Request $request)
    // {
    //     return $this->service->dateSummary($request);
    // }

    // public function dateSummaryList(Request $request)
    // {
    //     return $this->service->dateSummaryList($request);
    // }

    // public function dateUserLeaveList(Request $request)
    // {
    //     return $this->service->dateUserLeaveList($request);
    // }
}
