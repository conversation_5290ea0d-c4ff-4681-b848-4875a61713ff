@extends('frontend.auth.app')
@section('title', 'Forgot password')
@section('content')
    <div class="form-heading mb-40 text-center">
        <h3 class="title mb-8">{{ __('Change password!!') }}</h3>
    </div>

    <input type="hidden" id="password_change_successfully" value="{{ _trans('frontend.Password change successfully') }}" />
    <form action="#" method="post" id="login"
        class="auth-form d-flex justify-content-center align-items-start flex-column password_reset_form w-100 gap-10">

        @csrf
        <div class="input-field-group">
            <label for="username">{{ _trans('auth.Verification Code') }} <sup>*</sup></label><br>
            <div class="custom-input-field login__field">
                <input type="text" name="code" class="form-control"
                    placeholder="{{ _trans('auth.Verification Code') }}" id="username">
            </div>
            <p class="text-danger cus-error __code small-text"></p>
        </div>
        <div class="input-field-group">
            <label for="email">{{ _trans('auth.Email') }} <sup>*</sup></label><br>
            <div class="custom-input-field login__field">
                <input type="email" name="email" class="form-control" id="email"
                    placeholder="{{ _trans('common.Email') }}" value="{{ Session::get('email') }}" readonly>
            </div>
            <p class="text-danger cus-error __email small-text"></p>
        </div>

        <div class="input-field-group">
            <label for="password">{{ _trans('auth.Password') }} <sup>*</sup></label><br>
            <div class="custom-input-field login__field">
                <input type="password" name="password" id="password" class="form-control"
                    placeholder="{{ _trans('auth.Password') }}">
            </div>
            <p class="text-danger cus-error __password small-text"></p>
        </div>

        <div class="input-field-group">
            <label for="Confirm_p">{{ _trans('auth.Confirm Password') }} <sup>*</sup></label><br>
            <div class="custom-input-field login__field">
                <input id="Confirm_p" type="password" name="password_confirmation" class="form-control"
                    placeholder="{{ _trans('auth.Confirm password') }}">
            </div>
            <p class="text-danger cus-error __password_confirmation small-text"></p>
        </div>
        <button type="button" class="submit-btn mb-3 pv-16 mt-32 mb-20 submit_btn_change">{{ _trans('auth.Change password') }}</button>

        <!-- Back To Sign In -->
        <div class="authenticate-now mb-0">
            <a class="link-text d-flex align-items-center gap-10" href="{{ route('adminLogin') }}"> 
                <div class="icon icon-size-20 text-title">
                    <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none"><path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="1.5" d="M9.57 5.93L3.5 12l6.07 6.07M20.5 12H3.67"></path></svg>
                </div>
            <span> {{ _trans('auth.Back to Sign in') }}</span>
            </a>
        </div>
        <!-- / -->

    </form>

@endsection
@section('script')
    <script src="{{ global_asset('/') }}frontend/assets/jquery.min.js"></script>
    <script src="{{ global_asset('/') }}frontend/assets/bootstrap/bootstrap.min.js"></script>
    @include('backend.partials.message')
    <script src="{{ global_asset('js/toastr.js') }}"></script>
    {!! Toastr::message() !!}
    <script src="{{ global_asset('frontend/js/auth.js') }}"></script>
@endsection
