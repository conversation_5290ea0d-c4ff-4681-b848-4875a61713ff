@extends('backend.layouts.app')
@section('title', @$data['title'])

@section('content')

<div class="ot-card">
    <!-- toolbar table start -->
    <div
        class="table-toolbar d-flex flex-wrap gap-2 flex-xl-row justify-content-center justify-content-xxl-between align-content-center pb-3">
        <div class="align-self-center">
            <div class="d-flex flex-wrap gap-2  flex-lg-row justify-content-center align-content-center">
                <!-- show per page -->
                <div class="align-self-center">
                    <label>
                        <span class="mr-8">{{ _trans('common.Show') }}</span>
                        <select class="form-select d-inline-block" id="entries" onchange="expensePaymentTable()">
                            @include('backend.partials.tableLimit')
                        </select>
                        <span class="ml-8">{{ _trans('common.Entries') }}</span>
                    </label>
                </div>



                <div class="align-self-center d-flex  flex-wrap gap-2">
                    <div class="align-self-center">
                        <button type="button" class="btn-daterange" id="daterange">
                            <span class="icon"><i class="fa-solid fa-calendar-days"></i>
                            </span>
                            <span class="d-none d-xl-inline">{{ _trans('common.Date Range') }}</span>
                        </button>
                        <input type="hidden" id="daterange-input" onchange="expensePaymentTable()">
                    </div>
                    <!-- Designation -->
                    <div class="align-self-center">
                        <div class="dropdown dropdown-designation" data-bs-title="{{ _trans('common.Purpose') }}">
                            <button type="button" class="btn-designation" data-bs-toggle="dropdown"
                                aria-expanded="false" data-bs-auto-close="false">
                                <span class="icon"><i class="fa-solid fa-user-shield"></i></span>

                                <span class="d-none d-xl-inline">{{ _trans('expense.Purpose') }}</span>
                            </button>

                            <div class="dropdown-menu align-self-center ">
                                <select name="category_id" id="category_id" class="form-control pl-2 select2 w-100"
                                    onchange="expensePaymentTable()">
                                    <option value=" " selected>
                                        {{ _trans('common.Select Purpose') }}</option>
                                    @foreach ($data['purposes'] as $category)
                                    <option value="{{ $category->id }}">{{ @$category->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="align-self-center">
                        <div class="dropdown dropdown-designation" data-bs-title="{{ _trans('common.Employee') }}">
                            <button type="button" class="btn-designation" data-bs-toggle="dropdown"
                                aria-expanded="false" data-bs-auto-close="false">
                                <span class="icon"><i class="fa-solid fa-users"></i></span>
                                <span class="d-none d-xl-inline">{{ _trans('common.Employee') }}</span>
                            </button>

                            <div class="dropdown-menu  align-self-center ">
                                <select name="user_id" class="form-control select2" id="user_id"
                                    onchange="expensePaymentTable()">
                                    <option value="0" disabled selected>
                                        {{ _trans('common.Choose Employee') }}</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <!-- search -->
                    <div class="align-self-center">
                        <div class="search-box d-flex">
                            <input class="form-control" placeholder="{{ _trans('common.Search') }}" name="search"
                                onkeyup="expensePaymentTable()" autocomplete="off">
                            <span class="icon"><i class="fa-solid fa-magnifying-glass"></i></span>
                        </div>
                    </div>
                </div>

            </div>
        </div>
        <!-- export -->
        @include('backend.partials.buttons')
    </div>
    <!-- toolbar table end -->
    <!--  table start -->
    <div class="table-responsive min-height-300">
        @include('backend.partials.table')
    </div>
    <!--  table end -->
</div>

@endsection
@section('script')
@include('backend.partials.table_js')
@endsection