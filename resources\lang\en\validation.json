{"Name is required": "Name is required", "Status is required": "Status is required", "Pricing Plan is required": "Pricing Plan is required", "Expiry Date is required": "Expiry Date is required", "Expiry Date must be a date": "Expiry Date must be a date", "Name may not be greater than 255 characters": "Name may not be greater than 255 characters", "Company Name is required": "Company Name is required", "Company Name may not be greater than 255 characters": "Company Name may not be greater than 255 characters", "Company Name already exists": "Company Name already exists", "Phone is required": "Phone is required", "Phone may not be greater than 255 characters": "Phone may not be greater than 255 characters", "Phone already exists": "Phone already exists", "Invalid phone number format": "Invalid phone number format", "Please enter a valid phone number": "Please enter a valid phone number", "Email is required": "Email is required", "Email must be an email": "Email must be an email", "Email may not be greater than 255 characters": "Email may not be greater than 255 characters", "Email already exists": "Email already exists", "Trade Licence Number is required": "Trade Licence Number is required", "Trade Licence Number may not be greater than 255 characters": "Trade Licence Number may not be greater than 255 characters", "Trade Licence Number already exists": "Trade Licence Number already exists", "Subdomain is required": "Subdomain is required", "Subdomain may not be greater than 255 characters": "Subdomain may not be greater than 255 characters", "Subdomain already exists": "Subdomain already exists", "Invalid subdomain format": "Invalid subdomain format", "Only letters (both uppercase and lowercase), numbers, and hyphens are allowed": "Only letters (both uppercase and lowercase), numbers, and hyphens are allowed", "Total Employee is required": "Total Employee is required", "Business Type is required": "Business Type is required", "Business Type may not be greater than 255 characters": "Business Type may not be greater than 255 characters", "Country is required": "Country is required", "Payment Gateway is required": "Payment Gateway is required", "Google Recaptcha is required": "Google Recaptcha is required", "Title is required": "Title is required", "name is not 255 characters": "name is not 255 characters", "Is weekend is required": "Is weekend is required", "Days is required": "Days is required", "Days is not a number": "Days is not a number", "Department is required": "Department is required", "Employee is required": "Employee is required", "Type is required": "Type is required", "User name is required": "User name is required", "User phone is required": "User phone is required", "User phone may not be greater than 255 characters": "User phone may not be greater than 255 characters", "User phone already exists": "User phone already exists", "Invalid user phone number format": "Invalid user phone number format", "User email is required": "User email is required", "User email must be an email": "User email must be an email", "User email may not be greater than 255 characters": "User email may not be greater than 255 characters", "User email already exists": "User email already exists", "Password field is required": "Password field is required", "Department field is required": "Department field is required", "Designation field is required": "Designation field is required", "Company phone is required": "Company phone is required", "Company phone may not be greater than 255 characters": "Company phone may not be greater than 255 characters", "Company phone already exists": "Company phone already exists", "Invalid company phone number format": "Invalid company phone number format", "Company email is required": "Company email is required", "Company email must be an email": "Company email must be an email", "Company email may not be greater than 255 characters": "Company email may not be greater than 255 characters", "Company email already exists": "Company email already exists", "Name may not be greater than 100 characters": "Name may not be greater than 100 characters", "Name is not a valid string": "Name is not a valid string", "Name may not be greater than 191 characters": "Name may not be greater than 191 characters", "Progress is required": "Progress is required", "Client is required": "Client is required", "Billing type is required": "Billing type is required", "Estimated hour is required": "Estimated hour is required", "Start date is required": "Start date is required", "End date is required": "End date is required", "Priority is required": "Priority is required", "Description is required": "Description is required", "Title  is maximum 255 character": "Title  is maximum 255 character", "Date is required": "Date is required", "Remarks is maximum 600 character": "Remarks is maximum 600 character", "Rating is required": "Rating is required", "UserId is required": "UserId is required", "Month is required": "Month is required", "AdvanceType is required": "AdvanceType is required", "Amount is required": "Amount is required", "RecoveryMode is required": "RecoveryMode is required", "RecoveryCycle is required": "RecoveryCycle is required", "InstallmentAmount is required": "InstallmentAmount is required", "RecoverFrom is required": "RecoverFrom is required", "Reason is required": "Reason is required", "Subject is required": "Subject is required", "Subject is maximum 255 characters": "Subject is maximum 255 characters", "Category is required": "Category is required", "Attachment is required": "Attachment is required", "Attachment must be a file of type: jpeg, png, jpg, pdf, doc": "Attachment must be a file of type: jpeg, png, jpg, pdf, doc", "Ref must be less than 191 characters": "Ref must be less than 191 characters", "Response deadline is required": "Response deadline is required", "Response deadline must be on or after the date": "Response deadline must be on or after the date", "Attachment is not supported": "Attachment is not supported", "Description is not more than 800 characters": "Description is not more than 800 characters", "Location is required": "Location is required", "Start time is required": "Start time is required", "EndTime is required": "EndTime is required", "Title is too long than 191 character": "Title is too long than 191 character", "Location is too long than 191 character": "Location is too long than 191 character", "Date schedule is required": "Date schedule is required", "End time is required": "End time is required", "Appointment with is required": "Appointment with is required", "User Id is required": "User Id is required", "Award Type is required": "Award Type is required", "Gift is required": "Gift is required", "Award Information is required": "Award Information is required", "Attachment must be a file of type: jpeg, png, jpg": "Attachment must be a file of type: jpeg, png, jpg", "Title may not be greater than 255 characters": "Title may not be greater than 255 characters", "Content is required": "Content is required", "Content may not be greater than 1000 characters": "Content may not be greater than 1000 characters", "Start Date is required": "Start Date is required", "End Date is required": "End Date is required", "Document type is required": "Document type is required", "Document type not greater than 255": "Document type not greater than 255", "Document type is already in use": "Document type is already in use", "Name is not more than 255 characters": "Name is not more than 255 characters", "Upper Roles is required": "Upper Roles is required", "Permission is required": "Permission is required", "User ID is required": "User ID is required", "Award type is required": "Award type is required", "Award information is required": "Award information is required", "Attachment must be a file of type: jpeg, png, jpg, gif": "Attachment must be a file of type: jpeg, png, jpg, gif", "Account is required": "Account is required", "Payment Method is required": "Payment Method is required", "Provide Right Email address": "Provide Right Email address", "Provide Unique Email address": "Provide Unique Email address", "Old password is required": "Old password is required", "Email is not allowed more than 255 characters": "Email is not allowed more than 255 characters", "Password is required": "Password is required", "Password required minimum 8 character": "Password required minimum 8 character", "Password & confirmation password must be same": "Password & confirmation password must be same", "Password confirmation is required": "Password confirmation is required", "Password & old password are same": "Password & old password are same", "Address is required": "Address is required", "Email must be a valid email address": "Email must be a valid email address", "Email must be unique": "Email must be unique", "Phone must be unique": "Phone must be unique", "Gender is required": "Gender is required", "Address must not exceed 255 characters": "Address must not exceed 255 characters", "Birth date must be a valid date": "Birth date must be a valid date", "Joining date is required": "Joining date is required", "Department must exist": "Department must exist", "Designation is required": "Designation is required", "Designation must exist": "Designation must exist", "Duty schedule is required": "Duty schedule is required", "Duty schedule must exist": "Duty schedule must exist", "Role is required": "Role is required", "Role must exist": "Role must exist", "Basic salary is required": "Basic salary is required", "Basic salary must be numeric": "Basic salary must be numeric", "Attendance method is required": "Attendance method is required", "Invalid attendance method selected": "Invalid attendance method selected", "Password type is required": "Password type is required", "Invalid password type selected": "Invalid password type selected", "Password must be at least 8 characters": "Password must be at least 8 characters", "Weekends are required": "Weekends are required", "Invalid weekend selected": "Invalid weekend selected", "Holidays are required": "Holidays are required", "Invalid holiday selected": "Invalid holiday selected", "Avatar must be an image": "Avatar must be an image", "Avatar must be of type jpg, jpeg, png, webp": "Avatar must be of type jpg, jpeg, png, webp", "Avatar size must not exceed 2048 KB": "Avatar size must not exceed 2048 KB", "Department or employee is required": "Department or employee is required", "Employee or department is required": "Employee or department is required", "Month or year is required": "Month or year is required", "Year or month is required": "Year or month is required", "The title is required": "The title is required", "The title may not be greater than 255 characters": "The title may not be greater than 255 characters", "The title has already been taken": "The title has already been taken", "The threshold minutes are required": "The threshold minutes are required", "The threshold minutes must be an integer": "The threshold minutes must be an integer", "The threshold days must be an integer": "The threshold days must be an integer", "The deduction days are required": "The deduction days are required", "The deduction days must be an integer": "The deduction days must be an integer", "The description must be a string": "The description must be a string", "The description may not be greater than 255 characters": "The description may not be greater than 255 characters", "The selected status is invalid": "The selected status is invalid", "The date is required": "The date is required", "The date must be a valid date": "The date must be a valid date", "The user is required": "The user is required", "The user ID must be an integer": "The user ID must be an integer", "The selected user does not exist": "The selected user does not exist", "The tardy rule is required": "The tardy rule is required", "The tardy rule ID must be an integer": "The tardy rule ID must be an integer", "The selected tardy rule does not exist": "The selected tardy rule does not exist", "The tardy type is required": "The tardy type is required", "The tardy type must be a string": "The tardy type must be a string", "The tardy type must be either Check In or Check Out": "The tardy type must be either Check In or Check Out", "The tardy minutes are required": "The tardy minutes are required", "The tardy minutes must be an integer": "The tardy minutes must be an integer", "The tardy minutes must be at least 1": "The tardy minutes must be at least 1", "The emergency status must be 0 or 1": "The emergency status must be 0 or 1", "The notified status must be 0 or 1": "The notified status must be 0 or 1", "The reason must be a string": "The reason must be a string", "The reason must not exceed 255 characters": "The reason must not exceed 255 characters", "The reason is required": "The reason is required", "The reason may not exceed 255 characters": "The reason may not exceed 255 characters", "The attachment is required": "The attachment is required", "The attachment must be a file": "The attachment must be a file", "The attachment must be a file of type: pdf, png, jpg, or jpeg": "The attachment must be a file of type: pdf, png, jpg, or jpeg", "The attachment may not be larger than 10 MB": "The attachment may not be larger than 10 MB", "The deduction days must be a number": "The deduction days must be a number", "The deduction days must be a valid decimal value": "The deduction days must be a valid decimal value", "Deduction Appeal is Required": "Deduction Appeal is Required", "Deduction Appeal must not exceed 2000 characters": "Deduction Appeal must not exceed 2000 characters", "Invalid file type": "Invalid file type", "File size must not exceed 2MB": "File size must not exceed 2MB", "Start At is required": "Start At is required", "Start At must be a valid date": "Start At must be a valid date", "Start At must be before End At": "Start At must be before End At", "End At is required": "End At is required", "End At must be a valid date": "End At must be a valid date", "End At must be after Start At": "End At must be after Start At", "Target Type is required": "Target Type is required", "Invalid Target Type selected": "Invalid Target Type selected", "Distance is required when Target Type is Location Wise": "Distance is required when Target Type is Location Wise", "Distance must be a numeric value": "Distance must be a numeric value", "Location is required when Target Type is Location Wise": "Location is required when Target Type is Location Wise", "The group field is required": "The group field is required", "The type field is required": "The type field is required", "The group and type must be unique": "The group and type must be unique", "Machine Name is required": "Machine Name is required", "Origin is required": "Origin is required", "Installation Date is required": "Installation Date is required", "Date range or year is required": "Date range or year is required", "The email  is already associated with an existing or previously deleted account": "The email  is already associated with an existing or previously deleted account", "Status must be a valid selection": "Status must be a valid selection", "Due date is required for compliance items": "Due date is required for compliance items", "File is required for document type": "File is required for document type", "File must be a document (PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX)": "File must be a document (PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX)", "File size cannot exceed 10MB": "File size cannot exceed 10MB"}