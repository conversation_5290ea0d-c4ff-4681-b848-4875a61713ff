@extends('backend.layouts.app')
@section('title', @$data['title'])

@section('content')
<div class="div">
    <div class="ot-card">
        <div class="row">
            <div class="col-md-12">
                <form method="POST" action="{{ route('appointment.update', $data['appointment']->id) }}">
                    @csrf

                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group mb-3">
                                <label class="form-label" for="name">{{ _trans('common.Title') }} <span
                                        class="text-danger">*</span></label>
                                <input type="text" name="title" class="form-control ot-form-control ot-input"
                                    placeholder="{{ _trans('common.Enter Title') }}"
                                    value="{{ old('title', $data['appointment']->title ?? '') }}" required>
                                @if ($errors->has('title'))
                                <div class="error">{{ $errors->first('title') }}</div>
                                @endif
                            </div>
                        </div>

                        <div class="col-md-12">
                            <div class="form-group mb-3">
                                <label class="form-label">{{ _trans('common.Description') }} <span
                                        class="text-danger">*</span></label>
                                <textarea name="description" class="form-control mt-0 ot-input"
                                    placeholder="{{ _trans('common.Enter Description') }}" rows="6"
                                    required>{{ old('description', $data['appointment']->description ?? '') }}</textarea>
                                @if ($errors->has('description'))
                                <div class="error">{{ $errors->first('description') }}</div>
                                @endif
                            </div>
                        </div>

                        <div class="col-md-12">
                            <div class="form-group mb-3">
                                <label class="form-label" for="name">{{ _trans('common.Location') }} <span
                                        class="text-danger">*</span></label>
                                <input type="text" name="location" class="form-control ot-form-control ot-input"
                                    placeholder="{{ _trans('common.location') }}"
                                    value="{{ old('location', $data['appointment']->location ?? '') }}" required>
                                @if ($errors->has('location'))
                                <div class="error">{{ $errors->first('location') }}</div>
                                @endif
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label class="form-label" for="date">{{ _trans('common.Date Schedule') }} <span
                                        class="text-danger">*</span></label>
                                <input type="date" name="date" id="date" class="form-control ot-form-control ot-input"
                                    value="{{ old('date', $data['appointment']->date ?? '') }}" required>
                                @if ($errors->has('date'))
                                <div class="error">{{ $errors->first('date') }}</div>
                                @endif
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label class="form-label">{{ _trans('common.Appointment With') }}</label>
                                <select name="appoinment_with" class="form-control select2" id="user_id">
                                    @if (isset($users))
                                    @foreach ($users as $user)
                                    <option value="{{ $user->id }}" {{ old('appoinment_with', $data['appointment']->
                                        appoinment_with) == $user->id ? 'selected' : '' }}>
                                        {{ $user->name }}
                                    </option>
                                    @endforeach
                                    @endif
                                </select>
                                @if ($errors->has('appoinment_with'))
                                <div class="error">{{ $errors->first('appoinment_with') }}</div>
                                @endif
                            </div>
                        </div>

                        <div class="col-lg-6">
                            <div class="form-group mb-3">
                                <label for="#" class="form-label">{{ _trans('common.Start Time') }}<span
                                        class="text-danger">*</span></label>
                                <input type="time" class="form-control ot-form-control ot-input"
                                    name="appoinment_start_at"
                                    value="{{ old('appoinment_start_at', $data['appointment']->appoinment_start_at ?? '') }}"
                                    required>
                                @if ($errors->has('appoinment_start_at'))
                                <div class="error">{{ $errors->first('appoinment_start_at') }}</div>
                                @endif
                            </div>
                        </div>

                        <div class="col-lg-6">
                            <div class="form-group mb-3">
                                <label for="#" class="form-label">{{ _trans('common.End Time') }} <span
                                        class="text-danger">*</span></label>
                                <input type="time" class="form-control ot-form-control ot-input"
                                    name="appoinment_end_at"
                                    value="{{ old('appoinment_end_at', $data['appointment']->appoinment_end_at ?? '') }}"
                                    required>
                                @if ($errors->has('appoinment_end_at'))
                                <div class="error">{{ $errors->first('appoinment_end_at') }}</div>
                                @endif
                            </div>
                        </div>

                        <div class="col-md-12">
                            <div class="float-right d-flex justify-content-end">
                                <button type="submit" class="btn-primary-fill action-btn">{{ _trans('common.Save')
                                    }}</button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <input type="hidden" id="get_user_url" value="{{ route('user.getUser') }}">
</div>
@endsection